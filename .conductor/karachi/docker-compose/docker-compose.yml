version: '3.8'

services:
  server:
    build:
      context: ..
      dockerfile: Dockerfile
      target: server-image
    image: tandian-server
    command: ["/server"]
    container_name: tandian-server
    ports:
      - "12345:8080" # Exposes server port 8080 to the host. Change if your server uses a different port.
    networks:
      - tandian-net  
    depends_on:
      - redis
    env_file:
      - ./.env
    environment:
      - REDIS_ADDR=redis:6379

  client:
    build:
      context: ..
      dockerfile: Dockerfile
      target: server-image
    image: tandian-client
    command: ["/client"]
    container_name: tandian-client
    networks:
      - tandian-net
    depends_on:
      - server # Ensures server starts before client
      - redis  
    env_file:
      - ./.env
    environment:
      - REDIS_ADDR=redis:6379

  redis:
    image: "redis:8.0.2-alpine"
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    networks:
      - tandian-net
    env_file:
      - ./.env
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s

networks:
  tandian-net:
    driver: bridge

volumes:
  redis-data:
