app_env: "development" # development or production
redis:
  addr: "159.75.227.214:6379"
  password: "11e02683a13e59ee70aa7cd7f786b80b"
  db: 0
asynq:
  redis_addr: "159.75.227.214:6379"
  redis_password: "11e02683a13e59ee70aa7cd7f786b80b"
  redis_db: 0
wechat_pay:
  app_id: wxf77ccd1007594f39
  mch_id: 1656753137
  mch_certificate_serial_number: 501074248E70FF853CFC617D79488B3CA1A99A39
  mch_api_v3_key: API_V3_KEY
  mch_api_v2_key: API_V2_KEY
  private_key_path: ./apiclient_key.pem
  use_dynamic_task_manager: true
monitoring_port: 8080
app_server_port: 8182
jwt_secret: 5mqbXF+60+TP+8ZWqUc57kEwpmZ/bktJrpxD5F5IAgQ=
log:
  level: "info"
  modules:
    redis: "warn"
    server: "info"
    util: "error"
    tasks:note: "info"
    tasks:payment: "info"
    tasks:user: "debug"
    tasks:cloudbase_token: "error"
    tasks:wechat_token: "error"
    tasks:wecom_token: "error"
    tasks:transfer_bill: "info"
    tasks:flagsmith: "info"
flagsmith:
  enabled: true # Set to true to enable Flagsmith integration
  environment_key: "nLfFNWnZGraHQVCQdoQ65K" # Get from Flagsmith dashboard
  base_url: "https://flagsmith.dagexian.com/api/v1/" # Optional: for self-hosted Flagsmith instances (default: https://edge.api.flagsmith.com/api/v1/)
  local_eval: false # Set to true for local evaluation mode (requires server-side SDK key)
wecom:
  corp_id: "" # WeCom corp ID
  agent_id: 0 # WeCom agent ID
  secret: "" # WeCom secret
  callback_url: "" # WeCom callback URL
  webhook_key: "" # WeCom webhook key for sending messages to external groups
