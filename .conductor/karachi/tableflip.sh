#!/bin/bash
set -e

echo "优雅升级前"
curl -s http://localhost:8182/version
echo
echo

echo "--> Building application..."
if ! just build app; then
    echo "Build failed. Aborting upgrade."
    exit 1
fi
echo "Build successful."

PID_FILE="tmp/app.pid"

if [ ! -f "$PID_FILE" ]; then
    echo "PID file not found at $PID_FILE!"
    exit 1
fi

PID=$(cat $PID_FILE)
echo "--> Found PID $PID, sending SIGHUP for graceful upgrade..."
kill -SIGHUP $PID

echo "--> Waiting for upgrade to complete..."
sleep 3

echo
echo "优雅升级后"
curl -s http://localhost:8182/version
echo

curl -s http://localhost:8182/livez
echo
