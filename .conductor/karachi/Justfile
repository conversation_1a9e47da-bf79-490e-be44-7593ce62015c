# Default task to run when no command is specified
default: build

# Build the main application binary
b: build
build *args:
    @if [ -z "{{args}}" ]; then \
        echo "Building all applications..."; \
        go build -v -ldflags="-X main.Commit=$(git rev-parse HEAD)" -o ./bin/app tandian-server/cmd/app; \
        go build -v -o ./bin/cli tandian-server/cmd/cli; \
        go build -v -o ./bin/client tandian-server/cmd/client; \
        go build -v -o ./bin/server tandian-server/cmd/server; \
    else \
        for arg in {{args}}; do \
            echo "Building $arg..."; \
            go build -v -o ./bin/$arg tandian-server/cmd/$arg; \
        done \
    fi

# Run the application with live reload using air
run *args:
    @echo "Starting application with go run ..."
    @if [ -z "{{args}}" ]; then \
        echo "run server,client,cli,app"; \
    else \
        for arg in {{args}}; do \
            echo "Starting $arg..."; \
            go run -v tandian-server/cmd/$arg; \
        done \
    fi

# Run all tests
test:
    @echo "Running tests..."
    go test -v ./...

# Lint the codebase using golangci-lint
lint:
    @echo "Linting code..."
    golangci-lint run --fix

# Tidy go modules
tidy:
    @echo "Tidying go modules..."
    go mod tidy

# Build the Docker image
docker-build:
    @echo "Building Docker image..."
    docker build -t tandian-server .

# Start services with Docker Compose
docker-up:
    @echo "Starting services with Docker Compose..."
    docker-compose -f docker-compose/docker-compose.yml up -d

# Stop services with Docker Compose
docker-down:
    @echo "Stopping services with Docker Compose..."
    docker-compose -f docker-compose/docker-compose.yml down

# Run the sync script
sync:
    @echo "Running sync script..."
    sh ./sync.sh

# Clean up build artifacts
clean:
    @echo "Cleaning up..."
    rm -f ./tmp/app
