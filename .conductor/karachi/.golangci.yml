# golangci-lint configuration
# Optimized for less noise and practical use

version: 2 # Required for golangci-lint v2.x

run:
  timeout: 2m  # Strict timeout for CI - fail fast if analysis takes too long
  tests: false  # Don't run on test files by default
  issues-exit-code: 1  # Exit with non-zero code if issues are found (default, but explicit for CI)
  build-tags: []  # Ensure consistent behavior across environments

# Formatters configuration
formatters:
  enable:
    - gci       # Check if code and import statements are formatted, with additional rules
    - gofmt     # Check if the code is formatted according to 'gofmt' command
    - gofumpt   # Check if code and import statements are formatted, with additional rules
    - goimports # Checks if the code and import statements are formatted according to the 'goimports' command
    - golines   # Checks if code is formatted, and fixes long lines
    - swaggo    # Check if swaggo comments are formatted

  settings:
    gci:
      # Organize imports into sections
      sections:
        - standard  # Standard library imports
        - default   # Everything else
        - prefix(tandian-server)  # Local module imports
      
    gofumpt:
      # Use extra rules
      extra-rules: true
      
    golines:
      # Maximum line length
      max-len: 120
      
    goimports:
      # Put local imports after external packages
      local-prefixes: tandian-server

linters:
  # Don't use disable-all in v2, instead explicitly list what to enable
  enable:
    - govet
    - ineffassign
    - staticcheck
    - misspell
    - tagalign
    - musttag
    - whitespace
    - wastedassign
    - reassign
    - makezero
    - ireturn
    - godot
    - goheader
    - funcorder
    - errchkjson
    - canonicalheader
    # - forbidigo
    # - revive
    # - gosec
    - gocritic
    # - gochecknoinits
    # - gocognit
    # - cyclop
    # - depguard
    # - dupl
    # - errcheck

  disable:
    - errcheck
    - unused
    - unparam

linters-settings:
  govet:
    enable:
      - shadow
  
  staticcheck:
    checks: 
      - all
      - "-ST1000"
      - "-ST1003" 
      - "-SA9003"  # Disable empty branch check

issues:
  # Exclude specific error messages
  exclude:
    - "SA9003:"
  
  # Exclude common false positives
  exclude-rules:
    # Ignore SA9003 empty branch errors
    - text: "SA9003"
      linters:
        - staticcheck
    
    # Ignore error checks in defer statements
    - source: "defer"
      linters:
        - errcheck
    
    # Ignore test files for certain linters
    - path: _test\.go
      linters:
        - errcheck
        - ineffassign
    
    # Ignore generated files
    - path: "generated"
      linters:
        - all
    
    # Ignore vendor directory
    - path: vendor/
      linters:
        - all
  
  # Maximum issues count per one linter
  max-issues-per-linter: 50
  
  # Maximum count of issues with the same text
  max-same-issues: 3

  # Show only new issues in CI
  new: false