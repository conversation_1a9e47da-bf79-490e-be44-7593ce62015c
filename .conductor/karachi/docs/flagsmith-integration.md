# Flagsmith Integration Guide

## Overview

This document describes the Flagsmith feature flag and user segmentation integration for the Tandian Server project. Flagsmith enables dynamic feature toggling, A/B testing, and user segmentation based on traits.

## Setup

### 1. Configuration

Add Flagsmith configuration to your `config.yaml`:

```yaml
flagsmith:
  enabled: true
  environment_key: "YOUR_FLAGSMITH_ENVIRONMENT_KEY"
  base_url: "" # Optional: for self-hosted instances
  local_eval: false # Set to true for local evaluation mode
```

### 2. Initialize in Main Application

```go
import "tandian-server/internal/flagsmith"

func main() {
    // Initialize Flagsmith
    if err := flagsmith.InitializeFlagsmith(logger); err != nil {
        log.Fatal("Failed to initialize Flagsmith:", err)
    }
}
```

## User Traits

The integration tracks the following user traits automatically:

### Order-Related Traits
- `total_orders` (int): Total number of orders placed
- `finished_ten_or_more_orders` (bool): Whether user has completed 10+ orders
- `last_order_date` (timestamp): Date of most recent order
- `total_spent_yuan` (float): Total amount spent in Yuan
- `average_order_value_yuan` (float): Average order value
- `user_type` (string): "new", "regular", or "vip"

### User Behavior Traits
- `has_uploaded_notes` (bool): Whether user has uploaded notes
- `notes_approved_count` (int): Number of approved notes
- `notes_rejected_count` (int): Number of rejected notes
- `note_approval_rate` (float): Approval rate percentage

### Campaign Participation
- `help_campaigns_initiated` (int): Campaigns started by user
- `help_campaigns_completed` (int): Campaigns completed
- `help_campaigns_assisted` (int): Campaigns user helped with

### Coupon Usage
- `coupons_used` (int): Total coupons redeemed
- `coupons_available` (int): Current available coupons

### Account Info
- `registration_date` (timestamp): Account creation date
- `days_since_registration` (int): Days since registration
- `preferred_payment_method` (string): "wechat" or "balance"

## Usage Examples

### 1. Update Traits After Order Completion

```go
orderService := flagsmith.NewOrderService()
err := orderService.OnOrderCompleted(ctx, userID, orderID, orderAmount)
```

### 2. Check Feature Eligibility

```go
traitService := flagsmith.NewTraitService()
isVIP, err := traitService.CheckFeatureForUser(ctx, userID, "vip_features")
if isVIP {
    // Enable VIP features
}
```

### 3. Get Remote Configuration

```go
discountRate, err := traitService.GetFeatureValueForUser(ctx, userID, "special_discount_rate")
// Use discountRate in calculations
```

### 4. A/B Testing

```go
variant, err := traitService.GetFeatureValueForUser(ctx, userID, "checkout_flow_variant")
switch variant {
case "variant_a":
    // Show checkout flow A
case "variant_b":
    // Show checkout flow B
default:
    // Show default flow
}
```

## Recommended Segments in Flagsmith Dashboard

### 1. VIP Users
- **Rule**: `finished_ten_or_more_orders = true`
- **Use Cases**: 
  - Exclusive discounts
  - Early access to new features
  - Premium customer support

### 2. New Users
- **Rule**: `total_orders < 3`
- **Use Cases**:
  - Onboarding tutorials
  - First-time buyer discounts
  - Welcome campaigns

### 3. High Value Customers
- **Rule**: `total_spent_yuan > 1000`
- **Use Cases**:
  - Premium features
  - Special rewards
  - Personalized offers

### 4. Active Community Members
- **Rule**: `help_campaigns_assisted > 5`
- **Use Cases**:
  - Community badges
  - Social features
  - Referral bonuses

### 5. Content Contributors
- **Rule**: `notes_approved_count > 10`
- **Use Cases**:
  - Auto-approval privileges
  - Content creator rewards
  - Review capabilities

### 6. Recent Shoppers
- **Rule**: `last_order_date > (now - 7 days)`
- **Use Cases**:
  - Time-sensitive promotions
  - Reorder reminders
  - Flash sales

## Recommended Feature Flags

### 1. Feature Rollouts
- `new_checkout_flow` (Boolean): Gradual rollout of new checkout
- `enable_express_checkout` (Boolean): One-click checkout for VIPs
- `coupon_stacking_enabled` (Boolean): Allow multiple coupon usage

### 2. Remote Configuration
- `special_discount_rate` (Number): Dynamic discount percentages
- `max_help_campaigns_per_day` (Number): Campaign creation limits
- `note_auto_approval_threshold` (Number): Auto-approve trusted users
- `referral_bonus_amount` (Number): Dynamic referral rewards

### 3. A/B Testing
- `checkout_flow_variant` (String): Test different checkout flows
- `recommendation_algorithm` (String): Test recommendation engines
- `ui_theme_variant` (String): Test different UI themes

## Integration Points

### Order Handler
```go
// In cmd/app/orders_handler.go
func HandleOrderComplete(ctx context.Context, orderID string) {
    orderService := flagsmith.NewOrderService()
    orderService.OnOrderCompleted(ctx, userID, orderID, amount)
}
```

### Campaign Handler
```go
// In cmd/app/campaigns_handler.go
func HandleCampaignComplete(ctx context.Context, campaignID string) {
    traitService := flagsmith.NewTraitService()
    traitService.UpdateCampaignTraits(ctx, userID, initiated, completed, assisted)
}
```

### Payment Handler
```go
// In cmd/app/payments_handler.go
func HandlePaymentSuccess(ctx context.Context, paymentID string) {
    // Check for payment method preferences
    traitService := flagsmith.NewTraitService()
    traits := map[string]interface{}{
        "preferred_payment_method": paymentMethod,
    }
    client.GetIdentityFlags(ctx, userID, traits)
}
```

## Best Practices

1. **Trait Naming**: Use descriptive, snake_case trait names
2. **Trait Types**: Use appropriate data types (bool, int, float, string)
3. **Update Frequency**: Batch trait updates when possible
4. **Error Handling**: Always handle Flagsmith errors gracefully
5. **Default Values**: Provide sensible defaults when flags are unavailable
6. **Testing**: Test with Flagsmith disabled to ensure fallback behavior

## Monitoring

Monitor the following metrics:
- Trait update success/failure rates
- Feature flag evaluation latency
- Segment membership changes
- A/B test conversion rates

## Security Considerations

1. Never expose sensitive user data in traits
2. Use server-side SDK keys for backend services
3. Enable local evaluation for performance-critical paths
4. Rotate API keys regularly
5. Audit trait updates and flag changes

## Troubleshooting

### Common Issues

1. **Traits not updating**: Check environment key and network connectivity
2. **Features not enabled**: Verify segment rules and user traits
3. **Performance issues**: Consider enabling local evaluation mode
4. **Missing flags**: Ensure flags are created in Flagsmith dashboard

### Debug Logging

Enable debug logging for Flagsmith operations:
```yaml
log:
  modules:
    flagsmith: "debug"
```

## Migration Guide

When enabling Flagsmith for existing users:

1. Run batch job to populate initial traits
2. Start with feature flags in "off" state
3. Gradually enable features per segment
4. Monitor user experience metrics
5. Roll back if issues detected

## Support

For Flagsmith-specific issues:
- Documentation: https://docs.flagsmith.com
- Community: https://flagsmith.com/community
- Support: <EMAIL>

For integration issues:
- Check internal logs
- Review trait update logic
- Verify configuration settings