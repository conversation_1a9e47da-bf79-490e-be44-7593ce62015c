# CLI 文档

## 构建

```bash
go build -o bin/cli cmd/cli/main.go
```

## 命令列表

### 任务管理

```bash
# 列出任务类型
./bin/cli list

# 添加任务
./bin/cli enqueue --type=任务类型 --param=参数 --now  # 立即执行
./bin/cli enqueue --type=任务类型 --param=参数 --in=5m # 5分钟后执行
./bin/cli task enqueue -t update_help_campaign_stats -in 5s
```

### 订单管理

```bash
# 查询订单
./bin/cli queryOrder --order_no=订单号
./bin/cli queryOrder --transaction_id=微信交易号

# 退款
./bin/cli refundOrder --order=订单号 --amount=金额(分) --reason=退款原因

# 核销订单
./bin/cli verifyOrder --order_no=订单号
```

### 转账管理

```bash
# 查询转账
./bin/cli queryTransferBill --out_bill_no=商户单号

# 取消转账
./bin/cli cancelTransferBill --out_bill_no=商户单号
```

### 账单和余额

```bash
# 查余额
./bin/cli balance --account-type=basic

# 下载账单
./bin/cli downloadBill --date=2024-01-01 --type=trade --output-dir=./bills
```

### 优惠券管理

```bash
# 查询优惠券
./bin/cli coupon query --coupon_code=券码 --openid=用户ID

# 查询批次
./bin/cli coupon query-stock --stock_id=批次ID

# 核销优惠券
./bin/cli coupon use --stock_id=批次ID --coupon_code=券码 --openid=用户ID

# 返还优惠券
./bin/cli coupon return --stock_id=批次ID --coupon_code=券码

# 作废优惠券
./bin/cli coupon deactivate --stock_id=批次ID --coupon_code=券码
```