# 订单状态流程图

```mermaid
graph TD
    Start([创建订单]) --> Pending[待支付<br/>Status: 0]
    
    Pending -->|支付成功| Paid[待核销<br/>Status: 10]
    Pending -->|用户取消| Canceled[已取消<br/>Status: 81]
    Pending -->|超时未支付| Canceled
    
    Paid -->|核销| Verified[已核销<br/>Status: 20<br/>笔记变为待上传状态<br/>如果用了优惠券，核销优惠券]
    Paid -->|超时未核销| Expired[已过期<br/>Status: 11]
    Paid -->|退款| Refunded[已退款<br/>Status: 40<br/>如果用了优惠券，就退回优惠券]
    
    Expired -->|退款| Refunded
    
    Verified -->|上传笔记| UnderReview[笔记审核中<br/>Status: 30]
    
    UnderReview -->|审核通过| AuditPass[审核通过<br/>Status: 31]
    UnderReview -->|审核驳回| AuditFail[审核驳回<br/>Status: 32]
    
    AuditFail -->|重新上传| UnderReview
    
    AuditPass --> End([订单完成])
    Refunded --> End
    Canceled --> End
    
    style Pending fill:#fff5cc
    style Paid fill:#d4e1f5
    style Verified fill:#d4e1f5
    style UnderReview fill:#fce4d6
    style AuditPass fill:#d5e8d4
    style AuditFail fill:#f8cecc
    style Expired fill:#f8cecc
    style Refunded fill:#e1d5e7
    style Canceled fill:#e1d5e7
```

## 状态说明

| 状态码 | 状态名称 | 说明 |
|-------|---------|------|
| 0 | 待支付 | 订单创建后的初始状态 |
| 10 | 待核销 | 用户支付成功，等待商家核销 |
| 20 | 已核销 | 商家已核销，用户需上传探店笔记 |
| 11 | 已过期 | 超时未核销，可申请退款 |
| 30 | 笔记审核中 | 用户已上传笔记，等待审核 |
| 31 | 审核通过 | 笔记审核通过，订单完成 |
| 32 | 审核驳回 | 笔记审核未通过，需重新上传 |
| 40 | 已退款 | 订单已取消并退款 |
| 81 | 已取消 | 未支付前用户主动取消 |

## 主要流转路径

1. **正常流程**: 创建 → 待支付 → 待核销 → 已核销 → 笔记审核中 → 审核通过
2. **取消流程**: 待支付 → 已取消
3. **退款流程**: 待核销/已过期 → 已退款
4. **过期流程**: 待核销 → 已过期 → 已退款
5. **驳回重传**: 笔记审核中 → 审核驳回 → 笔记审核中