# 名片审核流程图

```mermaid
graph TD
    Start([用户提交名片]) --> Pending[待审核<br/>Status: 0]
    
    Pending --> Review{审核员审核}
    
    Review -->|通过| Approved[审核通过<br/>Status: 1]
    Review -->|不通过| Rejected[审核驳回<br/>Status: 2]
    
    Rejected -->|重新提交| Pending
    
    Approved --> End([完成认证])
    
    style Pending fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#fff
    style Approved fill:#4caf50,stroke:#388e3c,stroke-width:3px,color:#fff
    style Rejected fill:#f44336,stroke:#d32f2f,stroke-width:2px,color:#fff
```

## 状态说明

| 状态码 | 状态名称 | 说明 |
|-------|---------|------|
| 0 | 待审核 | 用户已提交名片信息，等待审核员审核 |
| 1 | 审核通过 | 审核员审核通过，用户认证成功 |
| 2 | 审核驳回 | 审核员审核不通过，用户需重新提交 |

## 审核流程说明

1. **用户提交**
   - 用户填写名片信息
   - 上传必要的认证材料
   - 提交后状态变为待审核（0）

2. **审核员审核**
   - 审核员在后台查看待审核名片
   - 核实用户提交的信息
   - 做出通过或驳回的决定

3. **审核结果**
   - **通过**：状态变为审核通过（1），用户获得认证标识
   - **驳回**：状态变为审核驳回（2），用户可查看驳回原因并重新提交

## 主要流转路径

1. **正常认证**: 提交 → 待审核 → 审核通过
2. **驳回重审**: 提交 → 待审核 → 审核驳回 → 重新提交 → 待审核 → 审核通过