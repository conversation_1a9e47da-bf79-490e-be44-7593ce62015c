# UpdateUserNoteTraits Asynq Task

## Overview

The `UpdateUserNoteTraits` task is an asynchronous background job that updates user traits in Flagsmith based on their note submission history. This task queries the CloudBase database for note statistics and updates corresponding traits in Flagsmith for user segmentation and feature flagging.

## Task Details

- **Task Type**: `flagsmith:update_user_note_traits`
- **Queue**: `default`
- **Max Retries**: 3
- **Timeout**: 2 minutes
- **Retention**: 7 days

## Payload

The task accepts a simple payload containing only the user ID:

```go
type UpdateUserNoteTraitsPayload struct {
    UserID string `json:"user_id"`
}
```

## Traits Updated

The task updates the following traits in Flagsmith:

### Note Statistics
- `has_uploaded_notes` (bool): Whether the user has uploaded any notes
- `notes_approved_count` (int): Number of approved notes
- `notes_rejected_count` (int): Number of rejected notes  
- `notes_pending_count` (int): Number of notes under review
- `notes_total_count` (int): Total number of notes submitted

### Calculated Metrics
- `note_approval_rate` (float): Percentage of notes approved (approved / (approved + rejected))
- `note_trust_level` (string): User trust level based on approval rate and volume
  - `"high"`: 10+ approved notes with 90%+ approval rate
  - `"medium"`: 5+ approved notes with 80%+ approval rate
  - `"low"`: All other cases

## Usage

### Basic Usage

```go
import "tandian-server/internal/tasks"

// Enqueue a task to update user note traits
err := tasks.EnqueueUpdateUserNoteTraitsTask("user_123")
if err != nil {
    // Handle error
}
```

### Integration Points

#### 1. After Note Submission (Status 30)
When a user submits a note for review:

```go
// In notes_handler.go
func HandleNoteSubmission(userID string, noteData NoteData) error {
    // ... submit note logic ...
    
    // Update user traits asynchronously
    go tasks.EnqueueUpdateUserNoteTraitsTask(userID)
    
    return nil
}
```

#### 2. After Note Approval (Status 31)
When an admin approves a note:

```go
func HandleNoteApproval(orderID string, userID string) error {
    // ... approve note logic ...
    
    // Update traits to reflect approval
    tasks.EnqueueUpdateUserNoteTraitsTask(userID)
    
    return nil
}
```

#### 3. After Note Rejection (Status 32)
When an admin rejects a note:

```go
func HandleNoteRejection(orderID string, userID string) error {
    // ... reject note logic ...
    
    // Update traits to reflect rejection
    tasks.EnqueueUpdateUserNoteTraitsTask(userID)
    
    return nil
}
```

#### 4. Batch Updates
For periodic synchronization of all user traits:

```go
func SyncAllUserNoteTraits() error {
    userIDs := getUserIDsFromDatabase()
    
    for _, userID := range userIDs {
        tasks.EnqueueUpdateUserNoteTraitsTask(userID)
    }
    
    return nil
}
```

## Server Configuration

The task handler is registered in `cmd/server/main.go`:

```go
mux.HandleFunc(tasks.TypeUpdateUserNoteTraits, tasks.HandleUpdateUserNoteTraitsTask)
```

This registration works in all environments (development, staging, production).

## Database Queries

The task queries the `orders` collection in CloudBase with the following status codes:
- Status `"30"`: Under review (pending)
- Status `"31"`: Approved
- Status `"32"`: Rejected

## Error Handling

The task implements the following error handling:

1. **Invalid Payload**: Returns `asynq.SkipRetry` to prevent retries
2. **Flagsmith Disabled**: Logs and returns nil (no error)
3. **CloudBase Errors**: Returns error for retry (up to 3 times)
4. **Flagsmith API Errors**: Returns error for retry (up to 3 times)

## Monitoring

Monitor the following in your logs:

- Task enqueueing: Look for "Enqueued update user note traits task"
- Task execution: Look for "Handling update user note traits task"
- Success: Look for "Successfully updated user note traits"
- Failures: Look for errors with context "tasks:flagsmith"

## Performance Considerations

- Each task makes 4 database queries (total, approved, rejected, pending)
- Consider batching updates during high traffic periods
- The task runs asynchronously and doesn't block user operations

## Testing

Run the tests:

```bash
go test ./internal/tasks -run TestUpdateUserNoteTraits
```

## Example Flagsmith Segments

Based on the traits updated by this task, you can create segments in Flagsmith:

1. **Trusted Contributors**
   - Rule: `note_trust_level = "high"`
   - Use: Auto-approve future notes, special badges

2. **New Note Writers**
   - Rule: `notes_total_count < 3`
   - Use: Show tutorial tips, beginner guidelines

3. **High Volume Contributors**
   - Rule: `notes_approved_count > 20`
   - Use: Community rewards, advanced features

4. **Quality Issues**
   - Rule: `note_approval_rate < 50`
   - Use: Additional review requirements, quality tips

## Future Enhancements

Potential improvements for this task:

1. Add caching to reduce database queries
2. Implement batch processing for multiple users
3. Add more granular trust levels
4. Track note quality metrics beyond approval rate
5. Add time-based traits (e.g., notes in last 30 days)
6. Implement webhook notifications for trait changes