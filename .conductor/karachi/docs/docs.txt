
商户单号查询转账单 https://pay.weixin.qq.com/doc/v3/merchant/4012716437  
微信单号查询转账单 https://pay.weixin.qq.com/doc/v3/merchant/**********
撤销转账 https://pay.weixin.qq.com/doc/v3/merchant/**********

申请交易账单
https://pay.weixin.qq.com/doc/v3/merchant/**********

申请资金账单
https://pay.weixin.qq.com/doc/v3/merchant/**********

下载账单
https://pay.weixin.qq.com/doc/v3/merchant/**********



下载交易账单

``` 
go run cmd/cli/main.go downloadBill --type trade --date 2025-07-01 --bill-type ALL

``` 

下载资金账单

``` 
go run cmd/cli/main.go downloadBill --type fundflow --date 2025-07-01 --account-type BASIC --output-dir ~/Downloads/

go run cmd/cli/main.go downloadBill --type fundflow --date ******** --account-type OPERATION
```



// https://wechatpay.js.org/webhook/v3/TRANSACTION.SUCCESS#BASIC

// 常见的几种回调通知

// TRANSACTION.SUCCESS
// 支付成功通知(JSON)
// 微信支付通过支付通知接口将用户支付成功消息通知给商户

// REFUND.SUCCESS
// 退款成功通知(JSON)
// 退款状态改变后，微信会把相关退款结果发送给商户。

// MCHTRANSFER.BILL.FINISHED
// 商家转账回调通知（用户确认模式）(JSON)
// 商家转账单据到终态后 （转账完成、转账失败和已撤销，对应单据状态status的值为SUCCESS、FAIL和CANCELLED），微信支付会把单据的信息发送给商户，商户需要接收处理该消息，并返回应答。

// COUPON.USE
// 核销事件回调通知(JSON)
// 用户使用券后，微信会把相关核销券信息发送给商户，商户需要接收处理，并按照文档规范返回应答。出于安全的考虑，我们对核销券信息数据进行了加密，商户需要先对通知数据进行解密，才能得到核销券信息数据。

// COUPON.SEND
// 领券事件回调通知(JSON)
// 领券完成后，微信会把相关领券结果和用户信息发送给商户，商户需要接收处理，并按照文档规范返回应答。出于安全的考虑，我们对支付结果数据进行了加密，商户需要先对通知数据进行解密，才能得到支付结果数据。

// REFUND.ABNORMAL
// 退款异常通知(JSON)
// 款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【平台—>交易中心】，手动处理此笔退款

// REFUND.CLOSED
// 退款关闭通知(JSON)
// 退款状态改变后，微信会把相关退款结果发送给商户。
