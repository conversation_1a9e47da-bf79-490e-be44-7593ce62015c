# wechat pay notify callback examples

## TRANSACTION.SUCCESS

```
notifyReq:  {
  "RawRequest": null,
  "create_time": "2025-08-15T22:40:41+08:00",
  "event_type": "TRANSACTION.SUCCESS",
  "id": "6a61ae9c-6618-5cbc-ac74-62252a17b73f",
  "resource": {
    "Plaintext": "{\"mchid\":\"**********\",\"appid\":\"wxf77ccd1007594f39\",\"out_trade_no\":\"20250815224035967813\",\"transaction_id\":\"4200002807202508154283167087\",\"trade_type\":\"JSAPI\",\"trade_state\":\"SUCCESS\",\"trade_state_desc\":\"支付成功\",\"bank_type\":\"OTHERS\",\"attach\":\"order_id:BZ15SQL1K6\",\"success_time\":\"2025-08-15T22:40:41+08:00\",\"payer\":{\"openid\":\"o5G2Y60AgRtAUxa5_tpB_Vwz23co\"},\"amount\":{\"total\":8800,\"payer_total\":8800,\"currency\":\"CNY\",\"payer_currency\":\"CNY\"}}",
    "algorithm": "AEAD_AES_256_GCM",
    "associated_data": "transaction",
    "ciphertext": "lXNlsRRfnkCOETmHIPTyRGY4MFJs/d+A/ev4OUQrTpv0RuRSTjTFOYLvs94vCp5KwNHvD4/zrPohLya5tAmigEuh3uEMlFf9OV30XkyXHjChKDqfphf2ndMFLPUAMq4KEnRuTbJJtSsq0P96ezCJ6L0fSHKWh3QBaThFbXuoM5XRHAUbM2/n2NnD6PJ77A9mEgveDlcJlW444v9MTbVeQLtFBOkYiG37bJ2ELeDMEbCp9BgLrLkVSHWvZrLYTihkXXAwm3E6zfhz0EES48kyqe2Dy/+aJmV9/QrxoUcIZd+YLDhHf9XUgJJLqDZHDtBMPk4kQmhizIRm8l0kWDhRYaebw8uorN1F/4KVSoO+bIDeQSQMYPwJwjb72f3yMFnStu4l5/Jc1z5hjamRVknhYCxDHJMwOu75u6/+s9jRPIMQA4U/Y64K/FHssUpLguG3bWiz758sNtMOSbu0/IhfVmKJLzd41RCjhxrObW373RQ9hjZ9wFgn9g/nRfP6bhMOOSzZeAVcrpIgBSiFihiVq1IsQwBTqwDs/iM9f0UmOiDNliUr8nD7Wo+gdgQmPBVLj6Evm8oueUvolD4TdZpccw/KJGsKZk59iLY0IQ==",
    "nonce": "rSwmlWVWZIW4",
    "original_type": "transaction"
  },
  "resource_type": "encrypt-resource",
  "summary": "支付成功"
}
decoded from ciphertext:  {
  "amount": {
    "currency": "CNY",
    "payer_currency": "CNY",
    "payer_total": 8800,
    "total": 8800
  },
  "appid": "wxf77ccd1007594f39",
  "attach": "order_id:BZ15SQL1K6",
  "bank_type": "OTHERS",
  "mchid": "**********",
  "out_trade_no": "20250815224035967813",
  "payer": {
    "openid": "o5G2Y60AgRtAUxa5_tpB_Vwz23co"
  },
  "success_time": "2025-08-15T22:40:41+08:00",
  "trade_state": "SUCCESS",
  "trade_state_desc": "支付成功",
  "trade_type": "JSAPI",
  "transaction_id": "4200002807202508154283167087"
}
```

## REFUND.SUCCESS

```
notifyReq:  {
  "RawRequest": null,
  "create_time": "2025-08-15T09:53:22+08:00",
  "event_type": "REFUND.SUCCESS",
  "id": "058dd550-a89d-5070-abba-76fc63f8d478",
  "resource": {
    "Plaintext": "{\"mchid\":\"**********\",\"out_trade_no\":\"20250805210122195309\",\"transaction_id\":\"4200002817202508050214082026\",\"out_refund_no\":\"REFUND_1755222798_JKK38Z\",\"refund_id\":\"50302604262025081577305147068\",\"refund_status\":\"SUCCESS\",\"success_time\":\"2025-08-15T09:53:22+08:00\",\"amount\":{\"total\":2000,\"refund\":2000,\"payer_total\":2000,\"payer_refund\":2000},\"user_received_account\":\"平安银行借记卡9585\"}",
    "algorithm": "AEAD_AES_256_GCM",
    "associated_data": "refund",
    "ciphertext": "Km34ePYGLU5vOLdyys1AOpOLMar9g9WjaCgFvNd29osvPUdDwLMWi8IO/0IyWMOyGRyHO3cshWqkzfyaBE4dKHcXdzTGbfffRzoOT55uTIif85Hcu0zzYs4AWGPfL0TZDJtTZ1QPE+HXdGA6myxueecsDVdgGVslfB5c1SPpsBMpBcwTszSULFfUKonl2wacTkO7/RRMm2bGjpF/M15gCMzUtdvWIELTTuMvGRBhRa3sTEfo0ZzMDf7GLUSa04drSg5ugT1HwdNqzt4BA5qx35VM1U0EMg/hztUm4pm8t1MKxjIvgKcHn8d/MGrBoKt3rs8mqxTRpQh7O5vtZ3PtwbQlg0v5ZSE4PynOS8KNzoO1QS2l9yYtevab8e3t8D33+AsN/J2vcjfkulTCpJbIVYdiv9lNik9XBJx6lVsAjCLfIGcU/rKaswqnQIQMKqSSA5FO2WAzAJYJcUqVd/hSqa7Ua/HGgqsef0/bopxBRb60sK3MG1K+toi6D2gMu8Ip5Dx6RNPJtes234/GdGiBs0ktYxiu24XNAg==",
    "nonce": "99YyCCndPxFl",
    "original_type": "refund"
  },
  "resource_type": "encrypt-resource",
  "summary": "退款成功"
}
decoded from ciphertext:  {
  "amount": {
    "payer_refund": 2000,
    "payer_total": 2000,
    "refund": 2000,
    "total": 2000
  },
  "mchid": "**********",
  "out_refund_no": "REFUND_1755222798_JKK38Z",
  "out_trade_no": "20250805210122195309",
  "refund_id": "50302604262025081577305147068",
  "refund_status": "SUCCESS",
  "success_time": "2025-08-15T09:53:22+08:00",
  "transaction_id": "4200002817202508050214082026",
  "user_received_account": "平安银行借记卡9585"
}
```

## MCHTRANSFER.BILL.FINISHED

```
notifyReq:  {
  "RawRequest": null,
  "create_time": "2025-08-15T22:07:37+08:00",
  "event_type": "MCHTRANSFER.BILL.FINISHED",
  "id": "ffe491e3-6e0f-55f1-8e99-cac2e30e05bc",
  "resource": {
    "Plaintext": "{\"mch_id\":\"**********\",\"out_bill_no\":\"T20250815220732018121\",\"transfer_bill_no\":\"1330001886819462508150021663649053\",\"transfer_amount\":2800,\"state\":\"SUCCESS\",\"openid\":\"o5G2Y6-Z98oihjEx51bqoxNaYwlY\",\"create_time\":\"2025-08-15T22:07:33+08:00\",\"update_time\":\"2025-08-15T22:07:37+08:00\",\"mchid\":\"**********\"}",
    "algorithm": "AEAD_AES_256_GCM",
    "associated_data": "mch_payment",
    "ciphertext": "+Y1iLdaSNljsJ5ak/m0YBcXDWo2DFhTbcgLuf6XGzgiyoo7hZ+tPBXOQ/9ltqwxN+3aSJ8OAE8+othOUVZfZa0QOi9bZzB/Wkax3E4NhBBQZxOs2APNZiEYrNMbuoUI4pgI2xhGWBi4wS+s8KZliwek4mtb/ebgPG9mtnJa2bwyD24NNGY3bVeBiRw5t5TdKKDoi34jMgXtXvQVLREg2//wn9RuiS+gJQJP/srul4Vb/97xV0GNI5J6ebutwaCE7tA/ZsaGAKgfU24RQyrIfuPRKoucmSLRMEMu9+3SC5BQB9aRew6Emi654cAjU0h4Uq0VfKcqZPOT3BWhBpEUDvObyjtZ4INVmaW+CordurIvZCG+hsJ1i7bK8cJIpaytHamFNHibJjuIz8n4m1CtZwvMdy9T/0VjN7USB/Tx4Rw==",
    "nonce": "HvdZDABcTrpU",
    "original_type": "mch_payment"
  },
  "resource_type": "encrypt-resource",
  "summary": "商家转账单据终态通知"
}
decoded from ciphertext:  {
  "appid": "",
  "create_time": "2025-08-15T22:07:33+08:00",
  "event_type": "",
  "fail_reason": "",
  "id": "",
  "mch_id": "**********",
  "openid": "o5G2Y6-Z98oihjEx51bqoxNaYwlY",
  "out_bill_no": "T20250815220732018121",
  "resource_type": "",
  "state": "SUCCESS",
  "summary": "",
  "transfer_amount": 2800,
  "transfer_bill_no": "1330001886819462508150021663649053",
  "transfer_remark": "",
  "update_time": "2025-08-15T22:07:37+08:00",
  "user_name": ""
}
```