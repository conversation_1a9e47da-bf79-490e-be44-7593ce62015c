# Golangci-lint Configuration

This document describes the linting configuration for the tandian-server project using golangci-lint v2.

## Overview

The project uses a streamlined golangci-lint configuration optimized for practical use with minimal noise. The configuration focuses on catching real issues while avoiding false positives.

## Key Features

### 1. **Run Configuration**
- **Timeout**: 2 minutes for analysis
- **Test Files**: Excluded by default (tests: false)
- **Version**: Configured for golangci-lint v2.x

### 2. **Code Formatters** (6 enabled)

The following formatters ensure consistent code style:

| Formatter | Purpose | Configuration |
|-----------|---------|---------------|
| **gci** | Organizes imports into sections | Standard → External → Local (tandian-server) |
| **gofmt** | Standard Go formatting | Default settings |
| **gofumpt** | Enhanced Go formatting | Extra rules enabled |
| **goimports** | Import formatting and management | Local prefix: tandian-server |
| **golines** | Manages long lines | Max length: 120 characters |
| **swaggo** | Swagger comment formatting | Default settings |

### 3. **Active Linters** (14 enabled)

Core linters for code quality:

| Linter | Purpose |
|--------|---------|
| **govet** | Reports suspicious constructs (with shadow detection) |
| **ineffassign** | Detects ineffectual assignments |
| **staticcheck** | Advanced static analysis (all checks except ST1000, ST1003, SA9003) |
| **misspell** | Finds commonly misspelled words |
| **tagalign** | Checks struct tag alignment |
| **musttag** | Enforces field tags in marshaled structs |
| **whitespace** | Detects unnecessary whitespace |
| **wastedassign** | Finds wasted assignments |
| **reassign** | Checks for reassignments |
| **makezero** | Finds slice declarations that aren't zeroed |
| **ireturn** | Checks for interface returns |
| **godot** | Checks comment formatting |
| **goheader** | Validates file headers |
| **funcorder** | Checks function ordering |
| **errchkjson** | Checks JSON error handling |
| **canonicalheader** | Ensures canonical header usage |
| **gocritic** | Provides opinionated code criticism |

### 4. **Disabled Linters**

These linters are explicitly disabled to reduce noise:
- `errcheck` - Error checking (too noisy)
- `unused` - Unused code detection
- `unparam` - Unused parameters

### 5. **Exclusion Rules**

Smart exclusions to avoid false positives:

- **SA9003**: Empty branch checks disabled
- **Defer statements**: Error checks ignored in defer
- **Test files**: Relaxed rules for `_test.go` files
- **Generated files**: All linters skipped
- **Vendor directory**: All linters skipped

### 6. **Issue Management**

- **Max issues per linter**: 50
- **Max same issues**: 3
- **New issues only**: Disabled (shows all issues)

## Usage

### Installation

```bash
# Install golangci-lint
brew install golangci-lint

# Or using go install
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
```

### Running the Linter

```bash
# Run all configured linters
golangci-lint run

# Run with auto-fix (where possible)
golangci-lint run --fix

# Check specific directories
golangci-lint run ./internal/...

# Run on test files too
golangci-lint run --tests

# Run specific linter only
golangci-lint run --disable-all -E govet
```

### CI Integration

```yaml
# Example GitHub Actions workflow
- name: golangci-lint
  uses: golangci/golangci-lint-action@v3
  with:
    version: latest
    args: --timeout=5m
```

## Import Organization

The configuration enforces a specific import ordering:

```go
import (
    // 1. Standard library
    "fmt"
    "strings"
    
    // 2. External packages
    "github.com/gofiber/fiber/v3"
    "github.com/wechatpay-apiv3/wechatpay-go/core"
    
    // 3. Local packages
    "tandian-server/internal/config"
    "tandian-server/internal/pay"
)
```

## Configuration Philosophy

This configuration follows these principles:

1. **Practical over Pedantic**: Focus on real issues that affect code quality
2. **Low Noise**: Avoid linters that generate too many false positives
3. **Performance**: Skip expensive checks that provide minimal value
4. **Consistency**: Enforce consistent formatting and style
5. **Flexibility**: Allow reasonable exceptions for test and generated code

## Customization

To modify the configuration, edit `.golangci.yml` in the project root. Common customizations:

- Add new linters: Add to the `enable` list
- Disable a linter: Move to the `disable` list
- Adjust line length: Modify `golines.max-len`
- Add exclusions: Add patterns to `exclude-rules`

## Troubleshooting

If you encounter issues:

1. Check golangci-lint version: `golangci-lint --version`
2. Validate config: `golangci-lint run --print-config`
3. Run verbose mode: `golangci-lint run -v`
4. Check specific file: `golangci-lint run path/to/file.go`

## Resources

- [golangci-lint documentation](https://golangci-lint.run/)
- [Linter descriptions](https://golangci-lint.run/usage/linters/)
- [Configuration reference](https://golangci-lint.run/usage/configuration/)