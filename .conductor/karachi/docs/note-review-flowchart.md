# 笔记审核流程图

```mermaid
graph TD
    Start([用户核销订单]) --> Upload[用户上传笔记<br/>笔记状态: 0 待审核<br/>订单状态: 30 笔记审核中]
    
    Upload --> PreReview{预审核}
    
    PreReview -->|预审通过| PreApproved[预审通过<br/>笔记状态: 3]
    PreReview -->|不通过| ManualReview[人工审核]
    
    PreApproved -->|延时10分钟<br/>auto_send_note_link| SendLink[发送笔记链接到商家群<br/>笔记状态: 4]
    
    SendLink -->|延时1小时<br/>auto_pass_note| AutoApprove[笔记自动审核通过<br/>笔记状态: 1<br/>订单状态: 31]
    
    ManualReview -->|人工通过| ManualApproved[审核通过<br/>笔记状态：1<br/>订单状态: 31]
    ManualReview -->|人工驳回| Rejected[审核驳回<br/>笔记状态: 2<br/>订单状态: 32]
    
    Rejected -->|重新上传| Upload
    
    AutoApprove --> Complete([完成])
    ManualApproved --> Complete
    
    style Upload fill:#ffffff
    style PreApproved fill:#d5e8d4
    style SendLink fill:#d4e1f5
    style AutoApprove fill:#d5e8d4
    style ManualApproved fill:#d5e8d4
    style Rejected fill:#f8cecc
    style ManualReview fill:#fce4d6
```

## 流程说明

### 笔记状态码
| 状态码 | 说明 |
|-------|------|
| 0 | 待审核 |
| 1 | 审核通过 |
| 2 | 审核驳回 |
| 3 | 笔记已通过预审 |
| 4 | 笔记链接已发送到商家群 |
| 5 | 笔记待自动通过变为1 |

### 订单状态码
| 状态码 | 说明 |
|-------|------|
| 30 | 笔记审核中 |
| 31 | 审核通过 |
| 32 | 审核驳回 |

## 自动化任务

### 1. 发送笔记链接任务
- **任务类型**: `scheduler:auto_enqueue_send_note_link`
- **执行逻辑**: 
  - 查询所有状态为3（链接已发送）的笔记
  - 为每个笔记创建发送链接任务
  - 延时5分钟执行

### 2. 自动通过审核任务
- **任务类型**: `scheduler:auto_enqueue_pass_note`
- **执行逻辑**:
  - 定时扫描符合条件的笔记
  - 自动更新订单状态为31（审核通过）
  - 支持随机延时，避免批量处理

## 关键流程

1. **预审机制**: 笔记上传后先进行预审，通过的可以走自动审核流程
2. **延时发送**: 预审通过后延时5分钟发送到商家群，给运营预留检查时间
3. **自动审核**: 已发送到商家群的笔记可通过定时任务自动审核通过
4. **人工介入**: 预审不通过或需要特殊处理的笔记走人工审核流程
5. **驳回重传**: 被驳回的笔记可以重新上传，重新进入审核流程