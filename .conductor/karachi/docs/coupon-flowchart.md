# 优惠券流程图

```mermaid
graph TD
    Start([创建优惠券批次]) --> Stock[批次创建成功<br/>状态: UNACTIVATED]
    
    Stock -->|激活批次| Active[批次激活<br/>状态: ACTIVATING]
    
    Active --> Available[可领取<br/>状态: ONGOING]
    
    Available -->|用户领券| Sended[已发放<br/>券状态: SENDED]
    
    Sended -->|用户使用| Used[已核销<br/>券状态: USED]
    Sended -->|过期未使用| Expired[已过期<br/>券状态: EXPIRED]
    Sended -->|商家作废| Deactivated[已作废<br/>券状态: DEACTIVED]
    Sended -->|商家返还| Available2[返还可用<br/>券状态: SENDED]
    
    Used -->|商家返还| Returned[已返还<br/>券状态: SENDED]
    
    Available -->|批次暂停| Paused[批次暂停<br/>状态: PAUSED]
    Paused -->|批次重启| Available
    
    Available -->|批次结束| Finished[批次结束<br/>状态: FINISHED]
    
    Returned --> Sended
    Available2 --> Sended
    
    style Stock fill:#fff5cc
    style Available fill:#d5e8d4
    style Sended fill:#d4e1f5
    style Used fill:#d5e8d4
    style Expired fill:#f8cecc
    style Deactivated fill:#f8cecc
    style Paused fill:#fce4d6
    style Finished fill:#e1d5e7
```

## 券批次状态

| 状态 | 说明 |
|------|------|
| UNACTIVATED | 未激活 |
| ACTIVATING | 激活中 |
| ONGOING | 进行中（可领取） |
| PAUSED | 已暂停 |
| FINISHED | 已结束 |

## 优惠券状态

| 状态 | 说明 |
|------|------|
| SENDED | 已发放（可使用） |
| USED | 已核销 |
| EXPIRED | 已过期 |
| DEACTIVED | 已作废 |

## 主要操作

### 1. 创建批次
- 通过 API `/api/v1/create_coupons` 创建
- 设置批次名称、预算、优惠金额等参数
- 创建后批次处于未激活状态

### 2. 发放优惠券
- 用户通过领券中心领取
- 支持设置每用户最大领取数量
- 领取后券状态为 SENDED

### 3. 核销优惠券
```bash
./bin/cli coupon use --stock_id=批次ID --coupon_code=券码 --openid=用户ID
```
- 用户在支付时使用优惠券
- 核销后状态变为 USED

### 4. 返还优惠券
```bash
./bin/cli coupon return --stock_id=批次ID --coupon_code=券码
```
- 可将已使用的券返还给用户
- 返还后券重新变为 SENDED 状态

### 5. 作废优惠券
```bash
./bin/cli coupon deactivate --stock_id=批次ID --coupon_code=券码
```
- 永久作废优惠券
- 作废后状态为 DEACTIVED

### 6. 查询操作
```bash
# 查询优惠券详情
./bin/cli coupon query --coupon_code=券码 --openid=用户ID

# 查询批次详情
./bin/cli coupon query-stock --stock_id=批次ID
```

## 业务场景

1. **正常使用流程**: 创建批次 → 激活 → 用户领取 → 用户使用 → 核销完成
2. **退款流程**: 已核销 → 订单退款 → 返还优惠券 → 重新可用
3. **过期处理**: 已发放 → 超过有效期 → 自动过期
4. **异常处理**: 已发放 → 商家作废 → 永久不可用
5. **批次管理**: 可随时暂停/重启批次，控制发放节奏