# 创建商家券 API 文档

## 接口说明

该接口用于创建微信支付商家券批次。

## 请求地址

```
POST /api/v1/create_coupons
```

## 请求头

```
Content-Type: application/json
```

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| stock_name | string | 是 | 批次名称，最多21个字符 |
| belong_merchant | string | 是 | 批次归属商户号 |
| comment | string | 否 | 批次备注，最多20个字符 |
| goods_name | string | 是 | 适用商品范围，最多15个字符 |
| available_begin_time | string | 是 | 批次发放开始时间，RFC3339格式 |
| available_end_time | string | 是 | 批次发放结束时间，RFC3339格式 |
| max_coupons | int64 | 是 | 批次总预算（单位：分），必须大于0 |
| max_coupons_per_user | int32 | 是 | 用户最大可领个数，必须大于0 |
| coupon_amount | int64 | 是 | 优惠金额（单位：分），必须大于0 |
| transaction_minimum | int64 | 是 | 使用门槛（单位：分），不能小于0 |
| out_request_no | string | 是 | 商户请求单号，需保持唯一 |

## 请求示例

```json
{
    "stock_name": "8月优惠券",
    "belong_merchant": "1234567890",
    "comment": "活动使用",
    "goods_name": "全场商品",
    "available_begin_time": "2024-08-01T00:00:00Z",
    "available_end_time": "2024-08-31T23:59:59Z",
    "max_coupons": 100000,
    "max_coupons_per_user": 5,
    "coupon_amount": 500,
    "transaction_minimum": 1000,
    "out_request_no": "MERCHANT_20240801_001"
}
```

## 响应参数

### 成功响应

```json
{
    "code": 0,
    "message": "创建商家券成功",
    "data": {
        "stock_id": "98065001",
        "create_time": "2024-08-01T00:00:00+08:00",
        "out_request_no": "MERCHANT_20240801_001"
    }
}
```

### 错误响应

```json
{
    "code": -1,
    "message": "错误信息",
    "error": "详细错误描述"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| -1 | 通用错误，具体查看message和error字段 |

## 注意事项

1. 时间格式必须符合RFC3339标准，例如：`2024-08-01T00:00:00Z`
2. 所有金额字段单位都是分
3. out_request_no 需要商户侧保证唯一性，建议格式：商户ID+日期+流水号
4. 该接口调用微信支付商家券创建接口，限流规则：1000次/分钟