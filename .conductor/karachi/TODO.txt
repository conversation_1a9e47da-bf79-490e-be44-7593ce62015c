
### 定时任务要优化成分批执行

开通多城市数据量大了之后

每晚重置库存要分批执行              scheduler_reset_quantity
每晚重置探店券 要分批执行           scheduler_reset_coupons


每晚自动过期未核销订单 要分批执行    scheduler_auto_refund_unverified_orders
每2分钟自动关闭未支付订单           scheduler_auto_close_unpaid_orders


计算用户积分
计算用户等级


团长升降级



SELECT _id, balance_before, type, amount, balance_after, DATE_FORMAT(FROM_UNIXTIME(createdAt / 1000), '%Y-%m-%d %H:%i:%s') FROM transactions where user_id = 'BAL9PK0TMA' order by createdAt asc;


## Payment Handler Security Concerns

### 🛡️ Critical Security Issues

1. **Information Disclosure**
   - Debug logging exposes sensitive payment data (payment_handler.go:104-105,108,136,215)
   - Error messages may leak internal details
   - Remove all debug logs that print payment information

2. **Missing Rate Limiting**
   - No protection against payment spam
   - Could lead to resource exhaustion
   - Implement rate limiting per user/IP

3. **No Audit Trail**
   - Payment operations not logged for compliance
   - Missing transaction history
   - Implement secure audit logging

4. **Input Sanitization**
   - Order descriptions built with string concatenation
   - Could be vulnerable to injection if not properly escaped
   - Use parameterized queries and proper escaping


## Additional Suggestions for Payment Handler

1. **Add Middleware**
   - Authentication/authorization middleware
   - Request logging with sanitization (remove sensitive data)
   - Rate limiting per user/IP

2. **Implement Circuit Breaker**
   - Protect against downstream failures
   - Graceful degradation when payment gateway is down

3. **Add Comprehensive Tests**
   - Unit tests for validation logic
   - Integration tests with mock payment gateway
   - Error scenario coverage

4. **Consider Event Sourcing**
   - Track all payment state changes
   - Enable replay and audit capabilities
   - Better debugging and compliance

5. **Standardize Response Format**
   - Create unified API response structure
   - Consistent error handling across all endpoints

6. **Use Decimal/Int64 for Money**
   - Avoid float64 for monetary values (precision errors)
   - Use int64 to represent cents
   - Implement proper money type with conversion methods

7. **Add Request Context Timeout**
   - Set timeout for payment operations (e.g., 30 seconds)
   - Prevent hanging requests

8. **Add Idempotency Keys**
   - Prevent duplicate payments
   - Store transaction states
   - Critical for payment reliability