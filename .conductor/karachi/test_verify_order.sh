#!/bin/bash

# Test script for verify order endpoint

API_URL="http://localhost:8080/api/v1/wx/verify_order"

# Test case 1: Verify an order
echo "Testing order verification..."
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "order_no": "ORDER_1234567890"
  }' | jq .

echo -e "\n\n"

# Test case 2: Missing order_no
echo "Testing with missing order_no..."
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d '{}' | jq .

echo -e "\n\n"

# Test case 3: Empty order_no
echo "Testing with empty order_no..."
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "order_no": ""
  }' | jq .