package model

import (
	"context"
	"errors"
	"log"
	"time"

	"github.com/dfang/go-prettyjson"

	"tandian-server/internal/util/cloudbase"
)

const (
	MAX_DAILY_INITIATIONS = 1         // 每日最多发起1次
	MAX_DAILY_ASSISTS     = 1         // 每日最多助力10次
	TARGET_HELP_COUNT     = 3         // 需要5个好友助力
	CAMPAIGN_DURATION     = 24 * 3600 // 24 hours in seconds
	INITIATOR_REWARD      = 500       // 发起者奖励5元(500分)
	ASSISTANT_REWARD      = 100       // 助力者奖励1元(100分)
)

// CampaignDetailResponse represents the detailed campaign information.
type CampaignDetailResponse struct {
	Campaign   HelpCampaign    `json:"campaign"`
	Assistants []AssistantInfo `json:"assistants"`
}

// AssistantInfo represents information about an assistant.
type AssistantInfo struct {
	InviterUserID      string  `json:"inviter_user_id"`
	CampaignID         string  `json:"campaign_id"`
	HelperUserID       string  `json:"helper_user_id"`
	HelperUserNickname string  `json:"helper_user_nickname"`
	HelperUserAvatar   string  `json:"helper_user_avatar"`
	RewardStatus       float64 `json:"reward_status"`
	CreatedAt          int64   `json:"created_at"`
}

// IsMaxDailyAssistsReached 每日助力次数是否用完.
func IsMaxDailyAssistsReached(ctx context.Context, helperUserID string) bool {
	// Get today's start time
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix() * 1000

	// Check today's assist count
	countFilter := map[string]interface{}{
		"helper_user_id": map[string]interface{}{
			"$eq": helperUserID,
		},
		"createdAt": map[string]interface{}{
			"$gte": todayStart,
		},
	}

	dailyAssistCount, err := cloudbase.Count(ctx, "help_records", countFilter)
	if err != nil {
		return false
	}

	// prettyjson.Printf("dailyAssistCount: %s\n", dailyAssistCount)
	return dailyAssistCount >= MAX_DAILY_ASSISTS
}

// HasHelpedToday 每日助力次数是否用完.
func HasHelpedToday(ctx context.Context, helperUserID string) bool {
	return IsMaxDailyAssistsReached(ctx, helperUserID)
}

// GetHelpRecordCountOfCampaign 获取活动助力总数.
func GetHelpRecordCountOfCampaign(ctx context.Context, campaignID string) int {
	countHelpRecordsFilter := map[string]interface{}{
		"campaign_id": map[string]interface{}{
			"$eq": campaignID,
		},
	}
	countHelpRecordsForCampaign, err := cloudbase.Count(ctx, "help_records", countHelpRecordsFilter)
	if err != nil {
		return 0
	}

	return int(countHelpRecordsForCampaign)
}

// IsCampaignExpired 活动是否过期.
func IsCampaignExpired(ctx context.Context, campaignID string) bool {
	campaign, err := cloudbase.FindById(ctx, "help_campaigns", campaignID)
	if err != nil {
		return true
	}

	// Safe type assertion for createdAt
	createdAt, ok := campaign["createdAt"].(float64)
	if !ok {
		// If we can't get the creation date, consider it expired
		return true
	}

	// Convert Unix timestamp to time.Time
	campaignCreatedTime := time.Unix(int64(createdAt/1000), 0)
	now := time.Now()

	// Check if campaign was created today
	campaignDate := time.Date(
		campaignCreatedTime.Year(),
		campaignCreatedTime.Month(),
		campaignCreatedTime.Day(),
		0,
		0,
		0,
		0,
		campaignCreatedTime.Location(),
	)
	todayDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// Campaign is expired if it was NOT created today
	return !campaignDate.Equal(todayDate)
}

// IsCampaignFinished 活动是否完成.
func IsCampaignFinished(ctx context.Context, campaignID string) bool {
	count := GetCurrentCountOfCampaign(ctx, campaignID)
	return count >= TARGET_HELP_COUNT
}

// IsAssistedCampaignToday 今日是否助力过此活动.
func IsAssistedCampaignToday(ctx context.Context, helperUserID, campaignID string) bool {
	// Get today's start time
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix() * 1000

	// Check today's assist count
	countFilter := map[string]interface{}{
		"helper_user_id": map[string]interface{}{
			"$eq": helperUserID,
		},
		"createdAt": map[string]interface{}{
			"$gte": todayStart,
		},
		"campaign_id": map[string]interface{}{
			"$eq": campaignID,
		},
	}

	countResult, err := cloudbase.Count(ctx, "help_records", countFilter)
	if err != nil {
		return false
	}

	// prettyjson.Printf("countResult: %s\n", countResult)
	return countResult >= 1
}

// GetCurrentCountOfCampaign 获取当前活动助力人数.
func GetCurrentCountOfCampaign(ctx context.Context, campaignID string) int {
	helpRecordsQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"campaign_id": map[string]interface{}{
					"$eq": campaignID,
				},
			},
		},
	}
	helpRecordsData, err := cloudbase.GetItems(ctx, "help_records", helpRecordsQuery)
	if err != nil {
		return 0
	}
	log.Printf("campaign: %s", campaignID)
	prettyjson.Printf("helpRecordsData: %s\n", helpRecordsData)

	// Safe type assertion for records
	recordsInterface, ok := helpRecordsData["records"]
	if !ok {
		return 0
	}
	records, ok := recordsInterface.([]interface{})
	if !ok {
		return 0
	}
	return len(records)
}

// GetHelpRecordsOfCampaign 获取活动助力记录.
func GetHelpRecordsOfCampaign(ctx context.Context, campaignID string) []AssistantInfo {
	assistFilter := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"campaign_id": map[string]interface{}{
					"$eq": campaignID,
				},
			},
		},
	}

	helpRecordsData, err := cloudbase.GetItems(ctx, "help_records", assistFilter)
	if err != nil {
		return nil
	}
	prettyjson.Printf("helpRecordsData: %s\n", helpRecordsData)

	// Safe type assertion for records
	recordsInterface, ok := helpRecordsData["records"]
	if !ok {
		return nil
	}
	records, ok := recordsInterface.([]interface{})
	if !ok {
		return nil
	}

	assistantInfos := make([]AssistantInfo, 0, len(records))
	for _, v := range records {
		record, ok := v.(map[string]interface{})
		if !ok {
			continue
		}
		prettyjson.Printf("record: %s\n", record)

		var nickname, avatar string
		helperUserID, ok := record["helper_user_id"].(string)
		if !ok {
			continue
		}

		userData, err := GetUserInfo(ctx, helperUserID)
		if err == nil && userData != nil {
			if n, ok := userData["nickname"].(string); ok {
				nickname = n
			}
			if a, ok := userData["avatar"].(string); ok {
				avatar = a
			}
		}

		assistantInfo := AssistantInfo{
			HelperUserID:       helperUserID,
			HelperUserNickname: nickname,
			HelperUserAvatar:   avatar,
			RewardStatus:       0,
		}

		// Safe type assertions for other fields
		if inviterID, ok := record["inviter_user_id"].(string); ok {
			assistantInfo.InviterUserID = inviterID
		}
		if campaignID, ok := record["campaign_id"].(string); ok {
			assistantInfo.CampaignID = campaignID
		}
		if createdAt, ok := record["createdAt"].(float64); ok {
			assistantInfo.CreatedAt = int64(createdAt)
		} else if createdAt, ok := record["created_at"].(float64); ok {
			assistantInfo.CreatedAt = int64(createdAt)
		}
		if rewardStatus, ok := record["reward_status"].(float64); ok {
			assistantInfo.RewardStatus = rewardStatus
		}
		assistantInfos = append(assistantInfos, assistantInfo)
	}

	return assistantInfos
}

// GetMyHelpRecordForCampaign 获取用户在活动中的助力记录.
func GetMyHelpRecordForCampaign(ctx context.Context, campaignID, userID string) map[string]interface{} {
	filter := map[string]interface{}{
		"campaign_id": map[string]interface{}{
			"$eq": campaignID,
		},
		"helper_user_id": map[string]interface{}{
			"$eq": userID,
		},
	}

	record, err := cloudbase.FindOne(ctx, "help_records", filter)
	if err != nil {
		return nil
	}

	return record
}

// GetMyLatestHelpRecord 获取用户最近的助力记录（即今天的）.
func GetMyLatestHelpRecord(ctx context.Context, userID string) map[string]interface{} {
	filter := map[string]interface{}{
		"helper_user_id": map[string]interface{}{
			"$eq": userID,
		},
		"createdAt": map[string]interface{}{
			"$gte": time.Now().AddDate(0, 0, -1).Unix() * 1000,
		},
	}

	record, err := cloudbase.FindOne(ctx, "help_records", filter)
	if err != nil {
		return nil
	}

	return record
}

// GetUserInfo 获取用户信息.
func GetUserInfo(ctx context.Context, useID string) (map[string]interface{}, error) {
	userQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": useID,
				},
			},
		},
	}
	userData, err := cloudbase.GetItem(ctx, "users", userQuery)
	if err == nil && len(userData) > 0 {
		return userData, nil
	}
	return nil, errors.New("user not found")
}

// UpdateCurrentCountForCampaign 更新活动当前助力人数.
func UpdateCurrentCountForCampaign(ctx context.Context, campaignID string) (int, error) {
	// Get campaign details
	campaign, err := cloudbase.FindById(ctx, "help_campaigns", campaignID)
	if err != nil || campaign == nil {
		return 0, err
	}

	// Update campaign current count
	currentCount := GetHelpRecordCountOfCampaign(ctx, campaignID)
	updateCampaignData := map[string]interface{}{
		"current_count": currentCount,
		"updatedAt":     time.Now().Unix() * 1000,
	}

	// // Check if campaign is completed
	// campaignComplete := currentCount > 0 && currentCount >= TARGET_HELP_COUNT
	// if campaignComplete {
	// 	updateCampaignData["status"] = string(HelpCampaignStatusCompleted)
	// }

	_, err = cloudbase.UpdateById(ctx, "help_campaigns", campaignID, updateCampaignData)
	if err != nil {
		return 0, err
	}

	return currentCount, nil
}
