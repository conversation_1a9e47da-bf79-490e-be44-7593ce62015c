package model

// // 根据条件获取单条数据
// // https://docs.cloudbase.net/http-api/model/get-item
// type FindResponse struct {
// 	Data      map[string]interface{} `json:"record" mapstructure:"record"`
// 	RequestId string                 `json:"requestId" mapstructure:"requestId"`
// }

// // type Data struct {
// // 	Record map[string]interface{} `json:"record" mapstructure:"record"`
// // }

// type ErrorResponse struct {
// 	Code      string `json:"code" mapstructure:"code"`
// 	Message   string `json:"message" mapstructure:"message"`
// 	RequestId string `json:"requestId" mapstructure:"requestId"`
// }

// // 创建单条数据
// // https://docs.cloudbase.net/http-api/model/create-item
// type CreateResponse struct {
// 	Data map[string]interface{} `json:"record" mapstructure:"record"`
// }

type OrderStatus string

const (
	OrderStatusPendingPayment OrderStatus = "0"  // 待支付
	OrderStatusPaid           OrderStatus = "10" // 待核销
	OrderStatusVerified       OrderStatus = "20" // 已核销（待上传笔记）
	OrderStatusExpired        OrderStatus = "11" // 已过期(超时未核销)
	OrderStatusUnderReview    OrderStatus = "30" // 笔记审核中 under review
	OrderStatusAuditPass      OrderStatus = "31" // 审核通过 pass the audit
	OrderStatusAuditFail      OrderStatus = "32" // 审核驳回 fail the audit
	OrderStatusCanceled       OrderStatus = "81" // 已取消，未支付主动取消
	OrderStatusRefunded       OrderStatus = "40" // 取消订单并已退款
)

// HelpCampaignStatus represents the status of a help campaign.
type HelpCampaignStatus string

const (
	HelpCampaignStatusActive    HelpCampaignStatus = "active"    // 进行中
	HelpCampaignStatusCompleted HelpCampaignStatus = "completed" // 已完成 （发起人领了券之后从active更新为completed）
	HelpCampaignStatusExpired   HelpCampaignStatus = "expired"   // 已过期
)

// HelpCampaign represents a help campaign record.
type HelpCampaign struct {
	ID                 string                   `json:"id"`
	InitiatorID        string                   `json:"initiator_id"`
	TargetCount        int                      `json:"target_count"`
	CurrentCount       int                      `json:"current_count"`
	Status             HelpCampaignStatus       `json:"status"`
	ShareURL           string                   `json:"share_url"`
	CreatedAt          int64                    `json:"created_at"` // unix timestamp
	UpdatedAt          int64                    `json:"updated_at"` // unix timestamp
	ExpiresAt          int64                    `json:"expires_at"` // unix timestamp
	InitiatorAvatar    string                   `json:"initiator_avatar"`
	InitiatorNickname  string                   `json:"initiator_nickname"`
	HelpRecords        []map[string]interface{} `json:"help_records"`
	ViewerHasHelped    bool                     `json:"viewer_has_helped"`
	ViewerRewardStatus int                      `json:"viewer_reward_status"`
	TodayHelpedOthers  bool                     `json:"today_helped_others"` // 发起助力页面（今日是否助力过别人)
	// Title              string                   `json:"title"`
	// Description        string                   `json:"description"`
	// RewardAmount       int                      `json:"reward_amount"` // in cents
}

// HelpAssist represents a help assist record.
type HelpAssist struct {
	ID           string `json:"id"`
	CampaignID   string `json:"campaign_id"`
	AssistantID  string `json:"assistant_id"`
	RewardAmount int    `json:"reward_amount"` // in cents
	CreatedAt    int64  `json:"created_at"`    // unix timestamp
}

// HelpCampaignStats represents daily statistics for help campaigns.
type HelpCampaignStats struct {
	ID                     string `json:"id"`
	Date                   int64  `json:"date"`                     // unix timestamp (milliseconds) for the date
	DateString             string `json:"date_string"`              // YYYY-MM-DD format for readability
	InitiatedCount         int    `json:"initiated_count"`          // Number of campaigns initiated
	HelpedCount            int    `json:"helped_count"`             // Number of help assists
	CampaignCompletedCount int    `json:"campaign_completed_count"` // Number of campaigns completed
	CreatedAt              int64  `json:"created_at"`               // unix timestamp
	UpdatedAt              int64  `json:"updated_at"`               // unix timestamp
}
