package redis

import (
	"context"
	"os"
	"sync"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"tandian-server/internal/config"
)

var (
	rdb  *redis.Client
	once sync.Once
)
var ctx = context.Background()

// Nil is a re-export of redis.Nil from github.com/redis/go-redis/v9.
var Nil = redis.Nil

func Init() {
	once.Do(func() {
		// Initialize Redis client using loaded config
		rdb = redis.NewClient(&redis.Options{
			Addr:     config.AppConfig.Redis.Addr,
			Password: config.AppConfig.Redis.Password,
			DB:       config.AppConfig.Redis.DB,
		})

		// Ping to check connection
		_, err := rdb.Ping(ctx).Result()
		if err != nil {
			zap.L().Fatal("Could not connect to Redis", zap.Error(err))
			os.Exit(1)
		}
		// fmt.Println("Successfully connected to Red<PERSON>!")
		zap.L().
			Info("Successfully connected to Redis", zap.String("addr", config.AppConfig.Redis.Addr), zap.Int("db", config.AppConfig.Redis.DB))
	})
}

// GetClient returns the global Redis client instance.
func GetClient() *redis.Client {
	return rdb
}
