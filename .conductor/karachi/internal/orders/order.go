package orders

import (
	"context"

	"go.uber.org/zap"

	"tandian-server/internal/logger"
	"tandian-server/internal/util/cloudbase"
)

func ValidateStock(ctx context.Context, promotionID string) bool {
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":               true,
			"name":              true,
			"realtime_quantity": true,
			"promotion_type":    true,
			"is_chain":          true,
		},
	}
	log := logger.Get("app")
	res, err := cloudbase.GetItem(ctx, "promotions", q)
	if err != nil {
		log.Error("failed to get item from cloudbase", zap.Error(err))
		return false
	}

	if len(res) == 0 {
		log.Warn("promotion not found when validating stock", zap.String("promotionID", promotionID))
		return false
	}

	// 特价团库存如果是品牌连锁看总库存
	if res["promotion_type"] == "ykj" { //nolint:staticcheck // SA9003: TODO - implement brand chain stock check
		// TODO: Implement logic for checking total stock for brand chains
	}

	// 探店库存看realtime_quantity
	if res["promotion_type"] == "tandian1" {
		if res["realtime_quantity"].(int) < 0 {
			// 库存不足，无法下单
			return false
		}
	}

	return true
}

func CreateOrder() {
}
