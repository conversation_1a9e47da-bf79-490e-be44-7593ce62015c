package pay

import (
	"context"
	"fmt"
	"time"

	"github.com/dfang/go-prettyjson"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"

	"tandian-server/internal/model"
	"tandian-server/internal/util/cloudbase"
)

// Methods on WechatPayClient for backward compatibility.
func (w *WechatPayClient) QueryOrderByOrderNo(ctx context.Context, orderNo string) (*payments.Transaction, error) {
	svc := jsapi.JsapiApiService{Client: w.Client}
	resp, _, err := svc.QueryOrderByOutTradeNo(ctx,
		jsapi.QueryOrderByOutTradeNoRequest{
			OutTradeNo: core.String(orderNo),
			Mchid:      core.String(w.MchID),
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to query order by out trade no: %w", err)
	}

	return resp, nil
}

func (w *WechatPayClient) QueryOrderByTransactionId(
	ctx context.Context,
	transactionId string,
) (*payments.Transaction, error) {
	svc := jsapi.JsapiApiService{Client: w.Client}
	resp, _, err := svc.QueryOrderById(ctx,
		jsapi.QueryOrderByIdRequest{
			TransactionId: core.String(transactionId),
			Mchid:         core.String(w.MchID),
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to query order by transaction id: %w", err)
	}

	return resp, nil
}

// Standalone functions for order operations (similar to coupon.go pattern)

// QueryOrder queries an order by either order number or transaction ID.
func QueryOrder(ctx context.Context, orderNo, transactionId string) (*payments.Transaction, error) {
	client := NewWechatPayClient()

	if orderNo != "" {
		return client.QueryOrderByOrderNo(ctx, orderNo)
	} else if transactionId != "" {
		return client.QueryOrderByTransactionId(ctx, transactionId)
	}

	return nil, fmt.Errorf("either order_no or transaction_id must be provided")
}

// RefundOrder processes a refund for an order.
func RefundOrder(ctx context.Context, req RefundRequest) (*refunddomestic.Refund, error) {
	client := NewWechatPayClient()
	return client.Refund(ctx, req)
}

// VerifyOrder marks an order as verified in the database.
func VerifyOrder(ctx context.Context, orderNo, verifyMethod string) error {
	// Validate verify method
	if verifyMethod != "1" && verifyMethod != "2" {
		return fmt.Errorf("invalid verify_method. Must be '1' (user instant) or '2' (merchant scan)")
	}

	// Create the update payload for CloudBase - query by order_no
	updatePayload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"order_no": map[string]interface{}{
					"$eq": orderNo,
				},
			},
		},
		"data": map[string]interface{}{
			"status":        model.OrderStatusVerified, // 已核销状态
			"verifiedAt":    time.Now().Unix() * 1000,  // CloudBase uses milliseconds
			"verify_method": verifyMethod,              // 1: 用户端点立即核销, 2: 商家端扫码核销
		},
	}

	// Use CloudBase client to update the order
	result, err := cloudbase.UpdateItem(ctx, "orders", updatePayload)
	if err != nil {
		return fmt.Errorf("failed to verify order: %w", err)
	}

	// Check update count
	count := 0
	if c, ok := result["count"].(int); ok {
		count = c
	} else if c, ok := result["count"].(float64); ok {
		count = int(c)
	}

	if count == 0 {
		return fmt.Errorf("order %s not found or already verified", orderNo)
	}

	orderQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"order_no": map[string]interface{}{
					"$eq": orderNo,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":        true,
			"order_no":   true,
			"order_type": true,
		},
	}
	orderResult, err := cloudbase.GetItem(ctx, "orders", orderQuery)
	if err == nil && len(orderResult) > 0 {
		if orderID, ok := orderResult["_id"].(string); ok {
			// Check order type
			if orderType, ok := orderResult["order_type"].(string); ok {
				if orderType == "ykj" {
					result, err := cloudbase.CallFunction(ctx, "calc_commission", map[string]interface{}{
						"order_id": orderID,
					})
					if err != nil {
						return err
					}
					prettyjson.Printf("cloudbase.CallFunction(calc_commission): %s\n", result)
				}
			}
		}
	}

	return nil
}

// QueryRefund queries a refund by refund number.
func QueryRefund(ctx context.Context, outRefundNo string) (*refunddomestic.Refund, error) {
	client := NewWechatPayClient()
	return client.QueryByOutRefundNo(ctx, QueryByRefundNoRequest{
		OutRefundNo: outRefundNo,
	})
}
