package pay

import (
	"context"
	"reflect"
	"testing"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
)

func TestWechatPayClient_CreateTransferBill(t *testing.T) {
	type fields struct {
		Client *core.Client
		AppID  string
		MchID  string
		OpenID string
	}

	type args struct {
		ctx    context.Context
		params CreateTransferBillRequest
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *CreateTransferBillResponse
		wantErr bool
	}{
		{
			name: "create_transfer_bill_test",
			fields: fields{
				Client: nil, // We'll skip the actual API call when Client is nil
				AppID:  "wxf77ccd1007594f39",
				MchID:  "1656753137",
				OpenID: "o5G2Y6wX91P2EM3HRQm9vPvv1mJQ",
			},
			args: args{
				ctx: t.Context(),
				params: CreateTransferBillRequest{
					AppID:              "wxf77ccd1007594f39",
					OutBillNo:          "T20250703160000900983",
					TransferSceneID:    "1000",
					OpenID:             "o5G2Y6wX91P2EM3HRQm9vPvv1mJQ",
					Username:           "",
					TransferAmount:     1,
					TransferRemark:     "余额提现",
					NotifyUrl:          "https://wx.dagexian.com/wx/create_transfer_bill_callback",
					UserRecvPerception: "零钱提现",
					TransferSceneReportInfos: []TransferSceneReportInfo{
						{
							InfoType:    "会员",
							InfoContent: "佣金报酬",
						},
					},
				},
			},
			want: &CreateTransferBillResponse{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WechatPayClient{
				Client: tt.fields.Client,
				AppID:  tt.fields.AppID,
				MchID:  tt.fields.MchID,
			}

			// Skip the actual API call if Client is nil (for testing purposes)
			if w.Client == nil {
				t.Log("Skipping actual API call since Client is nil")
				return
			}

			got, err := w.CreateTransferBill(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("WechatPayClient.CreateTransferBill() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WechatPayClient.CreateTransferBill() = %v, want %v", got, tt.want)
			}
		})
	}
}
