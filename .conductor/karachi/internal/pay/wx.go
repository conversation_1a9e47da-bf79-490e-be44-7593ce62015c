// docs/wx_callback_examples.md 微信回调通知示例
// https://pay.weixin.qq.com/doc/v3/merchant/4012791861
// https://pay.weixin.qq.com/doc/v3/merchant/4012791865
// https://pay.weixin.qq.com/doc/v3/merchant/4012712115
package pay

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/dfang/go-prettyjson"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"

	"tandian-server/internal/config"
	"tandian-server/internal/model"
	"tandian-server/internal/util"
	"tandian-server/internal/util/cloudbase"
)

type WechatPayClient struct {
	Client *core.Client
	AppID  string
	MchID  string
}

type TransferBillResponse struct {
	MchID          string    `json:"mch_id"`
	OutBillNo      string    `json:"out_bill_no"`
	TransferBillNo string    `json:"transfer_bill_no"`
	Appid          string    `json:"appid"`
	State          string    `json:"state"`
	TransferAmount int       `json:"transfer_amount"`
	TransferRemark string    `json:"transfer_remark"`
	FailReason     string    `json:"fail_reason"`
	Openid         string    `json:"openid"`
	UserName       string    `json:"user_name"`
	CreateTime     time.Time `json:"create_time"`
	UpdateTime     time.Time `json:"update_time"`
}

type CancelTransferBillResponse struct {
	OutBillNo      string `json:"out_bill_no"`
	TransferBillNo string `json:"transfer_bill_no"`
	State          string `json:"state"`
	UpdateTime     string `json:"update_time"`
}

func NewWechatPayClient() *WechatPayClient {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(config.AppConfig.WechatPay.PrivateKeyPath)
	if err != nil {
		log.Fatal("load merchant private key error")
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取 platformCertificates 以及发起 HTTP 请求的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(
			config.AppConfig.WechatPay.MchID,
			config.AppConfig.WechatPay.MchCertificateSerialNumber,
			mchPrivateKey,
			config.AppConfig.WechatPay.MchAPIv3Key,
		),
	}

	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Fatalf("new wechat pay client err:%s", err)
	}

	return &WechatPayClient{
		Client: client,
		AppID:  config.AppConfig.WechatPay.AppID,
		MchID:  config.AppConfig.WechatPay.MchID,
	}
}

func (w *WechatPayClient) ParseNotifyCallbackMessage(
	ctx context.Context,
	payload *notify.Request,
	req *http.Request,
) error {
	// Get WechatPay config
	wechatPayConfig := config.AppConfig.WechatPay
	// 1. Initialize the notification handler
	// ctx := context.Background()
	// Get the certificate visitor
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(wechatPayConfig.MchID)
	// Create the verifier
	verifier := verifiers.NewSHA256WithRSAVerifier(certificateVisitor)

	handler, err := notify.NewRSANotifyHandler(wechatPayConfig.MchAPIv3Key, verifier)
	if err != nil {
		log.Printf("Failed to create notify handler: %v", err)
	}

	switch payload.EventType {
	case "TRANSACTION.SUCCESS":
		content := payments.Transaction{} // 支付成功 回调
		// Parse the notification
		notifyReq, err := handler.ParseNotifyRequest(ctx, req, &content)
		if err != nil {
			log.Printf("Failed to parse notification: %v", err)
		}

		prettyjson.Printf("notifyReq: %s\n", notifyReq)
		prettyjson.Printf("decoded from ciphertext: %s\n", content)

		return HandlePaymentNotifyCallback(ctx, &content)
	case "REFUND.SUCCESS":
		// 对于没有定义的结构体，可以用 map[string]interface{}
		// https://github.com/wechatpay-apiv3/wechatpay-go/issues/103#issuecomment-1118113306
		content := make(map[string]interface{}) // 退款成功 回调
		// Parse the notification
		notifyReq, err := handler.ParseNotifyRequest(ctx, req, &content)
		if err != nil {
			log.Printf("Failed to parse notification: %v", err)
		}

		prettyjson.Printf("notifyReq: %s\n", notifyReq)
		prettyjson.Printf("decoded from ciphertext: %s\n", content)

		// Safely check refund_status
		refundStatus, ok := content["refund_status"].(string)
		if !ok {
			log.Printf("refund_status is not a string or not exists")
			return nil
		}

		if ok && refundStatus == "SUCCESS" {
			// Safely get out_refund_no and out_trade_no
			outRefundNo, ok := content["out_refund_no"].(string)
			if !ok {
				log.Printf("out_refund_no is not a string or not exists")
				return nil
			}
			outTradeNo, ok := content["out_trade_no"].(string)
			if !ok {
				log.Printf("out_trade_no is not a string or not exists")
				return nil
			}

			if outRefundNo != "" && outTradeNo != "" {
				return HandleRefundNotifySuccessCallback(ctx, outTradeNo, outRefundNo)
			}
			log.Printf("Missing out_refund_no or out_trade_no in refund callback")
		}

		return nil
	case "MCHTRANSFER.BILL.FINISHED": // 只是商家转账单据终态通知， "state": "CANCELED", "SUCCESS" 都有可能
		content := TransferBillCallbackResponse{}

		// Parse the notification
		notifyReq, err := handler.ParseNotifyRequest(ctx, req, &content)
		if err != nil {
			log.Printf("Failed to parse notification: %v", err)
		}

		prettyjson.Printf("notifyReq: %s\n", notifyReq)
		prettyjson.Printf("decoded from ciphertext: %s\n", content)

		if content.State == "SUCCESS" {
			return HandleTransferBillNotifySuccessCallback(ctx, &content)
		}

		if content.State == "CANCELED" { //nolint:staticcheck // SA9003: TODO - handle cancellation logic
			// TODO: Handle order cancellation
		}
		// return HandleTransferBillNotifySuccessCallback(ctx, &content)
	}

	return nil
}

// HandlePaymentNotifyCallback 支付成功回调通知 https://pay.weixin.qq.com/doc/v3/merchant/4012791861
func HandlePaymentNotifyCallback(ctx context.Context, trans *payments.Transaction) error {
	// Validate the transaction
	if trans == nil || trans.OutTradeNo == nil {
		return errors.New("invalid transaction data")
	}

	// Safely build log message with nil checks
	outTradeNo := ""
	transactionId := ""
	tradeState := ""
	payerOpenid := ""

	if trans.OutTradeNo != nil {
		outTradeNo = *trans.OutTradeNo
	}
	if trans.TransactionId != nil {
		transactionId = *trans.TransactionId
	}
	if trans.TradeState != nil {
		tradeState = *trans.TradeState
	}
	if trans.Payer != nil && trans.Payer.Openid != nil {
		payerOpenid = *trans.Payer.Openid
	}

	// Log the transaction details for debugging
	log.Printf("Payment success callback - OutTradeNo: %s, TransactionId: %s, TradeState: %s, Payer Openid: %s",
		outTradeNo, transactionId, tradeState, payerOpenid)

	// Only process successful payments
	if trans.TradeState == nil || *trans.TradeState != "SUCCESS" {
		return fmt.Errorf("payment not successful, state: %v", trans.TradeState)
	}

	// step1: Update order status in database to "paid"
	updateData := map[string]interface{}{
		"status": model.OrderStatusPaid,
		"paidAt": time.Now().Unix() * 1000, // CloudBase uses milliseconds
	}

	// Only add transaction_id if it's not nil
	if trans.TransactionId != nil {
		updateData["transaction_id"] = *trans.TransactionId
	}

	updatePayload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"order_no": map[string]interface{}{
					"$eq": *trans.OutTradeNo,
				},
			},
		},
		"data": updateData,
	}

	_, err := cloudbase.UpdateItem(ctx, "orders", updatePayload)
	if err != nil {
		return fmt.Errorf("failed to update order status in database: %w", err)
	}

	// Check if we have all required fields for EnqueueRedeemCouponCodeTask
	if trans.TransactionId == nil {
		log.Printf("Warning: TransactionId is nil, skipping redeem coupon task")
		return nil
	}
	if trans.Payer == nil || trans.Payer.Openid == nil {
		log.Printf("Warning: Payer or Openid is nil, skipping redeem coupon task")
		return nil
	}

	// step2: redeem coupon if used coupon when create order
	log.Printf("enqueue redeem coupon code task in TRANSACTION.SUCCESS callback")
	err = EnqueueRedeemCouponCodeTask(*trans.OutTradeNo, *trans.TransactionId, *trans.Payer.Openid)
	if err != nil {
		log.Print(err)
		return err
	}

	// Extract order_id from attach field if available
	if trans.Attach != nil && *trans.Attach != "" {
		log.Printf("Payment attach data: %s", *trans.Attach)
		// The attach field contains "order_id:xxxxx" as set in prepay
	}

	// TODO: Additional business logic after successful payment
	// - Send notification to user
	// - Update promotion sales count
	// - Generate commission records
	// - Send notification to shop owner

	log.Printf("Order %s marked as paid successfully", *trans.OutTradeNo)

	return nil
}

// HandleRefundNotifySuccessCallback 退款成功回调通知 https://pay.weixin.qq.com/doc/v3/merchant/4012791865
func HandleRefundNotifySuccessCallback(ctx context.Context, outTradeNo, outRefundNo string) error {
	if outTradeNo == "" || outRefundNo == "" {
		return errors.New("invalid refund data: outTradeNo or outRefundNo is empty")
	}

	log.Printf("Refund success callback - OutTradeNo: %s, OutRefundNo: %s", outTradeNo, outRefundNo)

	// step1: Update order status in database to "refunded"
	updatePayload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"order_no": map[string]interface{}{
					"$eq": outTradeNo,
				},
			},
		},
		"data": map[string]interface{}{
			"status":          model.OrderStatusRefunded,
			"refund_order_no": outRefundNo,
			"refundedAt":      time.Now().Unix() * 1000, // CloudBase uses milliseconds
		},
	}

	_, err := cloudbase.UpdateItem(ctx, "orders", updatePayload)
	if err != nil {
		return fmt.Errorf("failed to update order refund status in database: %w", err)
	}

	log.Printf("Order %s marked as refunded successfully with refund no: %s", outTradeNo, outRefundNo)

	// step2: enqueue return coupon code task if used coupon for the order
	orderQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"order_no": map[string]interface{}{
					"$eq": outTradeNo,
				},
				// "order_type": map[string]interface{}{
				// 	"$eq": "tandian1",
				// },
			},
		},
		"select": map[string]interface{}{
			"coupon_code":     true,
			"coupon_stock_id": true,
			"order_no":        true,
			"order_type":      true,
			"p_id":            true,
			"createdAt":       true,
		},
	}

	orderData, err := cloudbase.GetItem(ctx, "orders", orderQuery)
	if err != nil {
		log.Printf("Failed to get order: %v", err)
		return err
	}
	if len(orderData) == 0 {
		log.Printf("Order with order_no %s and order_type tandian1 not found, skipping coupon return.", outTradeNo)
		return nil
	}

	// Check if coupon_code and coupon_stock_id exist before attempting to use them
	couponCode, hasCouponCode := orderData["coupon_code"].(string)
	couponStockId, hasCouponStockId := orderData["coupon_stock_id"].(string)

	// Only enqueue return coupon task if both fields exist and are valid
	if hasCouponCode && hasCouponStockId && couponCode != "" && couponStockId != "" {
		log.Printf("enqueue return coupon code task in REFUND.SUCCESS callback")

		err = EnqueueReturnCouponCodeTask(couponCode, couponStockId, "")
		if err != nil {
			log.Print(err)
			return err
		}
	} else {
		log.Printf("Order %s does not have valid coupon information, skipping coupon return task", outTradeNo)
	}

	// step3: restore stock if tandian order and DAY(createdAt) = DAY(refundedAt)
	promotionID, ok := orderData["p_id"].(string)
	if !ok {
		log.Printf("p_id is not a string or not exists")
		return nil
	}
	orderType, ok := orderData["order_type"].(string)
	if !ok {
		log.Printf("order_type is not a string or not exists")
		return nil
	}
	createdAtFloat, ok := orderData["createdAt"].(float64)
	if !ok {
		log.Printf("createdAt is not a float64 or not exists")
		return nil
	}
	createdAt := int64(createdAtFloat)

	if orderType == "tandian1" {
		if util.IsOrderCreatedToday(createdAt) {
			incrementTandianInventory(ctx, promotionID)
		}
	}

	return nil
}

// HandleTransferBillNotifySuccessCallback 商家转账回调通知 https://pay.weixin.qq.com/doc/v3/merchant/4012712115
func HandleTransferBillNotifySuccessCallback(ctx context.Context, req *TransferBillCallbackResponse) error {
	return UpdateBalanceAfterWithdrawal(ctx, req)
}

// TransferBillCallbackResponse represents the structured data from a transfer bill callback.
type TransferBillCallbackResponse struct {
	// Notification metadata
	ID               string                 `json:"id"`
	CreateTime       string                 `json:"create_time"`
	ResourceType     string                 `json:"resource_type"`
	EventType        string                 `json:"event_type"`
	Summary          string                 `json:"summary"`
	ResourceOriginal map[string]interface{} `json:"-"` // Original resource data

	// Transfer bill specific fields
	OutBillNo      string `json:"out_bill_no"`
	TransferBillNo string `json:"transfer_bill_no"`
	State          string `json:"state"`
	MchID          string `json:"mch_id"`
	TransferAmount int64  `json:"transfer_amount"`
	OpenID         string `json:"openid"`
	FailReason     string `json:"fail_reason"`
	// CreateTime     string `json:"create_time"`
	UpdateTime     string `json:"update_time"`
	AppID          string `json:"appid"`
	UserName       string `json:"user_name"`
	TransferRemark string `json:"transfer_remark"`
}
