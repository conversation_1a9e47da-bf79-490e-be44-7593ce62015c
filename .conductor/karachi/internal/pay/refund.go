package pay

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"

	"tandian-server/internal/model"
	"tandian-server/internal/util/cloudbase"
)

type RefundRequest struct {
	TransactionId string `json:"transaction_id"`
	OutTradeNo    string `json:"out_trade_no"`
	OutRefundNo   string `json:"out_refund_no"`
	Reason        string `json:"reason"`
	NotifyUrl     string `json:"notify_url"`
	Amount        int64  `json:"amount"`
	TotalAmount   int64  `json:"total_amount"` // Original order total amount
	From          string `json:"from"`         // from: miniapp, cli, admin
	Force         bool   `json:"force"`        // 是否强制退款
}

type QueryByRefundNoRequest struct {
	OutRefundNo string
}

func (w *WechatPayClient) Refund(ctx context.Context, req RefundRequest) (*refunddomestic.Refund, error) {
	// First, query the order from database to check order type and status
	var orderQuery map[string]any
	switch {
	case req.OutTradeNo != "":
		orderQuery = map[string]any{
			"select": map[string]any{
				"order_type":     true,
				"status":         true,
				"order_no":       true,
				"transaction_id": true,
				"total_amount":   true,
				"paid_amount":    true,
			},
			"filter": map[string]any{
				"where": map[string]any{
					"order_no": map[string]any{
						"$eq": req.OutTradeNo,
					},
				},
			},
		}
	case req.TransactionId != "":
		orderQuery = map[string]any{
			"select": map[string]any{
				"order_type":     true,
				"status":         true,
				"order_no":       true,
				"transaction_id": true,
				"total_amount":   true,
				"paid_amount":    true,
			},
			"filter": map[string]any{
				"where": map[string]any{
					"transaction_id": map[string]any{
						"$eq": req.TransactionId,
					},
				},
			},
		}
	default:
		return nil, errors.New("either out_trade_no or transaction_id must be provided")
	}

	// Query order from database
	order, err := cloudbase.GetItem(ctx, "orders", orderQuery)
	if err != nil {
		return nil, fmt.Errorf("failed to query order from database: %w", err)
	}

	// Check if order exists
	if len(order) == 0 {
		return nil, errors.New("order not found")
	}

	if !req.Force {
		// Check order type - only allow refund for "tandian1" orders
		orderType, _ := order["order_type"].(string)
		if orderType != "tandian1" {
			return nil, errors.New("只能退款探店订单，特价团订单不支持退款")
		}

		// Check order status - only allow refund for pending verification (10) or expired (11) orders
		status, _ := order["status"].(string)
		if status != string(model.OrderStatusPaid) && status != string(model.OrderStatusExpired) {
			return nil, errors.New("只能退款待核销或已过期的订单，当前订单状态不允许退款")
		}
	}

	svc := refunddomestic.RefundsApiService{Client: w.Client}

	var reason string
	if req.Reason != "" {
		reason = req.Reason
	} else {
		reason = "商品已下架"
	}

	// Generate out_refund_no if not provided
	outRefundNo := req.OutRefundNo
	if outRefundNo == "" {
		// Generate a unique refund number using timestamp and random suffix
		outRefundNo = fmt.Sprintf("REFUND_%d_%s", time.Now().Unix(), generateRandomString(6))
	}

	// If amount is not provided, query the order to get the total amount
	refundAmount := req.Amount
	totalAmount := req.TotalAmount

	if refundAmount == 0 || totalAmount == 0 {
		// Query order to get amount information from WeChat Pay
		var transaction *payments.Transaction

		var queryErr error

		if req.OutTradeNo != "" {
			transaction, queryErr = w.QueryOrderByOrderNo(ctx, req.OutTradeNo)
		} else if req.TransactionId != "" {
			transaction, queryErr = w.QueryOrderByTransactionId(ctx, req.TransactionId)
		}

		if queryErr != nil {
			return nil, fmt.Errorf("failed to query order for refund amount: %w", queryErr)
		}

		if transaction == nil || transaction.Amount == nil || transaction.Amount.Total == nil {
			return nil, errors.New("unable to determine order amount for refund")
		}

		// If total amount not provided, use the order's total amount
		if totalAmount == 0 {
			totalAmount = *transaction.Amount.Total
		}

		// If refund amount not provided, refund the full amount
		if refundAmount == 0 {
			refundAmount = *transaction.Amount.Total
		}
	}

	// Create refund request - only include non-empty transaction ID or out trade no
	refundReq := refunddomestic.CreateRequest{
		OutRefundNo:  core.String(outRefundNo),
		Reason:       core.String(reason),
		NotifyUrl:    core.String("https://dgx.dagexian.com/wx/notify_callback"),
		FundsAccount: refunddomestic.REQFUNDSACCOUNT_AVAILABLE.Ptr(),
		Amount: &refunddomestic.AmountReq{
			Currency: core.String("CNY"),
			Refund:   core.Int64(refundAmount), // 退款金额，币种的最小单位，只能为整数，不能超过原订单支付金额。
			Total:    core.Int64(totalAmount),  // 原支付交易的订单总金额，币种的最小单位，只能为整数。
		},
	}

	// Set either TransactionId or OutTradeNo based on what was provided
	if req.TransactionId != "" {
		refundReq.TransactionId = core.String(req.TransactionId)
	} else if req.OutTradeNo != "" {
		refundReq.OutTradeNo = core.String(req.OutTradeNo)
	}

	resp, result, err := svc.Create(ctx, refundReq)
	if err != nil {
		// 处理错误
		log.Printf("call Create err:%s", err)
		return nil, err
	}

	// 处理返回结果
	log.Printf("status=%d resp=%s", result.Response.StatusCode, resp)

	// If refund successful, update order in database
	if resp != nil && resp.Status != nil && (*resp.Status == "SUCCESS" || *resp.Status == "PROCESSING") {
		// Use the same orderQuery from above for consistency
		// Update the order with refund information
		updatePayload := map[string]any{
			"filter": orderQuery["filter"],
			"data": map[string]any{
				"refund_order_no": outRefundNo,
				"refundedAt":      time.Now().UnixMilli(),
				// "refund_amount":   float64(refundAmount) / 100, // Convert cents to yuan
				"status": model.OrderStatusRefunded,
			},
		}

		_, updateErr := cloudbase.UpdateItem(ctx, "orders", updatePayload)
		if updateErr != nil {
			// Log the error but don't fail the refund since it was already processed
			log.Printf("failed to update order with refund info: %v", updateErr)
		}
	}

	return resp, nil
}

func (w *WechatPayClient) QueryByOutRefundNo(
	ctx context.Context,
	req QueryByRefundNoRequest,
) (*refunddomestic.Refund, error) {
	svc := refunddomestic.RefundsApiService{Client: w.Client}
	resp, result, err := svc.QueryByOutRefundNo(ctx,
		refunddomestic.QueryByOutRefundNoRequest{
			OutRefundNo: core.String("1217752501201407033233368018"),
			// SubMchid:    core.String("1900000109"),	// 子商户的商户号，由微信支付生成并下发。服务商模式下必须传递此参数
		},
	)

	if err != nil {
		// 处理错误
		log.Printf("call QueryByOutRefundNo err:%s", err)
	} else {
		// 处理返回结果
		log.Printf("status=%d resp=%s", result.Response.StatusCode, resp)
	}

	return resp, nil
}

func generateRandomString(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}

	return string(b)
}
