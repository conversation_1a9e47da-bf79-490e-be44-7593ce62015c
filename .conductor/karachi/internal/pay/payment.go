package pay

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/Oudwins/zog"
	"github.com/dfang/go-prettyjson"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/merchantexclusivecoupon"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"

	"tandian-server/internal/config"
	"tandian-server/internal/model"
	"tandian-server/internal/util"
	"tandian-server/internal/util/cloudbase"
)

type PrepayRequest struct {
	GoodsName       string
	MerchantGoodsId string
	Description     string
	OutTradeNo      string `json:"out_trade_no" zog:"out_trade_no"`
	Total           int64
	OpenId          string `json:"openid"`
	// NotifyUrl       string
	Attach     string
	GoodsTag   string
	LimitPay   []string
	Detail     *jsapi.Detail
	SceneInfo  *jsapi.SceneInfo
	SettleInfo *jsapi.SettleInfo
}

var UnifiedOrderRequestSchema = zog.Struct(zog.Shape{
	"ReferrerID":   zog.String().Optional(),
	"UserID":       zog.String().Required(zog.Message("user_id is required")),
	"PromotionID":  zog.String().Required(zog.Message("promotion_id is required")),
	"ShopID":       zog.String().Required(zog.Message("shop_id is required")),
	"PID":          zog.String().Required(zog.Message("p_id is required")),
	"OrderType":    zog.String().Required(zog.Message("order_type is required")),
	"TotalAmount":  zog.Float64().GT(0, zog.Message("total_amount must be greater than 0")),
	"PaidAmount":   zog.Float64().GTE(0, zog.Message("paid_amount must be non-negative")),
	"RefundAmount": zog.Float64().GTE(0, zog.Message("refund_amount must be non-negative")),
	"Commission":   zog.Float64().GTE(0, zog.Message("commission must be non-negative")),
	"OrderNo":      zog.String().Required(zog.Message("order_no is required")),
	"OpenID":       zog.String().Required(zog.Message("openid is required for WeChat payment")),
})

var PrepayRequestSchema = zog.Struct(zog.Shape{
	// its very important that schema keys like "name" match the struct field name NOT the input data
	"OpenId":      zog.String().Required(zog.Message("支付用户必须")),
	"OutTradeNo":  zog.String().Required(zog.Message("订单号必须")),
	"Description": zog.String().Required(),
	"Attach":      zog.String().Required(),
})

type CloseOrderRequest struct {
	OutTradeNo    string `json:"out_trade_no"`
	PromotionID   string `json:"promotion_id"`
	PromotionType string `json:"promotion_type"`
	IsChain       bool   `json:"is_chain"`
	ProductID     string `json:"product_id"`
	ShopID        string `json:"shop_id"`
	CreatedAt     int64  `json:"created_at"`
}

// Prepay https://pay.weixin.qq.com/doc/v3/merchant/4012791856
func (w *WechatPayClient) Prepay(ctx context.Context, req PrepayRequest) (*jsapi.PrepayResponse, error) {
	svc := jsapi.JsapiApiService{Client: w.Client}

	resp, result, err := svc.Prepay(ctx,
		jsapi.PrepayRequest{
			Appid:         core.String(config.AppConfig.WechatPay.AppID),
			Mchid:         core.String(config.AppConfig.WechatPay.MchID),
			OutTradeNo:    core.String(req.OutTradeNo),
			TimeExpire:    core.Time(time.Now().Add(time.Minute * 5)),
			Description:   core.String(req.Description),
			Attach:        core.String(req.Attach),
			NotifyUrl:     core.String("https://dgx.dagexian.com/wx/notify_callback"),
			GoodsTag:      core.String(""), // 商品标记，代金券或立减优惠功能的参数。
			LimitPay:      []string{""},    //  prompt: 创建预支付单时 LimitPay no_credit ​禁止使用信用卡，用户只能使用余额或借记卡支付
			SupportFapiao: core.Bool(false),
			Amount: &jsapi.Amount{
				Currency: core.String("CNY"),
				Total:    core.Int64(req.Total),
			},
			Payer: &jsapi.Payer{
				Openid: core.String(req.OpenId),
			},
			// TODO: 不传会怎样?
			// Detail: &jsapi.Detail{ // Detail 优惠功能
			// 	CostPrice: core.Int64(608800),
			// 	GoodsDetail: []jsapi.GoodsDetail{ // prompt: 创建预支付单时 WechatpayGoodsId 和 MerchantGoodsId分别是什么
			// 		jsapi.GoodsDetail{
			// 			GoodsName:       core.String(req.GoodsName),
			// 			MerchantGoodsId: core.String(req.MerchantGoodsId), // 商户系统内部的商品唯一标识（由商户自定义）
			// 			Quantity:        core.Int64(1),
			// 			UnitPrice:       core.Int64(828800),
			// 			// WechatpayGoodsId: core.String("1001"), // 微信支付定义的统一商品编号（非必需，仅特定场景使用）由微信支付分配，通常用于 ​微信侧已有备案的商品​（如虚拟商品、特定行业商品）
			// 		}},
			// 	InvoiceId: core.String("wx123"),
			// },
			// SceneInfo 支付场景描述 后续可能考虑
			// SceneInfo: &jsapi.SceneInfo{
			// 	DeviceId:      core.String("013467007045764"),
			// 	PayerClientIp: core.String("*************"),
			// 	StoreInfo: &jsapi.StoreInfo{
			// 		Address:  core.String("广东省深圳市南山区科技中一道10000号"),
			// 		AreaCode: core.String("440305"),
			// 		Id:       core.String("0001"),
			// 		Name:     core.String("腾讯大厦分店"),
			// 	},
			// },
			// 是否指定分账
			SettleInfo: &jsapi.SettleInfo{
				ProfitSharing: core.Bool(false),
			},
		},
	)

	if err != nil {
		// 处理错误
		log.Printf("call Prepay err:%s", err)
	} else {
		// 处理返回结果
		log.Printf("status=%d resp=%s", result.Response.StatusCode, resp)
	}

	return resp, err
}

// UnifiedOrder https://pay.weixin.qq.com/doc/v3/merchant/4012791898
func (w *WechatPayClient) UnifiedOrder(ctx context.Context, prepayID string) (UnifiedOrderResponse, error) {
	timeStamp := GenerateTimeStamp()

	nonceStr, err := utils.GenerateNonce()
	if err != nil {
		log.Fatal(err)
	}
	// prepayID := ""
	packageStr := fmt.Sprintf("prepay_id=%s", prepayID)
	// message := fmt.Sprintf("%s\n%s\n%s\n%s\n", appID, fmt.Sprintf("%v", timeStamp), nonceStr, packageStr)
	// appId、timeStamp、nonceStr、package
	message := fmt.Sprintf("%s\n%s\n%s\n%s\n", config.AppConfig.WechatPay.AppID, timeStamp, nonceStr, packageStr)

	paySign, err := w.Client.Sign(ctx, message) // use utils.SignSHA256WithRSA under the hood
	if err != nil {
		log.Fatal(err)
	}

	resp := UnifiedOrderResponse{
		Timestamp: timeStamp,
		NonceStr:  nonceStr,
		Package:   packageStr,
		SignType:  "RSA",
		PaySign:   paySign.Signature,
	}

	return resp, nil
}

// CloseOrder https://pay.weixin.qq.com/doc/v3/merchant/4012791901
func (w *WechatPayClient) CloseOrder(ctx context.Context, req CloseOrderRequest) error {
	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("promotion_lock:%s", req.PromotionID)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// step1: close order
	svc := jsapi.JsapiApiService{Client: w.Client}
	result, err := svc.CloseOrder(ctx,
		jsapi.CloseOrderRequest{
			OutTradeNo: core.String(req.OutTradeNo),
			Mchid:      core.String(config.AppConfig.WechatPay.MchID),
		},
	)
	if err != nil {
		// 处理错误
		log.Printf("call CloseOrder err:%s", err)
		return err
	} else {
		// 处理返回结果
		log.Printf("status=%d", result.Response.StatusCode)
	}

	// step2: Update order status in database to "canceled"
	updatePayload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"order_no": map[string]interface{}{
					"$eq": req.OutTradeNo,
				},
			},
		},
		"data": map[string]interface{}{
			"status": model.OrderStatusCanceled,
		},
	}

	_, err = cloudbase.UpdateItem(ctx, "orders", updatePayload)
	if err != nil {
		// Log the error but don't fail the request since the payment was already closed
		fmt.Printf("failed to update order status in database: %v\n", err)
		return errors.New("failed to update order status in database when close order")
	}

	// step3: rollback stock (add 1 back)
	// Only rollback inventory if order was created on the same day

	// Get order details to check creation time
	orderQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"order_no": map[string]interface{}{
					"$eq": req.OutTradeNo,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":       true,
			"createdAt": true,
			"status":    true,
		},
	}

	orderData, err := cloudbase.GetItem(ctx, "orders", orderQuery)
	if err != nil {
		log.Printf("failed to get order for rollback check: %v", err)
		// If we can't get the order, skip rollback to avoid potential issues
		return nil
	}

	if len(orderData) == 0 {
		return nil
	}

	// Check if order exists and get creation time
	createdAt, ok := orderData["createdAt"].(float64)
	if !ok {
		log.Printf("invalid or missing createdAt for order %s", req.OutTradeNo)
		// If we can't get creation time, skip rollback
		return nil
	}

	// This prevents rollback for old orders that might have already been processed
	if !isOrderCreatedToday(createdAt) {
		orderCreatedTime := time.Unix(int64(createdAt/1000), 0)
		currentTime := time.Now()
		log.Printf("Skipping inventory rollback for order %s (created on %s, cancelled on %s)",
			req.OutTradeNo, orderCreatedTime.Format("2006-01-02"), currentTime.Format("2006-01-02"))
		return nil
	}

	switch req.PromotionType {
	case "tandian1":
		// Only rollback inventory if the order was created today
		if util.IsOrderCreatedToday(req.CreatedAt) {
			if err := incrementTandianInventory(ctx, req.PromotionID); err != nil {
				log.Printf("failed to rollback tandian inventory: %v", err)
				return errors.New("failed to rollback tandian inventory when close order")
			}
		} else {
			log.Printf("order %s was not created today, skipping inventory rollback", req.OutTradeNo)
		}
	case "ykj", "tjt":
		// Only rollback inventory if the order was created today
		if util.IsOrderCreatedToday(req.CreatedAt) {
			if err := incrementTejiatuanInventory(ctx, req.PromotionID); err != nil {
				log.Printf("failed to rollback tejiatuan inventory: %v", err)
				return errors.New("failed to rollback tejiatuan inventory when close order")
			}
		} else {
			log.Printf("order %s was not created today, skipping inventory rollback", req.OutTradeNo)
		}
	}
	orderCreatedTime := time.Unix(int64(createdAt/1000), 0)
	log.Printf(
		"Inventory rolled back for order %s (created on %s)",
		req.OutTradeNo,
		orderCreatedTime.Format("2006-01-02"),
	)

	return nil
}

// 小程序调起支付
// https://pay.weixin.qq.com/doc/v3/merchant/4012791898
type UnifiedOrderResponse struct {
	Timestamp string `json:"timeStamp"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	SignType  string `json:"signType"`
	PaySign   string `json:"paySign"`
}

// GenerateTimeStamp 生成一个时间戳字符串.
func GenerateTimeStamp() string {
	return strconv.FormatInt(time.Now().Unix(), 10)
}

type CreateOrderRequest struct {
	ReferrerID    string  `json:"referrer_id"`
	UserID        string  `json:"user_id"`
	PromotionID   string  `json:"promotion_id"`
	ShopID        string  `json:"shop_id"`
	OrderType     string  `json:"order_type"`
	TotalAmount   float64 `json:"total_amount"`
	PaidAmount    float64 `json:"paid_amount"`
	RefundAmount  float64 `json:"refund_amount"`
	Commission    float64 `json:"commission"`
	OrderNo       string  `json:"order_no"`
	OpenID        string  `json:"openid"`
	CouponCode    string  `json:"coupon_code"`
	CouponStockId string  `json:"coupon_stock_id"`
}

func (w *WechatPayClient) CreateOrder(ctx context.Context, req CreateOrderRequest) (string, error) {
	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("promotion_lock:%s", req.PromotionID)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return "", fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// Check inventory based on order type
	switch req.OrderType {
	case "tandian", "tandian1":
		// 探店订单库存检查
		if err := checkTandianInventory(ctx, req.PromotionID); err != nil {
			return "", fmt.Errorf("tandian inventory check failed: %w", err)
		}
		// Decrement inventory for tandian
		if err := decrementTandianInventory(ctx, req.PromotionID); err != nil {
			return "", fmt.Errorf("tandian, failed to decrement inventory: %w", err)
		}
	case "ykj", "tjt":
		// 特价团订单库存检查
		if err := checkTejiatuanInventory(ctx, req.PromotionID); err != nil {
			return "", fmt.Errorf("tjt inventory check failed: %w", err)
		}
		// Decrement inventory for tejiatu
		if err := decrementTejiatuanInventory(ctx, req.PromotionID); err != nil {
			return "", fmt.Errorf("tjt, failed to decrement inventory: %w", err)
		}
	default:
		return "", fmt.Errorf("unknown order type: %s", req.OrderType)
	}

	// deprecated: now redeem coupon in TRANSACTION.SUCCESS callback
	// Redeem coupon if provided
	// if req.CouponCode != "" && req.CouponStockId != "" {
	// 	if err := redeemCoupon(ctx, req.CouponCode, req.CouponStockId, req.OpenID); err != nil {
	// 		// Log the error but don't fail the order creation
	// 		// since inventory has already been decremented
	// 		log.Printf("failed to redeem coupon %s: %v", req.CouponCode, err)
	// 		// You may want to implement a rollback mechanism here
	// 	}
	// }

	// Create order in database
	orderID, err := createOrderInDB(ctx, req)
	if err != nil {
		// If order creation fails, we should rollback inventory
		// For now, log the error - in production, implement proper rollback
		fmt.Printf("failed to create order in db: %v\n", err)
		return "", fmt.Errorf("failed to create order: %w", err)
	}

	return orderID, nil
}

// checkTandianInventory checks inventory for 探店 orders.
func checkTandianInventory(ctx context.Context, promotionID string) error {
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":               true,
			"realtime_quantity": true,
			"promotion_type":    true,
		},
	}

	data, err := cloudbase.GetItem(ctx, "promotions", q)
	if err != nil {
		return fmt.Errorf("failed to get promotion: %w", err)
	}

	// Check if data is empty (promotion not found)
	if len(data) == 0 {
		return fmt.Errorf("promotion not found: %s", promotionID)
	}

	// Check realtime_quantity for tandian
	if quantity, ok := data["realtime_quantity"].(float64); ok {
		if quantity <= 0 {
			return errors.New("insufficient inventory for tandian promotion")
		}
	} else {
		return errors.New("invalid realtime_quantity type")
	}

	return nil
}

// decrementTandianInventory decrements inventory for 探店 orders.
func decrementTandianInventory(ctx context.Context, promotionID string) error {
	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("decrement_promotion_stock:%s", promotionID)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// First get current quantity
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":               true,
			"realtime_quantity": true,
		},
	}

	data, err := cloudbase.GetItem(ctx, "promotions", q)
	if err != nil {
		return fmt.Errorf("failed to get promotion for decrement: %w", err)
	}

	// Check if data is empty (promotion not found)
	if len(data) == 0 {
		return fmt.Errorf("promotion not found for decrement: %s", promotionID)
	}

	// Get current quantity
	currentQty, ok := data["realtime_quantity"].(float64)
	if !ok {
		return errors.New("invalid realtime_quantity type")
	}

	// Calculate new quantity
	newQty := int(currentQty) - 1

	// Update with new quantity
	updatePayload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"data": map[string]interface{}{
			"realtime_quantity": newQty,
		},
	}

	_, err = cloudbase.UpdateItem(ctx, "promotions", updatePayload)
	if err != nil {
		return fmt.Errorf("failed to decrement tandian inventory: %w", err)
	}

	return nil
}

// checkTejiatuanInventory checks inventory for 特价团 orders.
func checkTejiatuanInventory(ctx context.Context, promotionID string) error {
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":               true,
			"is_chain":          true,
			"promotion_type":    true,
			"product_id":        true,
			"quantity":          true,
			"realtime_quantity": true,
		},
	}

	data, err := cloudbase.GetItem(ctx, "promotions", q)
	if err != nil {
		return fmt.Errorf("failed to get promotion: %w", err)
	}

	// Check if data is empty (promotion not found)
	if len(data) == 0 {
		return fmt.Errorf("promotion not found: %s", promotionID)
	}

	fmt.Printf("promotion data: %+v\n", data)

	// Check if this is a chain store promotion
	isChain, _ := data["is_chain"].(bool)

	if isChain {
		// For chain stores, check the product's remain_total_quantity
		productIDMap, ok := data["product_id"].(map[string]interface{})
		if !ok {
			return errors.New("invalid product_id for chain promotion")
		}

		productID, ok := productIDMap["_id"].(string)
		if !ok {
			return errors.New("invalid product_id._id for chain promotion")
		}

		// Query product for remain_total_quantity
		productQuery := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"_id": map[string]interface{}{
						"$eq": productID,
					},
				},
			},
			"select": map[string]interface{}{
				"_id":                   true,
				"remain_total_quantity": true,
			},
		}

		productData, err := cloudbase.GetItem(ctx, "products", productQuery)
		if err != nil {
			return fmt.Errorf("failed to get product for chain inventory: %w", err)
		}

		// Check if productData is empty (product not found)
		if len(productData) == 0 {
			return fmt.Errorf("product not found for chain inventory: %s", productID)
		}

		// Check remain_total_quantity
		if remainQty, ok := productData["remain_total_quantity"].(float64); ok {
			if remainQty <= 0 {
				return errors.New("insufficient chain inventory for tejiatu promotion")
			}
		} else {
			return errors.New("invalid remain_total_quantity type")
		}
	} else {
		// For non-chain stores, check realtime_quantity
		if stock, ok := data["realtime_quantity"].(float64); ok {
			if stock <= 0 {
				return errors.New("insufficient inventory for tejiatu promotion")
			}
		} else {
			return errors.New("invalid realtime_quantity type")
		}
	}

	return nil
}

// decrementTejiatuanInventory decrements inventory for 特价团 orders.
func decrementTejiatuanInventory(ctx context.Context, promotionID string) error {
	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("decrement_promotion_stock:%s", promotionID)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// First, get the promotion to check if it's a chain store
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":               true,
			"is_chain":          true,
			"product_id":        true,
			"realtime_quantity": true,
		},
	}

	data, err := cloudbase.GetItem(ctx, "promotions", q)
	if err != nil {
		return fmt.Errorf("failed to get promotion for inventory update: %w", err)
	}

	// Check if data is empty (promotion not found)
	if len(data) == 0 {
		return fmt.Errorf("promotion not found for inventory update: %s", promotionID)
	}

	isChain, _ := data["is_chain"].(bool)

	// Get promotion's current realtime_quantity
	currentPromotionQty, ok := data["realtime_quantity"].(float64)
	if !ok {
		return errors.New("invalid realtime_quantity type")
	}

	// Calculate new quantity
	newPromotionQty := int(currentPromotionQty) - 1

	// Always update realtime_quantity for the promotion
	updatePromotionPayload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"data": map[string]interface{}{
			"realtime_quantity": newPromotionQty,
		},
	}

	_, err = cloudbase.UpdateItem(ctx, "promotions", updatePromotionPayload)
	if err != nil {
		return fmt.Errorf("failed to decrement promotion inventory: %w", err)
	}

	// If it's a chain store, also update the product's remain_total_quantity
	if isChain {
		productIDMap, ok := data["product_id"].(map[string]interface{})
		if !ok {
			return errors.New("invalid product_id for chain promotion")
		}

		productID, ok := productIDMap["_id"].(string)
		if !ok {
			return errors.New("invalid product_id._id for chain promotion")
		}

		// Get current product quantity
		productQuery := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"_id": map[string]interface{}{
						"$eq": productID,
					},
				},
			},
			"select": map[string]interface{}{
				"_id":                   true,
				"remain_total_quantity": true,
			},
		}

		productData, err := cloudbase.GetItem(ctx, "products", productQuery)
		if err != nil {
			return fmt.Errorf("failed to get product for decrement: %w", err)
		}

		// Check if productData is empty (product not found)
		if len(productData) == 0 {
			return fmt.Errorf("product not found for decrement: %s", productID)
		}

		// Get current quantity
		currentQty, ok := productData["remain_total_quantity"].(float64)
		if !ok {
			return errors.New("invalid remain_total_quantity type")
		}

		// Calculate new quantity
		newQty := int(currentQty) - 1

		// Update with new quantity
		updateProductPayload := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"_id": map[string]interface{}{
						"$eq": productID,
					},
				},
			},
			"data": map[string]interface{}{
				"remain_total_quantity": newQty,
			},
		}

		_, err = cloudbase.UpdateItem(ctx, "products", updateProductPayload)
		if err != nil {
			return fmt.Errorf("failed to decrement chain product inventory: %w", err)
		}
	}

	return nil
}

// createOrderInDB creates the order in the database.
func createOrderInDB(ctx context.Context, req CreateOrderRequest) (string, error) {
	// Prepare order data
	orderData := map[string]interface{}{
		"data": map[string]interface{}{
			"referrer_id": req.ReferrerID,
			"user_id": map[string]interface{}{
				"_id": req.UserID,
			},
			"promotion_id": []map[string]interface{}{
				{
					"_id": req.PromotionID,
				},
			},
			"shop_id": map[string]interface{}{
				"_id": req.ShopID,
			},
			"p_id":            req.PromotionID,
			"order_type":      req.OrderType,
			"total_amount":    req.TotalAmount,
			"paid_amount":     req.PaidAmount,
			"refund_amount":   req.RefundAmount,
			"commission":      req.Commission,
			"order_no":        req.OrderNo,
			"openid":          req.OpenID,
			"coupon_code":     req.CouponCode,
			"coupon_stock_id": req.CouponStockId,
			"status":          model.OrderStatusPendingPayment, // Initial payment status
			"createdAt":       time.Now().Unix() * 1000,        // CloudBase uses milliseconds
			"updatedAt":       time.Now().Unix() * 1000,
		},
	}

	// Create order in database
	result, err := cloudbase.CreateItem(ctx, "orders", orderData)
	if err != nil {
		return "", fmt.Errorf("failed to create order in database: %w", err)
	}

	// Extract order ID from result
	if id, ok := result["id"].(string); ok {
		return id, nil
	}

	return "", errors.New("failed to get order ID from create result")
}

// incrementTandianInventory increments inventory for 探店 orders (rollback).
func incrementTandianInventory(ctx context.Context, promotionID string) error {
	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("increment_promotion_stock:%s", promotionID)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// First get current quantity
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":               true,
			"realtime_quantity": true,
		},
	}

	data, err := cloudbase.GetItem(ctx, "promotions", q)
	if err != nil {
		return fmt.Errorf("failed to get promotion for increment: %w", err)
	}

	// Check if data is empty (promotion not found)
	if len(data) == 0 {
		return fmt.Errorf("promotion not found for increment: %s", promotionID)
	}

	// Get current quantity
	currentQty, ok := data["realtime_quantity"].(float64)
	if !ok {
		return errors.New("invalid realtime_quantity type")
	}

	// Calculate new quantity (add 1 back)
	newQty := int(currentQty) + 1

	// Update with new quantity
	updatePayload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"data": map[string]interface{}{
			"realtime_quantity": newQty,
		},
	}

	_, err = cloudbase.UpdateItem(ctx, "promotions", updatePayload)
	if err != nil {
		return fmt.Errorf("failed to increment tandian inventory: %w", err)
	}

	return nil
}

// redeemCoupon redeems a coupon for a user.
func redeemCoupon(ctx context.Context, couponCode, couponStockId, openID string) error {
	// Use redsync for atomic operations to prevent double redemption
	lockKey := fmt.Sprintf("coupon_redeem:%s:%s", couponStockId, couponCode)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return fmt.Errorf("failed to acquire coupon redemption lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			log.Printf("failed to release coupon redemption lock: %v\n", err)
		}
	}()

	// Build request
	req := merchantexclusivecoupon.UseCouponRequest{
		CouponCode: core.String(couponCode),
		Openid:     core.String(openID),
		StockId:    core.String(couponStockId),
	}

	// Call API
	resp, result, err := UseCoupon(ctx, req)
	if err != nil {
		log.Printf("call UseCoupon err:%s", err)
		return err
	}

	prettyjson.Print(resp)
	prettyjson.Print(result)

	// TODO: insert redeem record to coupon_records table

	// // Check if coupon exists and is valid
	// couponQuery := map[string]interface{}{
	// 	"filter": map[string]interface{}{
	// 		"where": map[string]interface{}{
	// 			"coupon_code": map[string]interface{}{
	// 				"$eq": couponCode,
	// 			},
	// 			"stock_id": map[string]interface{}{
	// 				"$eq": couponStockId,
	// 			},
	// 		},
	// 	},
	// 	"select": map[string]interface{}{
	// 		"_id":         true,
	// 		"coupon_code": true,
	// 		"stock_id":    true,
	// 		"status":      true,
	// 		"openid":      true,
	// 		"redeemed_at": true,
	// 	},
	// }

	// couponData, err := cloudbase.GetItem(ctx, "coupon_records", couponQuery)
	// if err != nil {
	// 	// If coupon record doesn't exist, create one
	// 	couponRecord := map[string]interface{}{
	// 		"data": map[string]interface{}{
	// 			"coupon_code": couponCode,
	// 			"stock_id":    couponStockId,
	// 			"openid":      openID,
	// 			"status":      "REDEEMED",
	// 			"redeemed_at": time.Now().Unix() * 1000,
	// 			"created_at":  time.Now().Unix() * 1000,
	// 			"updated_at":  time.Now().Unix() * 1000,
	// 		},
	// 	}
	// 	_, createErr := cloudbase.CreateItem(ctx, "coupon_records", couponRecord)
	// 	if createErr != nil {
	// 		return fmt.Errorf("failed to create coupon redemption record: %v", createErr)
	// 	}
	// 	log.Printf("Coupon %s redeemed successfully for user %s", couponCode, openID)
	// 	return nil
	// }

	// // Check if coupon is already redeemed
	// if status, ok := couponData["status"].(string); ok && status == "REDEEMED" {
	// 	return fmt.Errorf("coupon %s has already been redeemed", couponCode)
	// }

	// // Update coupon status to redeemed
	// updatePayload := map[string]interface{}{
	// 	"filter": map[string]interface{}{
	// 		"where": map[string]interface{}{
	// 			"coupon_code": map[string]interface{}{
	// 				"$eq": couponCode,
	// 			},
	// 			"stock_id": map[string]interface{}{
	// 				"$eq": couponStockId,
	// 			},
	// 		},
	// 	},
	// 	"data": map[string]interface{}{
	// 		"status":      "REDEEMED",
	// 		"openid":      openID,
	// 		"redeemed_at": time.Now().Unix() * 1000,
	// 		"updated_at":  time.Now().Unix() * 1000,
	// 	},
	// }

	// _, err = cloudbase.UpdateItem(ctx, "coupon_records", updatePayload)
	// if err != nil {
	// 	return fmt.Errorf("failed to update coupon redemption status: %v", err)
	// }

	log.Printf("Coupon %s redeemed successfully for user %s", couponCode, openID)

	return nil
}

// incrementTejiatuanInventory increments inventory for 特价团 orders (rollback).
func incrementTejiatuanInventory(ctx context.Context, promotionID string) error {
	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("increment_promotion_stock:%s", promotionID)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// First, get the promotion to check if it's a chain store product
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":               true,
			"is_chain":          true,
			"product_id":        true,
			"brand_id":          true,
			"promotion_type":    true,
			"realtime_quantity": true,
		},
	}

	promotionData, err := cloudbase.GetItem(ctx, "promotions", q)
	if err != nil {
		return fmt.Errorf("failed to get promotion for increment: %w", err)
	}
	prettyjson.Printf("promotion detail: %+s\n", promotionData)

	// Check if promotion data is empty (promotion not found)
	if len(promotionData) == 0 {
		return fmt.Errorf("promotion not found for increment: %s", promotionID)
	}

	// Get chain status and product ID from promotion data
	actualIsChain, _ := promotionData["is_chain"].(bool)
	var actualProductID string
	if productIDMap, ok := promotionData["product_id"].(map[string]interface{}); ok {
		if id, ok := productIDMap["_id"].(string); ok {
			actualProductID = id
		}
	}

	// If it's a chain store, also update the product's remain_total_quantity, otherwise update promotion's realtime_quantity
	if actualIsChain {
		// Get current product quantity
		productQuery := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"_id": map[string]interface{}{
						"$eq": actualProductID,
					},
				},
			},
			"select": map[string]interface{}{
				"_id":                   true,
				"remain_total_quantity": true,
			},
		}

		productData, err := cloudbase.GetItem(ctx, "products", productQuery)
		if err != nil {
			return fmt.Errorf("failed to get product for increment: %w", err)
		}

		// Check if productData is empty (product not found)
		if len(productData) == 0 {
			return fmt.Errorf("product not found for increment: %s", actualProductID)
		}

		// Get current quantity
		currentQty, ok := productData["remain_total_quantity"].(float64)
		if !ok {
			return errors.New("invalid remain_total_quantity type")
		}

		// Calculate new quantity (add 1 back)
		newQty := int(currentQty) + 1

		// Update with new quantity
		updateProductPayload := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"_id": map[string]interface{}{
						"$eq": actualProductID,
					},
				},
			},
			"data": map[string]interface{}{
				"remain_total_quantity": newQty,
			},
		}

		_, err = cloudbase.UpdateItem(ctx, "products", updateProductPayload)
		if err != nil {
			return fmt.Errorf("failed to increment chain product inventory: %w", err)
		}
	} else {
		// First get current promotion quantity
		currentPromotionQty, ok := promotionData["realtime_quantity"].(float64)
		if !ok {
			return errors.New("invalid realtime_quantity type")
		}

		// Calculate new quantity (add 1 back)
		newPromotionQty := int(currentPromotionQty) + 1

		// Always update realtime_quantity for the promotion
		updatePromotionPayload := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"_id": map[string]interface{}{
						"$eq": promotionID,
					},
				},
			},
			"data": map[string]interface{}{
				"realtime_quantity": newPromotionQty,
			},
		}

		_, err = cloudbase.UpdateItem(ctx, "promotions", updatePromotionPayload)
		if err != nil {
			return fmt.Errorf("failed to increment promotion inventory: %w", err)
		}
	}

	return nil
}

// isOrderCreatedToday checks if an order was created on the same day as now.
func isOrderCreatedToday(createdAtMs float64) bool {
	orderCreatedTime := time.Unix(int64(createdAtMs/1000), 0)
	currentTime := time.Now()

	// Compare year, month, and day
	return orderCreatedTime.Year() == currentTime.Year() &&
		orderCreatedTime.Month() == currentTime.Month() &&
		orderCreatedTime.Day() == currentTime.Day()
}
