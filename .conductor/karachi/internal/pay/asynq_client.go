package pay

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
)

const (
	TypeRedeemCouponCode = "task:redeem_coupon_code"
	TypeReturnCouponCode = "task:return_coupon_code"
)

type RedeemCouponPayload struct {
	TransactionId string `json:"transaction_id"`
	OutTradeNo    string `json:"out_trade_no"`
	Openid        string `json:"openid"`
}

type ReturnCouponPayload struct {
	CouponCode      string `json:"coupon_code"`
	StockId         string `json:"stock_id"`
	ReturnRequestNo string `json:"return_request_no"`
}

func NewRedeemCouponCodeTask(orderNo, transactionId, openid string) (*asynq.Task, error) {
	payload, err := json.Marshal(RedeemCouponPayload{
		TransactionId: transactionId,
		OutTradeNo:    orderNo,
		Openid:        openid,
	})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(
			TypeRedeemCouponCode,
			payload,
			asynq.MaxRetry(2),
			asynq.Timeout(5*time.Minute),
			asynq.Queue("coupon"),
			asynq.Retention(30*24*time.Hour),
			asynq.Unique(30*24*time.Hour),
			asynq.TaskID(fmt.Sprintf("REDEEM_COUPON_FOR:%s", orderNo)),
		),
		nil
}

func NewRetrunCouponCodeTask(couponCode, stockId, returnRequestNo string) (*asynq.Task, error) {
	payload, err := json.Marshal(ReturnCouponPayload{
		CouponCode:      couponCode,
		StockId:         stockId,
		ReturnRequestNo: returnRequestNo,
	})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(
			TypeReturnCouponCode,
			payload,
			asynq.MaxRetry(2),
			asynq.Timeout(5*time.Minute),
			asynq.Queue("coupon"),
			asynq.Retention(30*24*time.Hour),
			asynq.Unique(30*24*time.Hour),
			asynq.TaskID(fmt.Sprintf("RETURN_COUPON_FOR:%s", couponCode)),
		),
		nil
}

func EnqueueRedeemCouponCodeTask(orderNo, transactionId, openid string) error {
	log := logger.Get("tasks:coupon")
	task, err := NewRedeemCouponCodeTask(orderNo, transactionId, openid)
	if err != nil {
		log.Error("Failed to create NewRedeemCouponCodeTask", zap.Error(err))
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	// Process between 1-5 seconds later
	delay := time.Duration(rand.Intn(5)+1) * time.Second

	info, err := client.Enqueue(task,
		asynq.ProcessIn(delay),
	)
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return err
	}

	log.Info("Enqueued task", zap.String("id", info.ID), zap.String("queue", info.Queue))

	return nil
}

func EnqueueReturnCouponCodeTask(couponCode, stockId, returnRequestNo string) error {
	log := logger.Get("tasks:coupon")
	task, err := NewRetrunCouponCodeTask(couponCode, stockId, returnRequestNo)
	if err != nil {
		log.Error("Failed to create NewRetrunCouponCodeTask", zap.Error(err))
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	// Process between 1-5 seconds later
	delay := time.Duration(rand.Intn(5)+1) * time.Second

	info, err := client.Enqueue(task, asynq.ProcessIn(delay))
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return err
	}

	log.Info("Enqueued task", zap.String("id", info.ID), zap.String("queue", info.Queue))

	return nil
}
