package pay

import (
	"context"
	crypto_rand "crypto/rand"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/Oudwins/zog"
	"github.com/dfang/go-prettyjson"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/merchantexclusivecoupon"

	"tandian-server/internal/config"
	"tandian-server/internal/util/cloudbase"
)

type MerchantCoupon struct {
	StockName      string `json:"stock_name"`
	BelongMerchant string `json:"belong_merchant"`
	Comment        string `json:"comment"`
	GoodsName      string `json:"goods_name"`
	StockType      string `json:"stock_type"`
	CouponUseRule  struct {
		CouponAvailableTime struct {
			AvailableBeginTime       time.Time `json:"available_begin_time"`
			AvailableEndTime         time.Time `json:"available_end_time"`
			AvailableDayAfterReceive int       `json:"available_day_after_receive"`
			AvailableWeek            struct {
				WeekDay          []int `json:"week_day"`
				AvailableDayTime []struct {
					BeginTime int `json:"begin_time"`
					EndTime   int `json:"end_time"`
				} `json:"available_day_time"`
			} `json:"available_week"`
			IrregularyAvaliableTime []struct {
				BeginTime time.Time `json:"begin_time"`
				EndTime   time.Time `json:"end_time"`
			} `json:"irregulary_available_time"`
			WaitDaysAfterReceive int `json:"wait_days_after_receive"`
		} `json:"coupon_available_time"`
		FixedNormalCoupon struct {
			DiscountAmount     int `json:"discount_amount"`
			TransactionMinimum int `json:"transaction_minimum"`
		} `json:"fixed_normal_coupon"`
		DiscountCoupon struct {
			DiscountPercent    int `json:"discount_percent"`
			TransactionMinimum int `json:"transaction_minimum"`
		} `json:"discount_coupon"`
		ExchangeCoupon struct {
			ExchangePrice      int `json:"exchange_price"`
			TransactionMinimum int `json:"transaction_minimum"`
		} `json:"exchange_coupon"`
		UseMethod         string `json:"use_method"`
		MiniProgramsAppid string `json:"mini_programs_appid"`
		MiniProgramsPath  string `json:"mini_programs_path"`
	} `json:"coupon_use_rule"`
	StockSendRule struct {
		MaxAmount          int  `json:"max_amount"`
		MaxCoupons         int  `json:"max_coupons"`
		MaxCouponsPerUser  int  `json:"max_coupons_per_user"`
		MaxAmountByDay     int  `json:"max_amount_by_day"`
		MaxCouponsByDay    int  `json:"max_coupons_by_day"`
		NaturalPersonLimit bool `json:"natural_person_limit"`
		PreventAPIAbuse    bool `json:"prevent_api_abuse"`
		Transferable       bool `json:"transferable"`
		Shareable          bool `json:"shareable"`
	} `json:"stock_send_rule"`
	OutRequestNo   string `json:"out_request_no"`
	CustomEntrance struct {
		MiniProgramsInfo struct {
			MiniProgramsAppid string `json:"mini_programs_appid"`
			MiniProgramsPath  string `json:"mini_programs_path"`
			EntranceWords     string `json:"entrance_words"`
			GuidingWords      string `json:"guiding_words"`
		} `json:"mini_programs_info"`
		Appid           string `json:"appid"`
		HallID          string `json:"hall_id"`
		StoreID         string `json:"store_id"`
		CodeDisplayMode string `json:"code_display_mode"`
	} `json:"custom_entrance"`
	DisplayPatternInfo struct {
		Description     string `json:"description"`
		MerchantLogoURL string `json:"merchant_logo_url"`
		MerchantName    string `json:"merchant_name"`
		BackgroundColor string `json:"background_color"`
		CouponImageURL  string `json:"coupon_image_url"`
		FinderInfo      struct {
			FinderID                 string `json:"finder_id"`
			FinderVideoID            string `json:"finder_video_id"`
			FinderVideoCoverImageURL string `json:"finder_video_cover_image_url"`
		} `json:"finder_info"`
	} `json:"display_pattern_info"`
	CouponCodeMode string `json:"coupon_code_mode"`
	NotifyConfig   struct {
		NotifyAppid string `json:"notify_appid"`
	} `json:"notify_config"`
	Subsidy bool `json:"subsidy"`
}

// CreateCouponsSchema defines validation for creating merchant exclusive coupons.
var CreateCouponsSchema = zog.Struct(zog.Shape{
	"StockName":      zog.String().Required(zog.Message("批次名称必须")).Min(1).Max(50),
	"BelongMerchant": zog.String().Required(zog.Message("批次归属商户号必须")),
	"Comment":        zog.String().Optional().Max(255),
	"GoodsName":      zog.String().Required(zog.Message("适用商品范围必须")).Min(1).Max(50),
	"StockType":      zog.String().Optional(), // NORMAL or DISCOUNT
	"CouponUseRule": zog.Struct(zog.Shape{
		"CouponAvailableTime": zog.Struct(zog.Shape{
			"AvailableBeginTime":       zog.Time().Required(zog.Message("可用开始时间必须")),
			"AvailableEndTime":         zog.Time().Required(zog.Message("可用结束时间必须")),
			"WaitDaysAfterReceive":     zog.Int32().Optional().GTE(0),
			"AvailableDayAfterReceive": zog.Int32().Optional().GTE(1),
		}),
		"FixedNormalCoupon": zog.Ptr(zog.Struct(zog.Shape{
			"DiscountAmount":     zog.Int64().Required(zog.Message("折扣金额必须")).GTE(1),
			"TransactionMinimum": zog.Int64().Required(zog.Message("最低消费金额必须")).GTE(0),
		})),
		"DiscountCoupon": zog.Ptr(zog.Struct(zog.Shape{
			"DiscountPercent":    zog.Int64().Optional().GTE(1).LTE(99),
			"TransactionMinimum": zog.Int64().Optional().GTE(0),
		})),
		"UseMethod": zog.String().Optional(), // OFF_LINE, ONLINE, ALL
	}),
	"StockSendRule": zog.Struct(zog.Shape{
		"MaxCoupons":         zog.Int64().Required(zog.Message("批次最大发放数必须")).GTE(1),
		"MaxCouponsPerUser":  zog.Int64().Required(zog.Message("用户最大领取数必须")).GTE(1),
		"MaxAmount":          zog.Int64().Optional().GTE(0),
		"MaxAmountByDay":     zog.Int64().Optional().GTE(0),
		"NaturalPersonLimit": zog.Bool().Optional(),
		"PreventAPIAbuse":    zog.Bool().Optional(),
	}),
	"OutRequestNo":   zog.String().Required(zog.Message("业务请求唯一流水号必须")).Min(1).Max(128),
	"CouponCodeMode": zog.String().Optional(), // WECHATPAY_MODE, MERCHANT_UPLOAD, MERCHANT_API
	"CustomEntrance": zog.Ptr(zog.Struct(zog.Shape{
		"MiniProgramsInfo": zog.Ptr(zog.Struct(zog.Shape{
			"MiniProgramsAppid": zog.String().Optional(),
			"MiniProgramsPath":  zog.String().Optional(),
			"EntranceWords":     zog.String().Optional().Max(5),
			"GuidingWords":      zog.String().Optional().Max(15),
		})),
		"Appid":           zog.String().Optional(),
		"HallId":          zog.String().Optional(),
		"StoreId":         zog.String().Optional(),
		"CodeDisplayMode": zog.String().Optional(),
	})),
	"DisplayPatternInfo": zog.Ptr(zog.Struct(zog.Shape{
		"Description":     zog.String().Optional().Max(1000),
		"MerchantLogoUrl": zog.String().Optional(),
		"MerchantName":    zog.String().Optional().Max(16),
		"BackgroundColor": zog.String().Optional(),
		"CouponImageUrl":  zog.String().Optional(),
	})),
	"NotifyConfig": zog.Ptr(zog.Struct(zog.Shape{
		"NotifyAppid": zog.String().Optional(),
	})),
	"Subsidy": zog.Bool().Optional(),
})

// CreateMerchantCouponResponse represents the response from creating a merchant coupon.
type CreateMerchantCouponResponse struct {
	StockId                  *string    `json:"stock_id"`
	CreateTime               *string    `json:"create_time,omitempty"`
	StockName                string     `json:"stock_name,omitempty"`
	OutRequestNo             string     `json:"out_request_no,omitempty"`
	CouponCodeMode           string     `json:"coupon_code_mode,omitempty"`
	AvailableBeginTime       *time.Time `json:"available_begin_time,omitempty"`
	AvailableEndTime         *time.Time `json:"available_end_time,omitempty"`
	WaitDaysAfterReceive     *int64     `json:"wait_days_after_receive,omitempty"`
	AvailableDayAfterReceive *int64     `json:"available_day_after_receive,omitempty"`
}

// CreateMerchantCoupon creates a new merchant exclusive coupon stock.
func CreateMerchantCoupon(
	ctx context.Context,
	request merchantexclusivecoupon.CreateBusifavorStockRequest,
) (*CreateMerchantCouponResponse, error) {
	fmt.Println("CreateMerchantCoupon: ")
	prettyjson.Print(request)
	// 1. Create WeChat Pay client
	client := NewWechatPayClient()

	// 2. Create merchant exclusive coupon API service
	svc := merchantexclusivecoupon.BusiFavorApiService{Client: client.Client}

	// 3. Call WeChat Pay API to create merchant coupon stock
	resp, result, err := svc.CreateBusifavorStock(ctx, request)
	if err != nil {
		log.Printf("call CreateBusifavorStock err: %s", err)
		return nil, fmt.Errorf("create coupon stock failed: %w", err)
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("CreateBusifavorStock response status: %d", result.Response.StatusCode)
		return nil, fmt.Errorf("create coupon stock failed with status: %d", result.Response.StatusCode)
	}

	log.Printf("Created merchant coupon stock successfully: %+v", resp)

	// 4. Build response
	response := &CreateMerchantCouponResponse{
		StockId:    resp.StockId,
		CreateTime: resp.CreateTime,
	}

	// Add optional fields from request if available
	if request.StockName != nil {
		response.StockName = *request.StockName
	}

	if request.OutRequestNo != nil {
		response.OutRequestNo = *request.OutRequestNo
	}

	if request.CouponCodeMode != nil {
		response.CouponCodeMode = string(*request.CouponCodeMode)
	}

	if request.CouponUseRule != nil && request.CouponUseRule.CouponAvailableTime != nil {
		response.AvailableBeginTime = request.CouponUseRule.CouponAvailableTime.AvailableBeginTime
		response.AvailableEndTime = request.CouponUseRule.CouponAvailableTime.AvailableEndTime
		response.WaitDaysAfterReceive = request.CouponUseRule.CouponAvailableTime.WaitDaysAfterReceive
		response.AvailableDayAfterReceive = request.CouponUseRule.CouponAvailableTime.AvailableDayAfterReceive
	}

	// 5. Save coupon information to database
	couponData := map[string]interface{}{
		"data": map[string]interface{}{
			"stock_id":       response.StockId,
			"stock_name":     response.StockName,
			"out_request_no": response.OutRequestNo,
			"coupon_code_mode": func() string {
				if request.CouponCodeMode != nil {
					return string(*request.CouponCodeMode)
				}
				return ""
			}(),
			"belong_merchant": func() string {
				if request.BelongMerchant != nil {
					return *request.BelongMerchant
				}
				return ""
			}(),
			"goods_name": func() string {
				if request.GoodsName != nil {
					return *request.GoodsName
				}
				return ""
			}(),
			"comment": func() string {
				if request.Comment != nil {
					return *request.Comment
				}
				return ""
			}(),
			"stock_type": func() string {
				if request.StockType != nil {
					return string(*request.StockType)
				}
				return "NORMAL"
			}(),
			"available_begin_time": func() int64 {
				if response.AvailableBeginTime != nil {
					return response.AvailableBeginTime.Unix() * 1000 // Convert to milliseconds
				}
				return 0
			}(),
			"available_end_time": func() int64 {
				if response.AvailableEndTime != nil {
					return response.AvailableEndTime.Unix() * 1000 // Convert to milliseconds
				}
				return 0
			}(),
			"wait_days_after_receive":     response.WaitDaysAfterReceive,
			"available_day_after_receive": response.AvailableDayAfterReceive,
			"create_time":                 response.CreateTime,
			"status":                      "ACTIVE",                 // 券批次状态
			"created_at":                  time.Now().Unix() * 1000, // CloudBase uses milliseconds
			"updated_at":                  time.Now().Unix() * 1000,
			"stock_send_rule": func() map[string]interface{} {
				result := make(map[string]interface{})
				if request.StockSendRule != nil {
					if request.StockSendRule.MaxCoupons != nil {
						result["max_coupons"] = *request.StockSendRule.MaxCoupons
					}
					if request.StockSendRule.MaxCouponsPerUser != nil {
						result["max_coupons_per_user"] = *request.StockSendRule.MaxCouponsPerUser
					}
					if request.StockSendRule.MaxAmount != nil {
						result["max_amount"] = *request.StockSendRule.MaxAmount
					}
					if request.StockSendRule.MaxAmountByDay != nil {
						result["max_amount_by_day"] = *request.StockSendRule.MaxAmountByDay
					}
					if request.StockSendRule.MaxCouponsByDay != nil {
						result["max_coupons_by_day"] = *request.StockSendRule.MaxCouponsByDay
					}
					if request.StockSendRule.NaturalPersonLimit != nil {
						result["natural_person_limit"] = *request.StockSendRule.NaturalPersonLimit
					}
					if request.StockSendRule.PreventApiAbuse != nil {
						result["prevent_api_abuse"] = *request.StockSendRule.PreventApiAbuse
					}
					if request.StockSendRule.Transferable != nil {
						result["transferable"] = *request.StockSendRule.Transferable
					}
					if request.StockSendRule.Shareable != nil {
						result["shareable"] = *request.StockSendRule.Shareable
					}
				}
				return result
			}(),
			"coupon_use_rule": func() map[string]interface{} {
				result := make(map[string]interface{})
				if request.CouponUseRule != nil {
					// Add coupon available time
					if request.CouponUseRule.CouponAvailableTime != nil {
						availableTime := make(map[string]interface{})
						if request.CouponUseRule.CouponAvailableTime.AvailableBeginTime != nil {
							availableTime["available_begin_time"] = request.CouponUseRule.CouponAvailableTime.AvailableBeginTime.Unix() * 1000
						}
						if request.CouponUseRule.CouponAvailableTime.AvailableEndTime != nil {
							availableTime["available_end_time"] = request.CouponUseRule.CouponAvailableTime.AvailableEndTime.Unix() * 1000
						}
						if request.CouponUseRule.CouponAvailableTime.WaitDaysAfterReceive != nil {
							availableTime["wait_days_after_receive"] = *request.CouponUseRule.CouponAvailableTime.WaitDaysAfterReceive
						}
						if request.CouponUseRule.CouponAvailableTime.AvailableDayAfterReceive != nil {
							availableTime["available_day_after_receive"] = *request.CouponUseRule.CouponAvailableTime.AvailableDayAfterReceive
						}
						result["coupon_available_time"] = availableTime
					}
					// Add fixed normal coupon
					if request.CouponUseRule.FixedNormalCoupon != nil {
						fixedCoupon := make(map[string]interface{})
						if request.CouponUseRule.FixedNormalCoupon.DiscountAmount != nil {
							fixedCoupon["discount_amount"] = *request.CouponUseRule.FixedNormalCoupon.DiscountAmount
						}
						if request.CouponUseRule.FixedNormalCoupon.TransactionMinimum != nil {
							fixedCoupon["transaction_minimum"] = *request.CouponUseRule.FixedNormalCoupon.TransactionMinimum
						}
						result["fixed_normal_coupon"] = fixedCoupon
					}
					// Add discount coupon
					if request.CouponUseRule.DiscountCoupon != nil {
						discountCoupon := make(map[string]interface{})
						if request.CouponUseRule.DiscountCoupon.DiscountPercent != nil {
							discountCoupon["discount_percent"] = *request.CouponUseRule.DiscountCoupon.DiscountPercent
						}
						if request.CouponUseRule.DiscountCoupon.TransactionMinimum != nil {
							discountCoupon["transaction_minimum"] = *request.CouponUseRule.DiscountCoupon.TransactionMinimum
						}
						result["discount_coupon"] = discountCoupon
					}
					// Add use method
					if request.CouponUseRule.UseMethod != nil {
						result["use_method"] = string(*request.CouponUseRule.UseMethod)
					}
					// Add mini programs info
					if request.CouponUseRule.MiniProgramsAppid != nil {
						result["mini_programs_appid"] = *request.CouponUseRule.MiniProgramsAppid
					}
					if request.CouponUseRule.MiniProgramsPath != nil {
						result["mini_programs_path"] = *request.CouponUseRule.MiniProgramsPath
					}
				}
				return result
			}(),
		},
	}

	prettyjson.Print(couponData)

	// Insert coupon record into database
	dbResult, err := cloudbase.CreateItem(ctx, "coupons", couponData)
	if err != nil {
		// Log the error but don't fail the entire operation since the coupon was created successfully in WeChat Pay
		log.Printf("Failed to save coupon to database: %v", err)
		// You might want to implement a retry mechanism or save to a queue for later processing
	} else {
		log.Printf("Coupon saved to database with ID: %v", dbResult["id"])
	}

	return response, nil
}

// UseCoupon 核销券.
func UseCoupon(
	ctx context.Context,
	request merchantexclusivecoupon.UseCouponRequest,
) (*merchantexclusivecoupon.UseCouponResponse, *core.APIResult, error) {
	// Create WeChat Pay client
	client := NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}

	// now := time.Now().In(time.FixedZone("Asia/Shanghai", +8*60*60))
	// request.UseTime = core.String(now.Format("2006-01-02T15:04:05+"))
	request.UseTime = core.String(time.Now().Format(time.RFC3339))
	request.UseRequestNo = core.String(_generate_use_request_no(*request.StockId))
	request.Appid = core.String(config.AppConfig.WechatPay.AppID)

	// Call API
	resp, result, err := svc.UseCoupon(ctx, request)
	if err != nil {
		log.Printf("call UseCoupon err:%s", err)
		return nil, nil, err
	}

	return resp, result, nil
}

// ReturnCoupon 返还用户券.
func ReturnCoupon(
	ctx context.Context,
	request merchantexclusivecoupon.ReturnCouponRequest,
) (*merchantexclusivecoupon.ReturnCouponResponse, *core.APIResult, error) {
	// Create WeChat Pay client
	client := NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}

	// Build request
	// req := merchantexclusivecoupon.ReturnCouponRequest{
	// 	CouponCode:      core.String(payload.CouponCode),
	// 	StockId:         core.String(payload.StockId),
	// 	ReturnRequestNo: core.String(payload.ReturnRequestNo),
	// }
	request.ReturnRequestNo = core.String(_generate_return_request_no(*request.StockId))

	// Call API
	resp, result, err := svc.ReturnCoupon(ctx, request)
	if err != nil {
		log.Printf("call ReturnCoupon err:%s", err)
		return nil, nil, err
	}

	return resp, result, nil
}

// DeactivateCoupon 使券失效.
func DeactivateCoupon(
	ctx context.Context,
	request merchantexclusivecoupon.DeactivateCouponRequest,
) (*merchantexclusivecoupon.DeactivateCouponResponse, *core.APIResult, error) {
	// Create WeChat Pay client
	client := NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}

	// Build request
	request.DeactivateRequestNo = core.String(_generate_deactivate_request_no(*request.StockId))

	// Call API
	resp, result, err := svc.DeactivateCoupon(ctx, request)
	if err != nil {
		log.Printf("call DeactivateCoupon err:%s", err)
		return nil, nil, err
	}

	return resp, result, nil
}

// QueryStock 查询批次详情.
func QueryStock(
	ctx context.Context,
	request merchantexclusivecoupon.QueryStockRequest,
) (*merchantexclusivecoupon.StockGetResponse, *core.APIResult, error) {
	// Create WeChat Pay client
	client := NewWechatPayClient()
	svc := merchantexclusivecoupon.BusiFavorApiService{Client: client.Client}

	// Call API
	resp, result, err := svc.QueryStock(ctx, request)
	if err != nil {
		log.Printf("call QueryStock err:%s", err)
		return nil, nil, err
	}

	return resp, result, nil
}

// QueryCoupon 查询用户券详情.
func QueryCoupon(
	ctx context.Context,
	request merchantexclusivecoupon.QueryCouponRequest,
) (*merchantexclusivecoupon.CouponEntity, *core.APIResult, error) {
	// Create WeChat Pay client
	client := NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}
	request.Appid = core.String(config.AppConfig.WechatPay.AppID)

	// Call API
	resp, result, err := svc.QueryCoupon(ctx, request)
	if err != nil {
		log.Printf("call QueryCoupon err:%s", err)
		return nil, nil, err
	}

	return resp, result, nil
}

// GetCouponNotifyCallbacks 查询优惠券事件通知地址.
func GetCouponNotifyCallbacks(
	ctx context.Context,
) (*merchantexclusivecoupon.GetCouponNotifyResponse, *core.APIResult, error) {
	// Create WeChat Pay client
	client := NewWechatPayClient()

	// Create callback API service
	svc := merchantexclusivecoupon.CallBackApiService{Client: client.Client}

	// Build request
	req := merchantexclusivecoupon.GetCouponNotifyRequest{
		Mchid: core.String(client.MchID),
	}

	// Query current notification settings
	resp, result, err := svc.GetCouponNotify(ctx, req)
	if err != nil {
		log.Printf("call GetCouponNotify err:%s", err)
		return nil, nil, err
	}

	return resp, result, nil
}

func _generate_use_request_no(stock_id string) string {
	// Generate 6 random alphanumeric characters using crypto/rand for security
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	randomBytes := make([]byte, 6)

	// Use crypto/rand for secure random generation
	if _, err := crypto_rand.Read(randomBytes); err != nil {
		// Fallback to timestamp-based generation if crypto/rand fails
		log.Printf("Warning: crypto/rand failed, using fallback: %v", err)

		for i := range randomBytes {
			randomBytes[i] = charset[i%len(charset)]
		}
	} else {
		// Map random bytes to charset
		for i := range randomBytes {
			randomBytes[i] = charset[randomBytes[i]%byte(len(charset))]
		}
	}

	return fmt.Sprintf(
		"%s_%s_USE_%s_%s",
		config.AppConfig.WechatPay.MchID,
		stock_id,
		time.Now().Format("20060102"),
		string(randomBytes),
	)
}

func _generate_return_request_no(stock_id string) string {
	// Generate 6 random alphanumeric characters using crypto/rand for security
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	randomBytes := make([]byte, 6)

	// Use crypto/rand for secure random generation
	if _, err := crypto_rand.Read(randomBytes); err != nil {
		// Fallback to timestamp-based generation if crypto/rand fails
		log.Printf("Warning: crypto/rand failed, using fallback: %v", err)

		for i := range randomBytes {
			randomBytes[i] = charset[i%len(charset)]
		}
	} else {
		// Map random bytes to charset
		for i := range randomBytes {
			randomBytes[i] = charset[randomBytes[i]%byte(len(charset))]
		}
	}

	return fmt.Sprintf(
		"%s_%s_RETURN_%s_%s",
		config.AppConfig.WechatPay.MchID,
		stock_id,
		time.Now().Format("20060102"),
		string(randomBytes),
	)
}

func _generate_deactivate_request_no(stock_id string) string {
	// Generate 6 random alphanumeric characters using crypto/rand for security
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	randomBytes := make([]byte, 6)

	// Use crypto/rand for secure random generation
	if _, err := crypto_rand.Read(randomBytes); err != nil {
		// Fallback to timestamp-based generation if crypto/rand fails
		log.Printf("Warning: crypto/rand failed, using fallback: %v", err)

		for i := range randomBytes {
			randomBytes[i] = charset[i%len(charset)]
		}
	} else {
		// Map random bytes to charset
		for i := range randomBytes {
			randomBytes[i] = charset[randomBytes[i]%byte(len(charset))]
		}
	}

	return fmt.Sprintf(
		"%s_%s_DEACTIVATE_%s_%s",
		config.AppConfig.WechatPay.MchID,
		stock_id,
		time.Now().Format("20060102"),
		string(randomBytes),
	)
}

// GetAppID returns the configured WeChat Pay App ID.
func GetAppID() string {
	return config.AppConfig.WechatPay.AppID
}

func GetMerchantID() string {
	return config.AppConfig.WechatPay.MchID
}

// ListCouponsByFilter 条件查询批次下的券.
func ListCouponsByFilter(
	ctx context.Context,
	request merchantexclusivecoupon.ListCouponsByFilterRequest,
) (*merchantexclusivecoupon.CouponListResponse, *core.APIResult, error) {
	// Create WeChat Pay client
	client := NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}

	// Set default limit if not provided
	if request.Limit == nil || *request.Limit == 0 {
		request.Limit = core.Int64(20) // Default limit
	}

	request.BelongMerchant = core.String(GetMerchantID())

	// Call API
	resp, result, err := svc.ListCouponsByFilter(ctx, request)
	if err != nil {
		log.Printf("call ListCouponsByFilter err:%s", err)
		return nil, nil, err
	}

	return resp, result, nil
}
