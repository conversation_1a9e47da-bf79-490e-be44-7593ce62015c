package pay

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"time"

	"github.com/Oudwins/zog"
	"github.com/dfang/go-prettyjson"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/go-viper/mapstructure/v2"

	"tandian-server/internal/redis"
	"tandian-server/internal/util/cloudbase"
)

type CreateTransferBillRequest struct {
	AppID                    string                    `json:"appid"`
	OutBillNo                string                    `json:"out_bill_no"`
	TransferSceneID          string                    `json:"transfer_scene_id"`
	OpenID                   string                    `json:"openid"`
	Username                 string                    `json:"user_name"`
	TransferAmount           int64                     `json:"transfer_amount"`
	TransferRemark           string                    `json:"transfer_remark"`
	NotifyUrl                string                    `json:"notify_url"`
	UserRecvPerception       string                    `json:"user_recv_perception"`
	TransferSceneReportInfos []TransferSceneReportInfo `json:"transfer_scene_report_infos"`
}

type TransferSceneReportInfo struct {
	InfoType    string `json:"info_type"`
	InfoContent string `json:"info_content"`
}

type CreateTransferBillResponse struct {
	OutBillNo      string `json:"out_bill_no"      mapstructure:"out_bill_no"`
	TransferBillNo string `json:"transfer_bill_no" mapstructure:"transfer_bill_no"`
	State          string `json:"state"            mapstructure:"state"`
	CreateTime     string `json:"create_time"      mapstructure:"create_time"`
	PackageInfo    string `json:"package_info"     mapstructure:"package_info"`
}

func (w *WechatPayClient) QueryTransferBillByOutBillNo(
	ctx context.Context,
	outBillNo string,
) (*TransferBillResponse, error) {
	url := fmt.Sprintf(
		"https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/%s",
		outBillNo,
	)
	header := http.Header{}
	header.Set("Accept", "application/json")

	result, err := w.Client.Request(ctx, "GET", url, header, nil, nil, "application/json")
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer result.Response.Body.Close()

	if result.Response.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(result.Response.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", result.Response.StatusCode, string(body))
	}

	body, err := io.ReadAll(result.Response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var resp TransferBillResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return &resp, nil
}

func (w *WechatPayClient) QueryTransferBillByTransferBillNo(
	ctx context.Context,
	transferBillNo string,
) (*TransferBillResponse, error) {
	url := fmt.Sprintf(
		"https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills/transfer-bill-no/%s",
		transferBillNo,
	)
	header := http.Header{}
	header.Set("Accept", "application/json")

	result, err := w.Client.Request(ctx, "GET", url, header, nil, nil, "application/json")
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer result.Response.Body.Close()

	if result.Response.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(result.Response.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", result.Response.StatusCode, string(body))
	}

	body, err := io.ReadAll(result.Response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var resp TransferBillResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return &resp, nil
}

func (w *WechatPayClient) CancelTransferBill(
	ctx context.Context,
	outBillNo string,
) (*CancelTransferBillResponse, error) {
	url := fmt.Sprintf(
		"https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/%s/cancel",
		outBillNo,
	)
	header := http.Header{}
	header.Set("Accept", "application/json")
	header.Set("Content-Type", "application/json")

	result, err := w.Client.Request(ctx, "POST", url, header, nil, nil, "application/json")
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer result.Response.Body.Close()

	if result.Response.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(result.Response.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", result.Response.StatusCode, string(body))
	}

	body, err := io.ReadAll(result.Response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var resp CancelTransferBillResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return &resp, nil
}

func (w *WechatPayClient) CancelTransferBillByTransferBillNo(
	ctx context.Context,
	transferBillNo string,
) (*CancelTransferBillResponse, error) {
	// First, query the transfer bill to get the out_bill_no
	transferBill, err := w.QueryTransferBillByTransferBillNo(ctx, transferBillNo)
	if err != nil {
		return nil, fmt.Errorf("failed to query transfer bill by transfer_bill_no: %w", err)
	}

	if transferBill.OutBillNo == "" {
		return nil, errors.New("could not find out_bill_no for the given transfer_bill_no")
	}

	// Then, cancel the transfer bill using the retrieved out_bill_no
	return w.CancelTransferBill(ctx, transferBill.OutBillNo)
}

// 发起转账 https://pay.weixin.qq.com/doc/v3/merchant/4012716434
func (w *WechatPayClient) CreateTransferBill(
	ctx context.Context,
	params CreateTransferBillRequest,
) (*CreateTransferBillResponse, error) {
	url := "https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills"
	header := http.Header{}
	header.Set("Accept", "application/json")
	header.Set("Content-Type", "application/json")

	fmt.Println("params:", params)

	body, err := json.MarshalIndent(params, "", " ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}
	// fmt.Println("body: ", string(body))

	result, err := w.Client.Request(ctx, "POST", url, header, nil, bytes.NewReader(body), "application/json")
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer result.Response.Body.Close()

	if result.Response.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(result.Response.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", result.Response.StatusCode, string(body))
	}

	respBody, err := io.ReadAll(result.Response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	fmt.Println("body:", string(respBody))

	var resp CreateTransferBillResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return &resp, nil
}

type CreatePendingWithdrawalRecordReq struct {
	TransactionId  string
	UserId         string
	Amount         float64
	OutBillNo      string
	TransferBillNo string
	Status         string // pending
}

type CreatePendingTransactionReq struct {
	UserID        string
	Amount        float64
	Method        string // wechat
	Status        string // pending
	BalanceBefore float64
	BalanceAfter  float64
}

func CreatePendingTransactionRecord(req CreatePendingTransactionReq) (string, error) {
	// Create Pending Transaction Record
	ctx := context.Background()
	data := map[string]interface{}{
		"data": map[string]interface{}{
			"type":   "withdraw",
			"status": "pending",
			"amount": req.Amount,
			"user_id": map[string]interface{}{
				"_id": req.UserID,
			},
			"balance_before": req.BalanceBefore,
			"balance_after":  req.BalanceAfter,
		},
	}

	result, err := cloudbase.CreateItem(ctx, "transactions", data)
	if err != nil {
		fmt.Println(err)
		return "", err
	}

	fmt.Printf("%+v\n", result)

	// Extract transaction ID from the response
	if id, ok := result["id"].(string); ok {
		return id, nil
	}

	return "", errors.New("failed to get transaction ID from response")
}

func CreatePendingWithdrawalRecord(req CreatePendingWithdrawalRecordReq) (string, error) {
	// Create Pending Withdrawal Record
	ctx := context.Background()
	data := map[string]interface{}{
		"data": map[string]interface{}{
			"user_id": map[string]interface{}{
				"_id": req.UserId,
			},
			"transaction_id": map[string]interface{}{
				"_id": req.TransactionId,
			},
			"transfer_bill_no": req.TransferBillNo,
			"out_bill_no":      req.OutBillNo,
			"amount":           req.Amount,
			"type":             "withdraw",
			"method":           "wechat",
			"status":           "pending", // withdrawal_status: pending,processing,completed,failed,canceled
		},
	}

	result, err := cloudbase.CreateItem(ctx, "withdrawals", data)
	if err != nil {
		fmt.Println(err)
		return "", err
	}

	fmt.Printf("%+v\n", result)

	// Extract withdrawal ID from the response
	if id, ok := result["id"].(string); ok {
		return id, nil
	}

	return "", errors.New("failed to get withdrawal ID from response")
}

type TransactionRecord struct {
	BalanceAfter  float64 `mapstructure:"balance_after"`
	BalanceBefore float64 `mapstructure:"balance_before"`
	CreatedAt     string  `mapstructure:"created_at"`
	ID            string  `mapstructure:"_id"`
	Status        string  `mapstructure:"status"`
	Type          string  `mapstructure:"type"`
	// UserID       string  `mapstructure:"user_id"`
}

type UserRecord struct {
	ID       string  `json:"id"       mapstructure:"_id"`
	Nickname string  `json:"nickname" mapstructure:"nickname"`
	Phone    string  `json:"phone"    mapstructure:"phone"`
	UID      float64 `json:"uid"      mapstructure:"uid"`
	Balance  float64 `json:"balance"  mapstructure:"balance"`
	Audited  bool    `json:"audited"  mapstructure:"audited"`
}

func GetLatestTransactionRecord(userId string) (*TransactionRecord, error) {
	ctx := context.Background()
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"user_id": map[string]interface{}{
					"$eq": userId,
				},
				"status": "completed", // TODO: 临时注释
			},
		},
		"select": map[string]interface{}{
			"_id":            true,
			"balance_before": true,
			"balance_after":  true,
			"created_at":     true,
			"status":         true,
			"type":           true,
			// "user_id":        true,
		},
		"sort": map[string]interface{}{
			"created_at": "desc",
		},
		"orderBy": []map[string]interface{}{
			{
				"createdAt": "DESC", // 按创建时间倒序
			},
		},
		"pageSize":   1,
		"pageNumber": 1,
	}
	prettyjson.Print(q)

	record, err := cloudbase.GetItem(ctx, "transactions", q)
	if err != nil {
		return nil, fmt.Errorf("cloudbase.GetItem failed: %w", err)
	}

	// Check if record is empty
	if len(record) == 0 {
		// No transaction found, return zero balance record
		return &TransactionRecord{
			BalanceAfter:  0,
			BalanceBefore: 0,
		}, nil
	}

	// Decode the transaction record
	var resp TransactionRecord
	if err := mapstructure.Decode(record, &resp); err != nil {
		return nil, fmt.Errorf("failed to decode transaction record: %w", err)
	}
	prettyjson.Printf("TransactionRecord: %s\n", resp)

	return &resp, nil
}

func GetUserByOpenId(openid string) (*UserRecord, error) {
	ctx := context.Background()
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"openid": map[string]interface{}{
					"$eq": openid,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":      true,
			"nickname": true,
			"phone":    true,
			"uid":      true,
			"balance":  true,
			"audited":  true,
		},
		"sort": map[string]interface{}{
			"created_at": "desc",
		},
		"orderBy": []map[string]interface{}{
			{
				"createdAt": "DESC", // 按创建时间倒序
			},
		},
		"pageSize":   1,
		"pageNumber": 1,
	}

	record, err := cloudbase.GetItem(ctx, "users", q)
	if err != nil {
		return nil, fmt.Errorf("cloudbase.GetItem failed: %w", err)
	}

	// Check if record is empty
	if len(record) == 0 {
		// No user found
		return nil, nil
	}

	var resp UserRecord
	if err := mapstructure.Decode(record, &resp); err != nil {
		return nil, fmt.Errorf("failed to decode user record: %w", err)
	}

	return &resp, nil
}

func GetUserIdByOpenId(openid string) (string, error) {
	ctx := context.Background()
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"openid": map[string]interface{}{
					"$eq": openid,
				},
			},
		},
		"select": map[string]interface{}{
			"_id": true,
		},
		"sort": map[string]interface{}{
			"created_at": "desc",
		},
		"orderBy": []map[string]interface{}{
			{
				"createdAt": "DESC", // 按创建时间倒序
			},
		},
		"pageSize":   1,
		"pageNumber": 1,
	}

	record, err := cloudbase.GetItem(ctx, "users", q)
	if err != nil {
		return "", fmt.Errorf("cloudbase.GetItem failed: %w", err)
	}

	// Check if record is empty
	if len(record) == 0 {
		// No user found
		return "", nil
	}

	var resp UserRecord
	if err := mapstructure.Decode(record, &resp); err != nil {
		return "", fmt.Errorf("failed to decode user record: %w", err)
	}

	return resp.ID, nil
}

// Global redsync instance (should be initialized in init or via dependency injection).
var (
	redsyncPool *redsync.Redsync
)

// InitRedsync initializes the redsync instance.
func InitRedsync() {
	redisClient := redis.GetClient()
	pool := goredis.NewPool(redisClient)
	redsyncPool = redsync.New(pool)
}

// GetRedsyncPool returns the initialized redsync instance.
func GetRedsyncPool() *redsync.Redsync {
	return redsyncPool
}

type CreatePendingWithdrawalRequest struct {
	OpenID         string  `json:"openid"`
	TransferAmount float64 `json:"transfer_amount"`
	TransferBillNo string  `json:"transfer_bill_no"`
	OutBillNo      string  `json:"out_bill_no"`
	UserID         string  `json:"user_id"`
}

var CreatePendingWithdrawalRequestSchema = zog.Struct(zog.Shape{
	// its very important that schema keys like "name" match the struct field name NOT the input data
	"OpenID":         zog.String().Required(zog.Message("openid is required")),
	"TransferAmount": zog.Float64().Required(zog.Message("transfer_amount is required")),
	"TransferBillNo": zog.String().Required(zog.Message("transfer_bill_no is required")),
	"OutBillNo":      zog.String().Required(zog.Message("out_bill_no is required")),
	"UserID":         zog.String().Required(zog.Message("user_id is required")),
})

// 用户申请提现，创建转账单，创建转账记录.
func CreatePendingRecordsWhenRequestWithdrawal(req CreatePendingWithdrawalRequest) error {
	// extract userID
	// userID, err := GetUserIdByOpenId(req.OpenID)
	// if err != nil {
	// 	return fmt.Errorf("failed to get user ID: %v", err)
	// }
	// req.UserID = userID
	errsMap := CreatePendingWithdrawalRequestSchema.Validate(&req)
	if errsMap != nil {
		fmt.Printf("Validation errors: %+v\n", errsMap)
		// handle errors -> see Errors section
		sanitized := zog.Issues.SanitizeMap(errsMap)

		return fmt.Errorf("validation failed: %v", sanitized)
	}

	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("withdrawal:%s", req.UserID)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// step 1. check balance
	// step 2. create pending transaction
	// step 3. create pending withdrawal
	// step 4. lock user balance and disable withdrawal

	// step 1. get latest transaction record to check balance
	latestTransaction, err := GetLatestTransactionRecord(req.UserID)
	if err != nil {
		return fmt.Errorf("failed to get latest transaction record: %w", err)
	}

	fmt.Printf("latestTransaction: %+v\n", latestTransaction)

	// Get current balance from latest transaction
	currentBalance := float64(0)
	if latestTransaction != nil {
		currentBalance = latestTransaction.BalanceAfter
	}

	// Check if user has sufficient balance
	if currentBalance < req.TransferAmount {
		fmt.Println("insufficient balance") // SHOULD LOG
		return fmt.Errorf("insufficient balance: %f < %f", currentBalance, req.TransferAmount)
	}

	// step 2. create pending transaction record
	transactionReq := CreatePendingTransactionReq{
		UserID:        req.UserID,
		Amount:        req.TransferAmount,
		Method:        "wechat",
		Status:        "pending",
		BalanceBefore: currentBalance,
		BalanceAfter:  currentBalance - req.TransferAmount,
	}

	transactionId, err := CreatePendingTransactionRecord(transactionReq)
	if err != nil {
		return fmt.Errorf("failed to create pending transaction record: %w", err)
	}

	fmt.Println("transactionId:", transactionId)

	// step 3. create pending withdrawal record
	withdrawalReq := CreatePendingWithdrawalRecordReq{
		TransactionId:  transactionId,
		UserId:         req.UserID,
		Amount:         req.TransferAmount,
		OutBillNo:      req.OutBillNo,
		TransferBillNo: req.TransferBillNo,
		Status:         "pending",
	}

	fmt.Println("create pending withdrawal record")

	withdrawalId, err := CreatePendingWithdrawalRecord(withdrawalReq)
	if err != nil {
		return fmt.Errorf("failed to create pending withdrawal record: %w", err)
	}

	fmt.Println("withdrawalId:", withdrawalId)

	// Step 5: lock user withdrawal feature
	userUpdateData := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"openid": map[string]interface{}{
					"$eq": req.OpenID,
				},
			},
		},
		"data": map[string]interface{}{
			// "balance":  balanceAfter,
			"verified": false,
		},
	}
	s1, _ := prettyjson.Marshal(userUpdateData)
	fmt.Println("update user balance request: ", string(s1))

	result, err := cloudbase.UpdateItem(context.Background(), "users", userUpdateData)
	if err != nil {
		return fmt.Errorf("failed to update user balance: %w", err)
	}

	s2, _ := prettyjson.Marshal(result)
	fmt.Println("update user balance result: ", string(s2))

	return nil
}

// 用户完成提现后， notify callback里更新状态，更新用户余额.
func UpdateBalanceAfterWithdrawal(ctx context.Context, req *TransferBillCallbackResponse) error {
	userID, err := GetUserIdByOpenId(req.OpenID)
	if err != nil {
		return fmt.Errorf("failed to get user ID: %w", err)
	}

	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("update_balance:%s", req.OpenID)
	mutex := redsyncPool.NewMutex(lockKey)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// goal 1: update transaction status to completed
	// goal 2: update withdrawal status to completed
	// goal 3: update user balance
	// goal 4: update user total withdrawals

	// detail steps
	// get the withdrawal record by transfer_bill_no and out_bill_no
	// extract transaction_id from the withdrawal record
	// get transaction record by transaction_id
	// update status of the withdrawal record to completed
	// update status of the transaction record to completed
	// extract balance_after from transaction record
	// update user balance to balance_after

	// Step 1: Get withdrawal record by transfer_bill_no and out_bill_no
	withdrawalQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"transfer_bill_no": map[string]interface{}{
					"$eq": req.TransferBillNo,
				},
				"out_bill_no": map[string]interface{}{
					"$eq": req.OutBillNo,
				},
				"openid": map[string]interface{}{
					"$eq": req.OpenID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":            true,
			"transaction_id": true,
			"amount":         true,
			"status":         true,
		},
	}

	withdrawalRecord, err := cloudbase.GetItem(ctx, "withdrawals", withdrawalQuery)
	if err != nil {
		return fmt.Errorf("failed to get withdrawal record: %w", err)
	}

	// Check if record is empty
	if len(withdrawalRecord) == 0 {
		return fmt.Errorf(
			"withdrawal record not found for transfer_bill_no: %s, out_bill_no: %s",
			req.TransferBillNo,
			req.OutBillNo,
		)
	}

	// Extract transaction_id from withdrawal record
	transactionIDInterface, ok := withdrawalRecord["transaction_id"]
	if !ok {
		return errors.New("transaction_id not found in withdrawal record")
	}

	transactionIDMap, ok := transactionIDInterface.(map[string]interface{})
	if !ok {
		return errors.New("invalid transaction_id format in withdrawal record")
	}

	transactionID, ok := transactionIDMap["_id"].(string)
	if !ok {
		return errors.New("transaction_id._id not found in withdrawal record")
	}

	// Step 2: Get transaction record by transaction_id
	transactionQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": transactionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":           true,
			"balance_after": true,
			"status":        true,
			"user_id":       true,
		},
	}

	transactionRecord, err := cloudbase.GetItem(ctx, "transactions", transactionQuery)
	if err != nil {
		return fmt.Errorf("failed to get transaction record: %w", err)
	}

	// Check if record is empty
	if len(transactionRecord) == 0 {
		return fmt.Errorf("transaction record not found for ID: %s", transactionID)
	}

	// Extract balance_after from transaction record
	balanceAfterInterface, ok := transactionRecord["balance_after"]
	if !ok {
		return errors.New("balance_after not found in transaction record")
	}

	balanceAfter, ok := balanceAfterInterface.(float64)
	if !ok {
		return errors.New("invalid balance_after format in transaction record")
	}

	// Extract withdrawal ID and transaction ID for updates
	withdrawalID, ok := withdrawalRecord["_id"].(string)
	if !ok {
		return errors.New("withdrawal _id not found")
	}

	// Step 3: Update withdrawal status to completed
	withdrawalUpdateData := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": withdrawalID,
				},
			},
		},
		"data": map[string]interface{}{
			"status": "completed",
		},
	}

	_, err = cloudbase.UpdateItem(ctx, "withdrawals", withdrawalUpdateData)
	if err != nil {
		return fmt.Errorf("failed to update withdrawal status: %w", err)
	}

	// Step 4: Update transaction status to completed
	transactionUpdateData := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": transactionID,
				},
			},
		},
		"data": map[string]interface{}{
			"status": "completed",
			"remark": "用户提现申请" + req.OutBillNo,
		},
	}

	_, err = cloudbase.UpdateItem(ctx, "transactions", transactionUpdateData)
	if err != nil {
		return fmt.Errorf("failed to update transaction status: %w", err)
	}

	// Step 5: Update user balance to balance_after
	userUpdateData := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"openid": map[string]interface{}{
					"$eq": req.OpenID,
				},
			},
		},
		"data": map[string]interface{}{
			"balance":  balanceAfter,
			"verified": true,
		},
	}

	s1, _ := prettyjson.Marshal(userUpdateData)
	fmt.Println("update user balance request: ", string(s1))

	result, err := cloudbase.UpdateItem(ctx, "users", userUpdateData)
	if err != nil {
		return fmt.Errorf("failed to update user balance: %w", err)
	}

	s2, _ := prettyjson.Marshal(result)
	fmt.Println("update user balance result: ", string(s2))

	cloudbase.CallFunction(ctx, "calc_user_total_withdrawals", map[string]interface{}{
		"user_id": userID,
	})

	return nil
}

func (w *WechatPayClient) CloseTransferBill(ctx context.Context, transferBillNo string) error {
	// w.Client.CancelTransferBill(ctx, transferBillNo)
	// call CancelTransferBill
	return nil
}

func GenerateOutBillNo() string {
	// Generate a string in format "T" + YYYYMMDDHHmmss + random 6 digits
	now := time.Now()
	timestamp := now.Format("20060102150405") // Go's format for YYYYMMDDHHmmss

	// Generate random 6-digit number (000000-999999)
	randomNum := rand.Intn(1000000)

	// Format with leading zeros if needed to ensure 6 digits
	return fmt.Sprintf("T%s%06d", timestamp, randomNum)
}

type CancelTransferBillPayload struct {
	Amount         float64 `json:"amount"`
	TransferBillNo string  `json:"transfer_bill_no"`
	OutBillNo      string  `json:"out_bill_no"`
	OpenID         string  `json:"openid"`
	UserID         string  `json:"user_id"` // required
}

// 用户在确认授权页面点叉叉 取消提现.
func CancelTransferBill(ctx context.Context, req *CancelTransferBillPayload) error {
	// close withdrawal
	withdrawalUpdateData := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"transfer_bill_no": map[string]interface{}{
					"$eq": req.TransferBillNo,
				},
				"out_bill_no": map[string]interface{}{
					"$eq": req.OutBillNo,
				},
			},
		},
		"data": map[string]interface{}{
			"status": "close",
		},
	}

	_, err := cloudbase.UpdateItem(ctx, "withdrawals", withdrawalUpdateData)
	if err != nil {
		return fmt.Errorf("failed to update withdrawal status: %w", err)
	}

	// close transaction
	transactionUpdateData := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"user_id": map[string]interface{}{
					"$eq": req.UserID,
				},
				"status": map[string]interface{}{
					"$eq": "pending",
				},
			},
		},
		"data": map[string]interface{}{
			"status": "close",
			"remark": "用户提现申请" + req.OutBillNo,
		},
	}

	_, err = cloudbase.UpdateItems(ctx, "transactions", transactionUpdateData)
	if err != nil {
		return fmt.Errorf("failed to update transaction status: %w", err)
	}

	userUpdateData := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"openid": map[string]interface{}{
					"$eq": req.OpenID,
				},
			},
		},
		"data": map[string]interface{}{
			// "balance":  balanceAfter,
			"verified": true,
		},
	}

	s1, _ := prettyjson.Marshal(userUpdateData)
	fmt.Println("update user balance request: ", string(s1))

	result, err := cloudbase.UpdateItem(ctx, "users", userUpdateData)
	if err != nil {
		return fmt.Errorf("failed to update user balance: %w", err)
	}

	s2, _ := prettyjson.Marshal(result)
	fmt.Println("update user balance result: ", string(s2))

	payClient := NewWechatPayClient()

	_, err = payClient.CancelTransferBill(ctx, req.OutBillNo)
	if err != nil {
		return err
	}

	return nil
}

// CheckIfUserAuditedForWithdrawal 返回true 表示可以提现.
func CheckIfUserAuditedForWithdrawal(openid, user_id string) bool {
	ctx := context.Background()
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"$or": []map[string]interface{}{
					{
						"openid": map[string]interface{}{
							"$eq": openid,
						},
						"user_id": map[string]interface{}{
							"$eq": user_id,
						},
					},
				},
			},
			"select": map[string]interface{}{
				"_id":     true,
				"audited": true,
			},
			"sort": map[string]interface{}{
				"created_at": "desc",
			},
			"orderBy": []map[string]interface{}{
				{
					"createdAt": "DESC", // 按创建时间倒序
				},
			},
			"pageSize":   1,
			"pageNumber": 1,
		},
	}

	record, err := cloudbase.GetItem(ctx, "users", q)
	if err != nil {
		return false
	}

	// Check if record is empty
	if len(record) == 0 {
		// No user found
		return false
	}

	var resp UserRecord
	if err := mapstructure.Decode(record, &resp); err != nil {
		fmt.Printf("failed to decode user record: %v\n", err)
		return false
	}

	return resp.Audited
}
