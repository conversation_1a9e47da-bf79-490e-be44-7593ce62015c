package pay

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// BillRequest holds the parameters for applying for a bill.
type BillRequest struct {
	BillDate    string // The date of the bill in YYYY-MM-DD format. Required.
	BillType    string // For TradeBill: ALL, SUCCESS, REFUND. Optional.
	AccountType string // For FundFlowBill: BASIC, OPERATION, FEES. Optional.
	TarType     string // Compression type: GZIP. Optional, defaults to GZIP.
}

// BillResponse is the response from applying for a bill.
type BillResponse struct {
	HashType    string `json:"hash_type"`
	HashValue   string `json:"hash_value"`
	DownloadURL string `json:"download_url"`
}

// TradeBill applies for a trade bill (交易账单).
// See: https://pay.weixin.qq.com/doc/v3/merchant/**********
func (w *WechatPayClient) TradeBill(ctx context.Context, params BillRequest) (*BillResponse, error) {
	endpoint := "https://api.mch.weixin.qq.com/v3/bill/tradebill"
	return w.requestBill(ctx, endpoint, params)
}

// FundFlowBill applies for a fund flow bill (资金账单).
// See: https://pay.weixin.qq.com/doc/v3/merchant/**********
func (w *WechatPayClient) FundFlowBill(ctx context.Context, params BillRequest) (*BillResponse, error) {
	endpoint := "https://api.mch.weixin.qq.com/v3/bill/fundflowbill"
	return w.requestBill(ctx, endpoint, params)
}

// requestBill is a helper function to request different types of bills.
func (w *WechatPayClient) requestBill(ctx context.Context, endpoint string, params BillRequest) (*BillResponse, error) {
	u, err := url.Parse(endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to parse endpoint URL: %w", err)
	}

	q := u.Query()
	q.Set("bill_date", params.BillDate)

	if params.BillType != "" {
		q.Set("bill_type", params.BillType)
	}

	if params.AccountType != "" {
		q.Set("account_type", params.AccountType)
	}
	// Default to GZIP if TarType is not specified.
	if params.TarType == "" {
		params.TarType = "GZIP"
	}

	q.Set("tar_type", params.TarType)
	u.RawQuery = q.Encode()

	requestURL := u.String()
	log.Printf("Requesting bill from URL: %s", requestURL)

	header := http.Header{}
	header.Set("Accept", "application/json")

	result, err := w.Client.Request(ctx, "GET", requestURL, header, nil, nil, "application/json")
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer result.Response.Body.Close()

	if result.Response.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(result.Response.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", result.Response.StatusCode, string(body))
	}

	body, err := io.ReadAll(result.Response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var resp BillResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return &resp, nil
}

func randomString(n int) string {
	letters := []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	b := make([]rune, n)

	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}

	return string(b)
}

// DownloadBill downloads the bill content from the provided URL.
// It uses the SDK的 Request 方法自动签名并发送请求。
// See: https://pay.weixin.qq.com/doc/v3/merchant/4013071238
func (w *WechatPayClient) DownloadBill(
	ctx context.Context,
	downloadURL string,
	billDate string,
	outputDir string,
) (string, error) {
	log.Printf("Attempting to download bill from URL: %s", downloadURL)

	// 先替换 \u0026 为 &
	fixedURL := strings.ReplaceAll(downloadURL, "\\u0026", "&")
	// 再做 URL 解码（可选）
	decodedURL, err := url.QueryUnescape(fixedURL)
	if err != nil {
		log.Printf("Failed to decode URL, use original: %v", err)

		decodedURL = fixedURL
	}

	fmt.Println("decodedURL", decodedURL)

	isGzip := strings.Contains(strings.ToLower(decodedURL), "tartype=gzip")

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, decodedURL, http.NoBody)
	if err != nil {
		return "", fmt.Errorf("failed to create new http request: %w", err)
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Wechatpay-Serial", "501074248E70FF853CFC617D79488B3CA1A99A39")

	// 构造签名参数
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonceStr := randomString(32)
	method := req.Method
	uri := req.URL.RequestURI()
	body := ""

	// 构造待签名字符串
	message := fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n", method, uri, timestamp, nonceStr, body)

	// 用 SDK 的 Sign 方法签名
	sigResult, err := w.Client.Sign(ctx, message)
	if err != nil {
		return "", fmt.Errorf("failed to sign the request: %w", err)
	}

	mchID := w.MchID
	serialNo := "501074248E70FF853CFC617D79488B3CA1A99A39"
	authHeader := fmt.Sprintf(
		`WECHATPAY2-SHA256-RSA2048 mchid="%s",serial_no="%s",nonce_str="%s",timestamp="%s",signature="%s"`,
		mchID, serialNo, nonceStr, timestamp, sigResult.Signature,
	)
	req.Header.Set("Authorization", authHeader)

	log.Printf("Request header before send: %+v", req.Header)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to execute http request: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("Response header: %+v", resp.Header)
	log.Printf("Response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("Download failed, status: %d, body: %s", resp.StatusCode, string(body))

		return "", fmt.Errorf("download failed with status %d: %s", resp.StatusCode, string(body))
	}

	var bodyBytes []byte

	var ext string

	switch {
	case isGzip && resp.Header.Get("Content-Encoding") == "gzip":
		// tartype=gzip 且响应为 gzip，解压并保存为 .csv
		log.Println("GZIP compression detected, decompressing bill...")

		gz, err := gzip.NewReader(resp.Body)
		if err != nil {
			log.Printf("Failed to create gzip reader: %v", err)
			return "", fmt.Errorf("failed to create gzip reader: %w", err)
		}

		defer gz.Close()

		bodyBytes, err = io.ReadAll(gz)
		if err != nil {
			log.Printf("Failed to read response body: %v", err)
			return "", fmt.Errorf("failed to read response body: %w", err)
		}

		ext = ".csv"
	case isGzip:
		// tartype=gzip 但响应不是 gzip，直接保存为 .csv.gz
		bodyBytes, err = io.ReadAll(resp.Body)
		if err != nil {
			log.Printf("Failed to read response body: %v", err)
			return "", fmt.Errorf("failed to read response body: %w", err)
		}

		ext = ".csv.gz"
	default:
		// 普通 csv
		reader := resp.Body

		if resp.Header.Get("Content-Encoding") == "gzip" {
			log.Println("GZIP compression detected, decompressing bill...")

			gz, err := gzip.NewReader(resp.Body)
			if err != nil {
				log.Printf("Failed to create gzip reader: %v", err)
				return "", fmt.Errorf("failed to create gzip reader: %w", err)
			}

			defer gz.Close()
			reader = gz
		}

		bodyBytes, err = io.ReadAll(reader)
		if err != nil {
			log.Printf("Failed to read response body: %v", err)
			return "", fmt.Errorf("failed to read response body: %w", err)
		}

		ext = ".csv"
	}

	// 账单日期格式化为 YYYYMMDD
	var dateStr string
	if billDate != "" {
		dateStr = strings.ReplaceAll(billDate, "-", "")
	} else {
		dateStr = time.Now().Format("20060102")
	}

	timestamp = strconv.FormatInt(time.Now().Unix(), 10)

	fileName := fmt.Sprintf("bill-%s-%s%s", dateStr, timestamp, ext)
	if outputDir != "" {
		fileName = filepath.Join(outputDir, fileName)
	}

	file, err := os.Create(fileName)
	if err != nil {
		log.Printf("Failed to create file: %v", err)
		return "", fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	// 只有 csv 写入 BOM，避免 Excel 打开乱码
	if !isGzip {
		bom := []byte{0xEF, 0xBB, 0xBF}

		_, err = file.Write(bom)
		if err != nil {
			log.Printf("Failed to write BOM: %v", err)
			return "", fmt.Errorf("failed to write BOM: %w", err)
		}
	}

	_, err = file.Write(bodyBytes)
	if err != nil {
		log.Printf("Failed to write file: %v", err)
		return "", fmt.Errorf("failed to write file: %w", err)
	}

	log.Printf("Successfully downloaded and saved bill, file: %s, size: %d bytes", fileName, len(bodyBytes))

	return fileName, nil
}
