package pay

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// BalanceResponse 定义余额查询返回结构
// 参考微信官方文档：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_1_9.shtml
// 响应字段可能会有调整，按官方文档为准
// 这里只保留常用字段

type BalanceResponse struct {
	AccountType     string `json:"account_type"`
	AvailableAmount int64  `json:"available_amount"`
	PendingAmount   int64  `json:"pending_amount"`
	AcctBalance     int64  `json:"acct_balance"`
	FrozenAmount    int64  `json:"frozen_amount"`
	// 还有其它字段可按需补充
}

// Balance 查询商户余额
// accountType: BASIC 或 OPERATION.
func (w *WechatPayClient) Balance(ctx context.Context, accountType string) (*BalanceResponse, error) {
	url := fmt.Sprintf("https://api.mch.weixin.qq.com/v3/merchant/fund/balance/%s", accountType)
	headers := http.Header{}
	headers.Set("Accept", "application/json")

	result, err := w.Client.Request(ctx, "GET", url, headers, nil, nil, "application/json")
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer result.Response.Body.Close()

	if result.Response.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(result.Response.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", result.Response.StatusCode, string(body))
	}

	body, err := io.ReadAll(result.Response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var resp BalanceResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return &resp, nil
}
