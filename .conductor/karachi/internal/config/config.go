package config

import (
	"encoding/json"
	"errors"
	"log"
	"os"

	"github.com/spf13/viper"

	"tandian-server/internal/logger"
)

type Config struct {
	AppEnv         string          `json:"app_env"         mapstructure:"app_env"`
	Redis          RedisConfig     `json:"redis"           mapstructure:"redis"`
	Asynq          AsynqConfig     `json:"asynq"           mapstructure:"asynq"`
	Log            LogConfig       `json:"log"             mapstructure:"log"`
	AppServerPort  int             `json:"app_server_port" mapstructure:"app_server_port"`
	MonitoringPort int             `json:"monitoring_port" mapstructure:"monitoring_port"`
	WechatPay      WechatPayConfig `json:"wechat_pay"      mapstructure:"wechat_pay"`
	Wecom          Wecom           `json:"wecom"           mapstructure:"wecom"`
	CloudBase      CloudBase       `json:"cloudbase"       mapstructure:"cloudbase"`
	JWTSecret      string          `json:"jwt_secret"      mapstructure:"jwt_secret"`
	Flagsmith      FlagsmithConfig `json:"flagsmith"       mapstructure:"flagsmith"`
}

type WechatPayConfig struct {
	AppID                      string `json:"app_id"                        mapstructure:"app_id"`
	MchID                      string `json:"mch_id"                        mapstructure:"mch_id"`
	MchCertificateSerialNumber string `json:"mch_certificate_serial_number" mapstructure:"mch_certificate_serial_number"`
	MchAPIv3Key                string `json:"mch_api_v3_key"                mapstructure:"mch_api_v3_key"`
	MchAPIv2Key                string `json:"mch_api_v2_key"                mapstructure:"mch_api_v2_key"` // V2 API key for merchant exclusive coupon signature
	PrivateKeyPath             string `json:"private_key_path"              mapstructure:"private_key_path"`
}

type Wecom struct {
	CorpID      string `json:"corp_id"      mapstructure:"corp_id"`
	AgentID     int64  `json:"agent_id"     mapstructure:"agent_id"`
	Secret      string `json:"secret"       mapstructure:"secret"`
	CallbackUrl string `json:"callback_url" mapstructure:"callback_url"`
}

type CloudBase struct {
	EnvID string `json:"env_id" mapstructure:"env_id"`
}

type FlagsmithConfig struct {
	EnvironmentKey string `json:"environment_key" mapstructure:"environment_key"`
	BaseURL        string `json:"base_url"        mapstructure:"base_url"`
	LocalEval      bool   `json:"local_eval"      mapstructure:"local_eval"`
	Enabled        bool   `json:"enabled"         mapstructure:"enabled"`
}

type MySQLConfig struct {
	Host         string `json:"host"           mapstructure:"host"`
	User         string `json:"user"           mapstructure:"user"`
	Password     string `json:"password"       mapstructure:"password"`
	DB           string `json:"db"             mapstructure:"db"`
	Port         int    `json:"port"           mapstructure:"port"`
	MaxOpenConns int    `json:"max_open_conns" mapstructure:"max_open_conns"`
	MaxIdleConns int    `json:"max_idle_conns" mapstructure:"max_idle_conns"`
}

type Cors struct {
	AllowOrigins []string `mapstructure:"allow_origins"`
}

// type RedisConfig struct {
// 	Host     string `mapstructure:"host"`
// 	Password string `mapstructure:"password"`
// 	Port     int    `mapstructure:"port"`
// 	DB       int    `mapstructure:"db"`
// 	PoolSize int    `mapstructure:"pool_size"`
// }

type LogConfig struct {
	Level   string            `json:"level"   mapstructure:"level"`
	Modules map[string]string `json:"modules" mapstructure:"modules"`
}

type RedisConfig struct {
	Addr     string `json:"addr"     mapstructure:"addr"`
	Password string `json:"password" mapstructure:"password"`
	DB       int    `json:"db"       mapstructure:"db"`
}

type AsynqConfig struct {
	RedisAddr             string `json:"redis_addr"               mapstructure:"redis_addr"`
	RedisPassword         string `json:"redis_password"           mapstructure:"redis_password"`
	RedisDB               int    `json:"redis_db"                 mapstructure:"redis_db"`
	UseDynamicTaskManager bool   `json:"use_dynamic_task_manager" mapstructure:"use_dynamic_task_manager"`
}

var (
	AppConfig Config
	loaded    bool = false
)

type ConfigOptions struct {
	Silence bool
}

func LoadConfig(opts ...ConfigOptions) {
	if loaded {
		return
	}

	silent := false // 默认值
	if len(opts) > 0 {
		silent = opts[0].Silence
	}

	// Set default values first
	viper.SetDefault("app_env", "development") // Set default for AppEnv
	viper.SetDefault("redis.addr", "127.0.0.1:6379")
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("asynq.redis_addr", "127.0.0.1:6379")
	viper.SetDefault("asynq.redis_password", "")
	viper.SetDefault("asynq.redis_db", 0)
	viper.SetDefault("asynq.use_dynamic_task_manager", true)
	viper.SetDefault("monitoring_port", 8080)
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.modules.redis", "warn")
	viper.SetDefault("log.modules.server", "info")
	viper.SetDefault("log.modules.util", "error")
	viper.SetDefault("log.modules.tasks:note", "debug")
	viper.SetDefault("log.modules.tasks:payment", "info")
	viper.SetDefault("log.modules.tasks:user", "info")
	viper.SetDefault("log.modules.tasks:order", "info")
	viper.SetDefault("log.modules.tasks:cloudbase_token", "info")
	viper.SetDefault("log.modules.tasks:wecom_token", "info")
	viper.SetDefault("flagsmith.enabled", false)
	viper.SetDefault("flagsmith.local_eval", false)

	// 1. Load base config file (config.yaml)
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("/etc/tandian-server/")
	viper.AddConfigPath("$HOME/.tandian-server/")

	if err := viper.ReadInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if errors.As(err, &configFileNotFoundError) {
			log.Println("Config file 'config.yaml' not found, using defaults and other sources.")
		} else {
			log.Printf("Error reading config file: %s\n", err)
			os.Exit(1)
		}
	}

	// 2. Merge .env file, which can override config.yaml
	viper.SetConfigFile(".env")
	viper.SetConfigType("env")

	if err := viper.MergeInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if errors.As(err, &configFileNotFoundError) {
			log.Printf("Warning: Cannot merge .env file: %s\n", err)
		}
	}

	// 3. Merge .env.local file, which can override .env and config.yaml
	viper.SetConfigFile(".env.local")
	viper.SetConfigType("env")

	if err := viper.MergeInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if errors.As(err, &configFileNotFoundError) {
			log.Printf("Warning: Cannot merge .env.local file: %s\n", err)
		}
	}

	// 4. Allow environment variables to override everything.
	// This has the highest priority.
	viper.SetEnvPrefix("TANDIAN")
	viper.AutomaticEnv()

	// Unmarshal the final configuration into the struct
	if err := viper.Unmarshal(&AppConfig); err != nil {
		log.Printf("Unable to decode config into struct, %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	if err := logger.Init(AppConfig.AppEnv, AppConfig.Log.Level, AppConfig.Log.Modules); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	if !silent {
		configBytes, err := json.MarshalIndent(AppConfig, "", "  ")
		if err != nil {
			log.Printf("Error marshaling config to JSON: %v\n", err)
		} else {
			log.Printf("Loaded configuration:\n%s\n", string(configBytes))
		}
	}

	// zap.L().Sugar().Info("Loaded configuration", zap.Any("config", AppConfig))
	loaded = true
}

//  func init() {
//     zap.ReplaceGlobals(zap.Must(zap.NewProduction()))
// }
