package tasks

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"text/template"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
)

const (
	TypeAutoEnqueueCancelOverduePaymentOrders = "scheduler:enqueue_cancel_overdue_payment_orders"
	TypeAutoCancelOverduePaymentOrder         = "scheduler:cancel_overdue_payment_order"
)

type AutoCancelOverduePaymentOrderPayload struct {
	OrderID string `json:"order_id"`
	OrderNo string `json:"order_no"`
}

func HandleAutoEnqueueCancelOverduePaymentOrdersTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:order").With(zap.String("task_type", task.Type()))
	log.Info("HandleAutoEnqueueCancelOverduePaymentOrderTask")

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/orders/list"
	method := "POST"

	// Define your template
	tmplString := `{
		"filter": {
			"where": {
				"status": {
					"$eq": 0
				}
			}
		},
		"select": {
			"_id": true,
			"order_no": true
		},
		"pageSize": {{.PageSize}},
		"pageNumber": {{.PageNumber}},
		"getCount": true,
		"orderBy": [
			{
				"createdAt": "desc"
			}
		]
	}`
	pageSize := 200
	currentPage := 1
	totalCount := 0

	for {
		// Data to inject into the template
		data := struct {
			PageSize   int
			PageNumber int
		}{
			PageSize:   pageSize,
			PageNumber: currentPage,
		}

		// Create and execute the template
		tmpl := template.Must(template.New("payload").Parse(tmplString))

		var buf bytes.Buffer

		err := tmpl.Execute(&buf, data)
		if err != nil {
			log.Error("Template error", zap.Error(err))
			return err
		}

		client := &http.Client{}

		req, err := http.NewRequest(method, url, strings.NewReader(buf.String()))
		if err != nil {
			log.Error("Request creation error", zap.Error(err))
			return err
		}

		rdb := redis.GetClient()

		cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
		if err != nil {
			log.Error("Failed to get cloudbase_access_token", zap.Error(err))
			return err
		}
		// log.Debug("cloudbaseAccessToken:", zap.String("token", cloudbaseAccessToken))

		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
		req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

		res, err := client.Do(req)
		if err != nil {
			log.Error("Request execution error", zap.Error(err))
			return err
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Error("Response reading error", zap.Error(err))
			return err
		}

		var resp ListNoteResponse

		err = json.Unmarshal(body, &resp)
		if err != nil {
			log.Error("JSON unmarshal error", zap.Error(err))
			return err
		}

		// 如果是第一页，获取总数
		if currentPage == 1 {
			if total, ok := resp.Data.Total.(float64); ok {
				log.Info("totalCount", zap.Float64("total", total))
				totalCount = int(total)
				// totalCount = 5
			} else {
				return nil
			}
		}

		// 处理当前页的数据
		for index, note := range resp.Data.Records {
			log.Info("Processing order",
				zap.String("orderID", note.ID),
				zap.Int("index", (pageSize*(currentPage-1))+index+1),
				zap.Int("total", totalCount),
			)
			enqueueAutoCancelOverduePaymentOrderTask(note.ID)
		}

		// 检查是否还有下一页
		if currentPage*pageSize >= totalCount {
			break
		}

		currentPage++
	}

	return nil
}

func NewAutoEnqueueCancelOverduePaymentOrderTask() (*asynq.Task, error) {
	return asynq.NewTask(
		TypeAutoEnqueueCancelOverduePaymentOrders,
		nil,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("order"),
	), nil
}

func NewCancelOverduePaymentOrderTask(orderID string) (*asynq.Task, error) {
	payload, err := json.Marshal(AutoCancelOverduePaymentOrderPayload{OrderID: orderID})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(TypeAutoCancelOverduePaymentOrder,
		payload,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("order"),
	), nil
}

func HandleAutoCancelOverduePaymentOrderTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:order").With(zap.String("task_type", task.Type()))
	log.Info("HandleAutoCancelOverduePaymentOrderTask")

	var p AutoCancelOverduePaymentOrderPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		log.Error("json.Unmarshal failed", zap.Error(err))
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/functions/cancel_overdue_payment_order"
	method := "POST"

	// Define your template
	tmplString := `{
			"order_id": "{{.OrderID}}"
		}`
	// Data to inject into the template
	data := struct {
		OrderID string
	}{
		OrderID: p.OrderID,
	}

	// Create and execute the template
	tmpl := template.Must(template.New("payload").Parse(tmplString))

	var buf bytes.Buffer

	err := tmpl.Execute(&buf, data)
	if err != nil {
		panic(err)
	}
	// log.Debug("payload:", zap.String("payload", buf.String()))

	// Get the final payload
	payload := strings.NewReader(buf.String())

	// Send the request
	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Error("http.NewRequest failed", zap.Error(err))
		return err
	}

	rdb := redis.GetClient()

	cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
	if err != nil {
		log.Error("Failed to get cloudbase_access_token", zap.Error(err))
		return err
	}
	// log.Debug("cloudbaseAccessToken:", zap.String("token", cloudbaseAccessToken))

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

	res, err := client.Do(req)
	if err != nil {
		log.Error("client.Do failed", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("io.ReadAll failed", zap.Error(err))
		return err
	}

	log.Debug("response body", zap.String("body", string(body)))

	if _, err := task.ResultWriter().Write(body); err != nil {
		log.Error("Failed to write task result", zap.Error(err))
		return fmt.Errorf("failed to write task result: %w", err)
	}

	return nil
}

func enqueueAutoCancelOverduePaymentOrderTask(orderID string) error {
	log := logger.Get("tasks:order").With(zap.String("order_id", orderID))
	task, err := NewCancelOverduePaymentOrderTask(orderID)
	if err != nil {
		log.Error("NewCancelOverduePaymentOrderTask failed", zap.Error(err))
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	// Process between 1-30 seconds later
	delay := time.Duration(rand.Intn(30)+1) * time.Second

	info, err := client.Enqueue(
		task,
		asynq.ProcessIn(delay),
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("order"),
		asynq.Retention(24*time.Hour),
	)
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return err
	}

	log.Info(
		"enqueued task",
		zap.String("id", info.ID),
		zap.String("queue", info.Queue),
		zap.String("type", task.Type()),
	)

	return nil
}

// type ListNoteResponse struct {
// 	Data struct {
// 		Records []struct {
// 			ID     string `json:"_id"`
// 			Status int    `json:"status"`
// 		} `json:"records"`
// 		Total interface{} `json:"total"`
// 	} `json:"data"`
// 	RequestID string `json:"requestId"`
// }

// type UpdateSingleDataResponse struct {
// 	Data struct {
// 		Count int `json:"count"`
// 	} `json:"data"`
// 	RequestID string `json:"requestId"`
// }
