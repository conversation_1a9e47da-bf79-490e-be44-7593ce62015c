package tasks

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"text/template"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
)

const (
	TypeEnqueueRefreshUserMoney = "scheduler:enqueue_refresh_user_money"
	TypeRefreshUserMoney        = "scheduler:refresh_user_money"
)

type RefreshUserMoneyPayload struct {
	UserID string `json:"user_id"`
}

func NewRefreshUserMoneyTask(userID string) (*asynq.Task, error) {
	log := logger.Get("tasks:user")
	payload, err := json.Marshal(RefreshUserMoneyPayload{UserID: userID})
	if err != nil {
		log.Error("Failed to marshal payload", zap.Error(err))
		return nil, err
	}

	log.Debug("Creating new refresh user money task", zap.String("userID", userID))

	// Process between 1-5000 seconds later
	delay := time.Duration(rand.Intn(5000)+1) * time.Second

	return asynq.NewTask(
		TypeRefreshUserMoney,
		payload,
		asynq.MaxRetry(2),
		asynq.ProcessIn(delay),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("daily"),
		asynq.Retention(30*24*time.Hour),
	), nil
}

func HandleRefreshUserMoneyTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:user").With(zap.String("task_type", task.Type()))
	log.Info("Handling refresh user money task")

	var p RefreshUserMoneyPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		log.Error("Failed to unmarshal payload", zap.Error(err))
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/functions/calc_user_money2"
	method := "POST"

	// Define your template
	tmplString := `{
			"user_id": "{{.UserID}}"
		}`
	// Data to inject into the template
	data := struct {
		UserID string
	}{
		UserID: p.UserID,
	}

	// Create and execute the template
	tmpl := template.Must(template.New("payload").Parse(tmplString))

	var buf bytes.Buffer

	err := tmpl.Execute(&buf, data)
	if err != nil {
		log.Error("Failed to execute template", zap.Error(err))
		return err
	}

	log.Debug("calc_user_money payload", zap.String("payload", buf.String()))

	// Get the final payload
	payload := strings.NewReader(buf.String())

	// Send the request
	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Error("Failed to create http request", zap.Error(err))
		return err
	}

	rdb := redis.GetClient()

	cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
	if err != nil {
		log.Error("Failed to get cloudbase_access_token from redis", zap.Error(err))
		return err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

	res, err := client.Do(req)
	if err != nil {
		log.Error("Failed to execute http request", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("Failed to read response body", zap.Error(err))
		return err
	}

	log.Info("calc_user_money response", zap.String("body", string(body)))

	return nil
}

// 自动enque 刷新用户余额的任务.
func NewAutoEnqueueRefreshUserMoneyTask() (*asynq.Task, error) {
	return asynq.NewTask(
		TypeEnqueueRefreshUserMoney,
		nil,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("daily"),
		asynq.Retention(30*24*time.Hour),
	), nil
}

func HandleAutoEnqueueRefreshUserMoneyTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:user").With(zap.String("task_type", task.Type()))
	log.Info("Handling auto enqueue refresh user money task")

	rdb := redis.GetClient()

	cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
	if err != nil {
		log.Error("Failed to get cloudbase_access_token from redis", zap.Error(err))
		return err
	}

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/users/list"
	method := "POST"

	// Define your template
	tmplString := `{
		"select": {
			"_id": true
		},
		"pageSize": {{.PageSize}},
		"pageNumber": {{.PageNumber}},
		"getCount": true,
		"orderBy": [
			{
				"createdAt": "desc"
			}
		]
	}`

	pageSize := 200
	currentPage := 1
	totalUsers := 0

	for {
		// Data to inject into the template
		data := struct {
			PageSize   int
			PageNumber int
		}{
			PageSize:   pageSize,
			PageNumber: currentPage,
		}

		// Create and execute the template
		tmpl := template.Must(template.New("payload").Parse(tmplString))

		var buf bytes.Buffer

		err := tmpl.Execute(&buf, data)
		if err != nil {
			log.Error("Template error", zap.Error(err))
			return err
		}

		client := &http.Client{}

		req, err := http.NewRequest(method, url, strings.NewReader(buf.String()))
		if err != nil {
			log.Error("Request creation error", zap.Error(err))
			return err
		}

		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
		req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

		res, err := client.Do(req)
		if err != nil {
			log.Error("Request execution error", zap.Error(err))
			return err
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Error("Failed to read response body", zap.Error(err))
			return err
		}

		var listUserResponse ListUserResponse

		err = json.Unmarshal(body, &listUserResponse)
		if err != nil {
			log.Error("JSON unmarshal error", zap.Error(err))
			return err
		}

		// 如果是第一页，获取总用户数
		if currentPage == 1 {
			if total, ok := listUserResponse.Data.Total.(float64); ok {
				log.Info("Total users to process", zap.Float64("total", total))
				totalUsers = int(total)
				// totalUsers = 5
			} else {
				log.Warn("Failed to get total users count")
				return nil
			}
		}

		// 处理当前页的用户数据
		for index, user := range listUserResponse.Data.Records {
			log.Info(
				"Processing user",
				zap.String("userID", user.ID),
				zap.Int("index", (pageSize*(currentPage-1))+index+1),
			)
			enqueueRefreshUserMoneyTask(user.ID)
		}

		log.Info("Finished processing users", zap.Int("total", totalUsers))

		// 检查是否还有下一页
		if currentPage*pageSize >= totalUsers {
			break
		}

		currentPage++
	}

	return nil
}

type ListUserResponse struct {
	Data struct {
		Records []struct {
			Unionid                          string  `json:"unionid"`
			ReferrerID                       string  `json:"referrer_id"`
			TotalPoints                      float64 `json:"total_points"`
			TotalRewards                     float64 `json:"total_rewards"`
			UID                              float64 `json:"uid"`
			Effective                        bool    `json:"effective"`
			CreatedAt                        int64   `json:"createdAt"`
			Balance                          float64 `json:"balance"`
			LastSignInAt                     int64   `json:"last_sign_in_at"`
			RemainCoupons                    float64 `json:"remain_coupons"`
			UpdateBy                         string  `json:"updateBy"`
			Nickname                         string  `json:"nickname"`
			Banned                           bool    `json:"banned"`
			UpdatedAt                        int64   `json:"updatedAt"`
			DailyCoupons                     float64 `json:"daily_coupons"`
			Owner                            string  `json:"owner"`
			OpenID                           string  `json:"openid"`
			CurrentMonthTotalOrderAmount     float64 `json:"current_month_total_order_amount"`
			Avatar                           string  `json:"avatar"`
			TotalWithdrawals                 float64 `json:"total_withdrawals"`
			CreateBy                         string  `json:"createBy"`
			CurrentTeamMonthTotalOrderAmount float64 `json:"current_team_month_total_order_amount"`
			UserLevel                        float64 `json:"user_level"`
			ID                               string  `json:"_id"`
			Phone                            string  `json:"phone,omitempty"`
		} `json:"records"`
		Total interface{} `json:"total"`
	} `json:"data"`
	RequestID string `json:"requestId"`
}

func enqueueRefreshUserMoneyTask(userID string) {
	log := logger.Get("tasks:user")
	task, err := NewRefreshUserMoneyTask(userID)
	if err != nil {
		log.Error("Failed to create new refresh user money task", zap.Error(err))
		return
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	// Process between 1-900 seconds later
	delay := time.Duration(rand.Intn(900)+1) * time.Second

	info, err := client.Enqueue(
		task,
		asynq.ProcessIn(delay),
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("daily"),
		asynq.Retention(30*24*time.Hour),
	)
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return
	}

	log.Info("Enqueued task", zap.String("id", info.ID), zap.String("queue", info.Queue))
}
