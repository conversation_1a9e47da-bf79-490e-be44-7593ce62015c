package tasks

import (
	"context"
	"fmt"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
)

// Example usage of UpdateUserNoteTraits task
// This file demonstrates how to integrate the task in your application

// Example 1: Enqueue task after note status change.
func ExampleEnqueueAfterNoteStatusChange(userID, noteID, newStatus string) error {
	log := logger.Get("example")

	// Check if the status change is relevant for trait updates
	// Status 31 = Approved, Status 32 = Rejected, Status 30 = Under Review
	if newStatus == "31" || newStatus == "32" || newStatus == "30" {
		// Enqueue the task to update user note traits
		err := EnqueueUpdateUserNoteTraitsTask(userID)
		if err != nil {
			log.Error("Failed to enqueue update user note traits task",
				zap.String("user_id", userID),
				zap.String("note_id", noteID),
				zap.String("new_status", newStatus),
				zap.Error(err))
			return err
		}

		log.Info("Enqueued user note traits update",
			zap.String("user_id", userID),
			zap.String("note_id", noteID),
			zap.String("new_status", newStatus))
	}

	return nil
}

// Example 2: Integration in order handler.
func ExampleIntegrationInOrderHandler(ctx context.Context, orderID, userID, status string) error {
	log := logger.Get("order_handler")

	// Your existing order update logic here...
	// ...

	// After successfully updating order status, enqueue trait update
	if status == "31" || status == "32" {
		err := EnqueueUpdateUserNoteTraitsTask(userID)
		if err != nil {
			// Log error but don't fail the order update
			log.Error("Failed to enqueue trait update",
				zap.String("order_id", orderID),
				zap.String("user_id", userID),
				zap.Error(err))
		}
	}

	return nil
}

// Example 3: Batch update for multiple users.
func ExampleBatchUpdateUserTraits(userIDs []string) error {
	log := logger.Get("batch_update")

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	successCount := 0
	failureCount := 0

	for _, userID := range userIDs {
		task, err := NewUpdateUserNoteTraitsTask(userID)
		if err != nil {
			log.Error("Failed to create task",
				zap.String("user_id", userID),
				zap.Error(err))
			failureCount++
			continue
		}

		_, err = client.Enqueue(task)
		if err != nil {
			log.Error("Failed to enqueue task",
				zap.String("user_id", userID),
				zap.Error(err))
			failureCount++
			continue
		}

		successCount++
	}

	log.Info("Batch update completed",
		zap.Int("success_count", successCount),
		zap.Int("failure_count", failureCount),
		zap.Int("total", len(userIDs)))

	if failureCount > 0 {
		return fmt.Errorf("failed to enqueue %d out of %d tasks", failureCount, len(userIDs))
	}

	return nil
}

// Example 4: Integration points in your application
//
// 1. In notes_handler.go when note is submitted:
//    - When order status changes to "30" (under review)
//    - Call: EnqueueUpdateUserNoteTraitsTask(userID)
//
// 2. In admin panel or moderation system:
//    - When note is approved (status -> "31")
//    - When note is rejected (status -> "32")
//    - Call: EnqueueUpdateUserNoteTraitsTask(userID)
//
// 3. In auto_pass_note.go task:
//    - After automatically approving a note
//    - Call: EnqueueUpdateUserNoteTraitsTask(userID)
//
// 4. In scheduled jobs:
//    - Daily sync of all user note traits
//    - Call: ExampleBatchUpdateUserTraits(allUserIDs)
//
// 5. In webhook handlers:
//    - When receiving note status update from external system
//    - Call: EnqueueUpdateUserNoteTraitsTask(userID)

// Example 5: Direct usage in HTTP handler.
func ExampleHTTPHandler() {
	// This would be in your actual HTTP handler
	// e.g., in cmd/app/notes_handler.go

	// After processing note update...
	userID := "user_123"

	// Fire and forget - don't block the HTTP response
	go func() {
		err := EnqueueUpdateUserNoteTraitsTask(userID)
		if err != nil {
			log := logger.Get("http_handler")
			log.Error("Failed to enqueue trait update",
				zap.String("user_id", userID),
				zap.Error(err))
		}
	}()
}

// Example 6: CLI command to update a specific user's traits.
func ExampleCLICommand(userID string) error {
	// This could be called from cmd/cli/main.go
	log := logger.Get("cli")

	log.Info("Updating user note traits via CLI",
		zap.String("user_id", userID))

	err := EnqueueUpdateUserNoteTraitsTask(userID)
	if err != nil {
		return fmt.Errorf("failed to enqueue task: %w", err)
	}

	log.Info("Task enqueued successfully",
		zap.String("user_id", userID))

	return nil
}
