package tasks

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"text/template"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
	"tandian-server/internal/util"
	"tandian-server/internal/util/cloudbase"
)

const (
	TypeAutoEnqueueSendNoteLinkTask = "scheduler:auto_enqueue_send_note_link"
	TypeAutoEnqueuePassNoteTask     = "scheduler:auto_enqueue_pass_note"
	TypeAutoSendNoteLink            = "scheduler:auto_send_note_link"
	TypeAutoPassNote                = "scheduler:auto_pass_the_note"
)

type AutoPassNotePayload struct {
	NoteID string `json:"note_id"`
}

type AutoSendNoteLinkPayload struct {
	NoteID string `json:"note_id"`
}

func AutoEnqueueSendNoteLinkTask() (*asynq.Task, error) {
	return asynq.NewTask(
			TypeAutoEnqueueSendNoteLinkTask,
			nil,
			asynq.MaxRetry(2),
			asynq.Timeout(5*time.Minute),
			asynq.Queue("note")),
		nil
}

func AutoEnqueuePassNoteTask() (*asynq.Task, error) {
	return asynq.NewTask(
			TypeAutoEnqueuePassNoteTask,
			nil,
			asynq.MaxRetry(2),
			asynq.Timeout(5*time.Minute),
			asynq.Queue("note")),
		nil
}

func HandleAutoEnqueueSendNoteLinkTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:note")
	log.Info("HandleAutoEnqueueSendNoteLinkTask")

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/notes/list"
	method := "POST"

	// Define your template
	tmplString := `{
		"filter": {
			"where": {
				"status": {
					"$eq": 3
				}
			}
		},
		"select": {
			"_id": true
		},
		"pageSize": {{.PageSize}},
		"pageNumber": {{.PageNumber}},
		"getCount": true,
		"orderBy": [
			{
				"createdAt": "desc"
			}
		]
	}`
	pageSize := 200
	currentPage := 1
	totalCount := 0

	for {
		// Data to inject into the template
		data := struct {
			PageSize   int
			PageNumber int
		}{
			PageSize:   pageSize,
			PageNumber: currentPage,
		}

		// Create and execute the template
		tmpl := template.Must(template.New("payload").Parse(tmplString))

		var buf bytes.Buffer

		err := tmpl.Execute(&buf, data)
		if err != nil {
			log.Error("Template error", zap.Error(err))
			return err
		}

		client := &http.Client{}

		req, err := http.NewRequest(method, url, strings.NewReader(buf.String()))
		if err != nil {
			log.Error("Request creation error", zap.Error(err))
			return err
		}

		rdb := redis.GetClient()

		cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
		if err != nil {
			log.Error("Failed to get cloudbase_access_token", zap.Error(err))
			return err
		}

		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
		req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

		res, err := client.Do(req)
		if err != nil {
			log.Error("Request execution error", zap.Error(err))
			return err
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Error("Response reading error", zap.Error(err))
			return err
		}

		var resp ListNoteResponse

		err = json.Unmarshal(body, &resp)
		if err != nil {
			log.Error("JSON unmarshal error", zap.Error(err))
			return err
		}

		// 如果是第一页，获取总用户数
		if currentPage == 1 {
			if total, ok := resp.Data.Total.(float64); ok {
				log.Info("Total notes to process", zap.Float64("total", total))
				totalCount = int(total)
				// totalCount = 5
			} else {
				return nil
			}
		}

		// 处理当前页的数据
		for index, note := range resp.Data.Records {
			log.Info("Processing note",
				zap.String("note_id", note.ID),
				zap.Int("index", (pageSize*(currentPage-1))+index+1),
				zap.Int("total", totalCount),
			)
			enqueueAutoSendNoteLinkTask(note.ID)
		}

		// 检查是否还有下一页
		if currentPage*pageSize >= totalCount {
			break
		}

		currentPage++
	}

	return nil
}

func HandleAutoEnqueuePassNoteTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:note").With(zap.String("task_type", task.Type()))
	log.Info("HandleAutoEnqueuePassNoteTask")

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/notes/list"
	method := "POST"

	// Define your template
	tmplString := `{
		"filter": {
			"where": {
				"status": {
					"$eq": 4
				}
			}
		},
		"select": {
			"_id": true
		},
		"pageSize": {{.PageSize}},
		"pageNumber": {{.PageNumber}},
		"getCount": true,
		"orderBy": [
			{
				"createdAt": "desc"
			}
		]
	}`
	pageSize := 200
	currentPage := 1
	totalCount := 0

	for {
		// Data to inject into the template
		data := struct {
			PageSize   int
			PageNumber int
		}{
			PageSize:   pageSize,
			PageNumber: currentPage,
		}

		// Create and execute the template
		tmpl := template.Must(template.New("payload").Parse(tmplString))

		var buf bytes.Buffer

		err := tmpl.Execute(&buf, data)
		if err != nil {
			log.Error("Template error", zap.Error(err))
			return err
		}

		client := &http.Client{}

		req, err := http.NewRequest(method, url, strings.NewReader(buf.String()))
		if err != nil {
			log.Error("Request creation error", zap.Error(err))
			return err
		}

		rdb := redis.GetClient()

		cloudbaseAccessToken, err := rdb.Get(ctx, "cloudbase_access_token").Result()
		if err != nil {
			log.Error("Failed to get cloudbase_access_token", zap.Error(err))
			return err
		}

		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
		req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

		res, err := client.Do(req)
		if err != nil {
			log.Error("Request execution error", zap.Error(err))
			return err
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Error("Response reading error", zap.Error(err))
			return err
		}

		var resp ListNoteResponse

		err = json.Unmarshal(body, &resp)
		if err != nil {
			log.Error("JSON unmarshal error", zap.Error(err))
			return err
		}

		// 如果是第一页，获取总用户数
		if currentPage == 1 {
			if total, ok := resp.Data.Total.(float64); ok {
				log.Info("Total notes to process", zap.Float64("total", total))
				totalCount = int(total)
			} else {
				return nil
			}
		}

		// 处理当前页的数据
		for index, note := range resp.Data.Records {
			log.Info("Processing note",
				zap.String("note_id", note.ID),
				zap.Int("index", (pageSize*(currentPage-1))+index+1),
				zap.Int("total", totalCount),
			)
			enqueueAutoSendNoteLinkTask(note.ID)
		}

		// 检查是否还有下一页
		if currentPage*pageSize >= totalCount {
			break
		}

		currentPage++
	}

	return nil
}

func NewAutoSendNoteLinkTask(noteID string) (*asynq.Task, error) {
	payload, err := json.Marshal(AutoSendNoteLinkPayload{NoteID: noteID})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(
		TypeAutoSendNoteLink,
		payload,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("note"),
		asynq.Retention(24*time.Hour),
		asynq.Unique(24*time.Hour),
		asynq.TaskID(fmt.Sprintf("SENT_%s", noteID)),
	), nil
}

// 发送笔记链接到企业微信群组(在预审通过后，延时5分钟执行).
func HandleAutoSendNoteLinkTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:note").With(zap.String("task_type", task.Type()))

	var p AutoSendNoteLinkPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		log.Error("json.Unmarshal failed", zap.Error(err))
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	log.Info("HandleAutoSendNoteLinkTask", zap.String("noteID", p.NoteID))

	// 发送笔记链接到企业微信群组
	// then 3 -> 4
	cloudbase.CallFunction(ctx, "send_to_wecom_for_boss", map[string]interface{}{
		"note_id": p.NoteID,
	})

	rdb := redis.GetClient()

	cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
	if err != nil {
		log.Error("Failed to get cloudbase_access_token", zap.Error(err))
		return err
	}

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/notes/update"
	method := "PUT"

	// Define your template
	tmplString := `{
		"filter": {
			"where": {
				"_id": {
					"$eq": "{{.NoteID}}"
				}
			}
		},
		"data": {
			"status": {{.Status}}
		}
	}`

	data := struct {
		NoteID string
		Status int
	}{
		NoteID: p.NoteID,
		Status: 4,
	}

	// Create and execute the template
	tmpl := template.Must(template.New("payload").Parse(tmplString))

	var buf bytes.Buffer

	err = tmpl.Execute(&buf, data)
	if err != nil {
		log.Error("Template error", zap.Error(err))
		return err
	}

	client := &http.Client{}

	req, err := http.NewRequest(method, url, strings.NewReader(buf.String()))
	if err != nil {
		log.Error("Request creation error", zap.Error(err))
		return err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

	res, err := client.Do(req)
	if err != nil {
		log.Error("Request execution error", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("Response reading error", zap.Error(err))
		return err
	}

	log.Info("Response body", zap.String("body", string(body)))

	var resp UpdateSingleDataResponse

	err = json.Unmarshal(body, &resp)
	if err != nil {
		log.Error("JSON unmarshal error", zap.Error(err))
		return err
	}

	log.Info("requestID", zap.String("requestID", resp.RequestID))

	if _, err := task.ResultWriter().Write(body); err != nil {
		log.Error("Failed to write task result", zap.Error(err))
		return fmt.Errorf("failed to write task result: %w", err)
	}

	// 3 -> 4
	enqueueAutoPassNoteTask(p.NoteID)

	return nil
}

func NewAutoPassNoteTask(noteID string) (*asynq.Task, error) {
	payload, err := json.Marshal(AutoPassNotePayload{NoteID: noteID})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(
			TypeAutoPassNote,
			payload,
			asynq.MaxRetry(2),
			asynq.Timeout(5*time.Minute),
			asynq.Queue("note"),
			asynq.Retention(24*time.Hour),
			asynq.Unique(24*time.Hour),
			asynq.TaskID(fmt.Sprintf("PASS_%s", noteID)),
		),
		nil
}

func HandleAutoPassNoteTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:note")
	log.Info("Executing HandleAutoPassNoteTask", zap.String("note_id", task.Type()))

	var p AutoPassNotePayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		log.Error("json.Unmarshal failed", zap.Error(err))
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	// TODO:
	// 4 -> 1
	// 自动通过笔记并且发放返现

	// Step1: check note status if equals 4

	// q := map[string]interface{}{
	// 	"filter": map[string]interface{}{
	// 		"where": map[string]interface{}{
	// 			"_id": map[string]interface{}{
	// 				"$eq": p.NoteID,
	// 			},
	// 		},
	// 	},
	// 	"select": map[string]interface{}{
	// 		"_id":    true,
	// 		"status": true,
	// 	},
	// }

	// type Note struct {
	// 	ID     string `json:"id"`
	// 	Status string `json:"status"`
	// }

	// // Call the cloudbase GetItem function.
	// res1, err := cloudbase.GetItem(ctx, "notes", q)
	// if err != nil {
	// 	log.Error("failed to get item from cloudbase", zap.Error(err))
	// 	return errors.New("failed to get item from cloudbase")
	// }

	// // The actual record is nested inside "data.record".
	// data, ok := res1["data"].(map[string]interface{})
	// if !ok {
	// 	log.Warn("note not found or response format is invalid", zap.Any("response", res1))
	// 	return errors.New("note not found or response format is invalid")
	// }

	// record, ok := data["record"]
	// if !ok || record == nil {
	// 	log.Info("record not found for id", zap.String("note_id", p.NoteID))
	// 	return errors.New("record not found for id")
	// }

	// // Marshal the record map into JSON bytes.
	// recordBytes, err := json.Marshal(record)
	// if err != nil {
	// 	log.Error("failed to marshal record", zap.Error(err))
	// 	return errors.New("failed to marshal record")
	// }

	// // Unmarshal the JSON bytes into our note struct.
	// var note Note
	// if err := json.Unmarshal(recordBytes, &note); err != nil {
	// 	log.Error("failed to unmarshal note", zap.Error(err))
	// 	return errors.New("failed to unmarshal note")
	// }

	// if note.Status != "4" {
	// 	log.Error("note.status != 4, no need to pass_the_audit")
	// 	return errors.New("note.status != 4, no need to pass_the_audit")
	// }

	// Step2: Call pass_the_audit cloud function

	// 创建新的随机数生成器
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	// 生成 1-5 秒的随机时间
	randomMilliseconds := r.Intn(4000) + 1000 // [0,3999] +1000 => [1000,4999]
	time.Sleep(time.Duration(randomMilliseconds) * time.Millisecond)
	fmt.Printf("Sleeping for %d seconds before call pass_the_audit function ...\n", randomMilliseconds/1000)

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/functions/pass_the_audit"
	method := "POST"

	// Define your template
	tmplString := `{
			"note_id": "{{.NoteID}}"
		}`
	// Data to inject into the template
	params := struct {
		NoteID string
	}{
		NoteID: p.NoteID,
	}

	// Create and execute the template
	tmpl := template.Must(template.New("payload").Parse(tmplString))

	var buf bytes.Buffer

	err := tmpl.Execute(&buf, params)
	if err != nil {
		log.Error("Template execution failed", zap.Error(err))
		return err
	}

	log.Debug("pass_the_audit payload", zap.String("payload", buf.String()))

	// Get the final payload
	payload := strings.NewReader(buf.String())

	// Send the request
	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Error("Failed to create http request", zap.Error(err))
		return err
	}

	rdb := redis.GetClient()

	cloudbaseAccessToken, err := rdb.Get(ctx, "cloudbase_access_token").Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			log.Warn("cloudbase_access_token not found in redis, trying to refresh it")

			if refreshErr := util.UpdateCloudbaseAccessToken(); refreshErr != nil {
				log.Error("Failed to refresh cloudbase_access_token", zap.Error(refreshErr))
				return refreshErr
			}

			cloudbaseAccessToken, err = rdb.Get(ctx, "cloudbase_access_token").Result()
			if err != nil {
				log.Error("Failed to get cloudbase_access_token from redis after refresh", zap.Error(err))
				return err
			}

			log.Info("Successfully refreshed and retrieved cloudbase_access_token")
		} else {
			log.Error("Failed to get cloudbase_access_token from redis", zap.Error(err))
			return err
		}
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

	res, err := client.Do(req)
	if err != nil {
		log.Error("Failed to execute http request", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("Failed to read response body", zap.Error(err))
		return err
	}

	log.Info("pass_the_audit response", zap.String("body", string(body)))

	if _, err := task.ResultWriter().Write(body); err != nil {
		log.Error("Failed to write task result", zap.Error(err))
		return fmt.Errorf("failed to write task result: %w", err)
	}

	return nil
}

type ListNoteResponse struct {
	Data struct {
		Records []struct {
			ID     string `json:"_id"`
			Status int    `json:"status"`
		} `json:"records"`
		Total interface{} `json:"total"`
	} `json:"data"`
	RequestID string `json:"requestId"`
}

func enqueueAutoSendNoteLinkTask(noteID string) error {
	log := logger.Get("tasks:note")
	task, err := NewAutoSendNoteLinkTask(noteID)
	if err != nil {
		log.Error("Failed to create new auto send note link task", zap.Error(err))
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	// Process between 1-900 seconds later
	delay := time.Duration(rand.Intn(900)+1) * time.Second

	info, err := client.Enqueue(
		task,
		asynq.ProcessIn(delay),
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute), // delay 5 minutes
		asynq.Queue("note"),
		asynq.Retention(24*time.Hour),
	)
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return err
	}

	log.Info(
		"Enqueued task",
		zap.String("id", info.ID),
		zap.String("queue", info.Queue),
		zap.String("type", task.Type()),
	)

	return nil
}

func enqueueAutoPassNoteTask(noteID string) error {
	log := logger.Get("tasks:note")
	task, err := NewAutoPassNoteTask(noteID)
	if err != nil {
		log.Error("Failed to create new auto pass note task", zap.Error(err))
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	delay := 1 * time.Hour

	info, err := client.Enqueue(
		task,
		asynq.ProcessIn(delay),
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("note"),
		asynq.Retention(30*24*time.Hour),
	)
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return err
	}

	log.Info(
		"Enqueued task",
		zap.String("id", info.ID),
		zap.String("queue", info.Queue),
		zap.String("type", task.Type()),
	)

	return nil
}

type UpdateSingleDataResponse struct {
	Data struct {
		Count int `json:"count"`
	} `json:"data"`
	RequestID string `json:"requestId"`
}
