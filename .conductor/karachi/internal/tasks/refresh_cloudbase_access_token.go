package tasks

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
	"tandian-server/internal/util"
)

const (
	TypeRefreshToken = "scheduler:refresh_cloudbase_access_token"
)

var ctx = context.Background()

func NewRefreshCloudbaseTokenTask() (*asynq.Task, error) {
	return asynq.NewTask(
		TypeRefreshToken,
		nil, asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("default"),
		asynq.Retention(5*time.Minute),
	), nil
}

func HandleRefreshCloudbaseTokenTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:cloudbase_token").With(zap.String("task_type", task.Type()))
	log.Info("HandleRefreshCloudbaseTokenTask")

	rdb := redis.GetClient()

	ping, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Error("redis ping failed", zap.Error(err))
	}

	log.Debug("redis ping", zap.String("ping", ping))

	var result map[string]interface{}

	token, err := rdb.Get(ctx, "cloudbase_access_token").Result()

	switch {
	case errors.Is(err, redis.Nil):
		log.Debug("CACHE MISS, cloudbase_access_token does not exist")

		if err := RefreshCloudbaseToken(); err != nil {
			result = map[string]interface{}{
				"status": "failed",
				"error":  err.Error(),
			}
		} else {
			result = map[string]interface{}{
				"status": "refreshed",
				"source": "api",
			}
		}
	case err != nil:
		log.Error("err", zap.Error(err))
		result = map[string]interface{}{
			"status": "failed",
			"error":  err.Error(),
		}
	default:
		log.Debug("CACHE HIT", zap.String("cloudbase_access_token", token))
		util.UpdateCloudbaseAccessToken()

		result = map[string]interface{}{
			"status": "cache_hit",
			"token":  token,
		}
	}

	resultJSON, err := json.Marshal(result)
	if err != nil {
		log.Error("Failed to marshal result", zap.Error(err))
		return err
	}

	if _, err := task.ResultWriter().Write(resultJSON); err != nil {
		log.Error("Failed to write task result", zap.Error(err))
		return err
	}

	return nil
}

func RefreshCloudbaseToken() error {
	log := logger.Get("tasks:cloudbase_token")
	rdb := redis.GetClient()

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/auth/v1/signin"
	method := "POST"

	payload := strings.NewReader(`{
	"username": "administrator",
	"password": "1Qaz2wsx./"
	}`)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Error("http.NewRequest failed", zap.Error(err))
		return err
	}

	req.Header.Add("X-Device-Id", "asynq-server")
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Error("client.Do failed", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("io.ReadAll failed", zap.Error(err))
		return err
	}
	// fmt.Println(string(body))

	var resp LoginResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		log.Error("json.Unmarshal failed", zap.Error(err))
		return err
	}

	err = rdb.SetNX(ctx, "cloudbase_access_token", resp.AccessToken, time.Duration(resp.ExpiresIn)*time.Second).Err()
	if err != nil {
		log.Error("rdb.SetNX failed", zap.Error(err))
		return err
	}

	err = rdb.Set(ctx, "cloudbase_refresh_token", resp.RefreshToken, 0).Err()
	if err != nil {
		log.Error("rdb.Set failed", zap.Error(err))
		return err
	}

	util.UpdateCloudbaseAccessToken()

	return nil
}

type LoginResponse struct {
	TokenType    string   `json:"token_type"`
	AccessToken  string   `json:"access_token"`
	RefreshToken string   `json:"refresh_token"`
	ExpiresIn    int      `json:"expires_in"`
	Sub          string   `json:"sub"`
	Groups       []string `json:"groups"`
}

// BGKPU87ATA

// func updateItem() {
// 	rdb := initRedisClient()
// 	accessToken, err := rdb.Get(ctx, "cloudbase_access_token").Result()
// 	if err != nil {
// 		fmt.Println(err)
// 		return
// 	}
// 	url := fmt.Sprintf("https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/%s/put", model)

// 	// create a Resty client
// 	client := resty.New()
// 	defer client.Close()

// 	res, err := client.R().
// 		SetBody(Article{
// 			Title:   "Resty",
// 			Content: "This is my article content, oh ya!",
// 			Author:  "Jeevanandam M",
// 			Tags:    []string{"article", "sample", "resty"},
// 		}). // default request content type is JSON
// 		SetAuthToken(accessToken).
// 		SetError(&Error{}). // or SetError(Error{}).
// 		Put(url)

// 	fmt.Println(err, res)
// 	fmt.Println(res.Error().(*Error))
// }

// type FilterByID struct {
// 	Filter struct {
// 		Where struct {
// 			ID struct {
// 				Eq string `json:"$eq"`
// 			}
// 		} `json:"where"`
// 	} `json:"filter"`
// }
