package tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	flagsmithClient "github.com/Flagsmith/flagsmith-go-client/v4"
	"github.com/dfang/go-prettyjson"
	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/flagsmith"
	"tandian-server/internal/logger"
	"tandian-server/internal/notify"
	"tandian-server/internal/util/cloudbase"
)

const (
	TypeAutoScanNotes = "scheduler:auto_scan_notes"
)

// NewAutoScanNotesTask creates a new task to auto scan and approve pending notes.
func NewAutoScanNotesTask() (*asynq.Task, error) {
	return asynq.NewTask(
		TypeAutoScanNotes,
		nil,
		asynq.MaxRetry(2),
		asynq.Timeout(10*time.Minute),
		asynq.Queue("note"),
		asynq.Retention(24*time.Hour),
	), nil
}

// HandleAutoScanNotesTask handles the auto scan notes task.
func HandleAutoScanNotesTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:note").With(zap.String("task_type", task.Type()))
	log.Info("Starting auto scan notes task")

	// Check if Flagsmith is enabled
	if !config.AppConfig.Flagsmith.Enabled || !flagsmith.IsInitialized() {
		log.Info("Flagsmith is disabled or not initialized, skipping auto scan")
		return nil
	}

	// Get environment flags from Flagsmith
	client := flagsmith.GetClient()
	flags, err := client.GetEnvironmentFlags(ctx)
	if err != nil {
		log.Error("Failed to get environment flags from Flagsmith", zap.Error(err))
		return err
	}

	// Check if smart audit mode is enabled
	smartAuditEnabled := false
	autoApprovalThreshold := 10 // Default value

	for _, flag := range flags.AllFlags() {
		switch flag.FeatureName {
		case "note_smart_audit_mode":
			smartAuditEnabled = flag.Enabled
		case "note_auto_approval_threshold":
			if val, ok := flag.Value.(float64); ok {
				autoApprovalThreshold = int(val)
			}
		}
	}

	// If smart audit mode is not enabled, skip the task
	if !smartAuditEnabled {
		log.Info("Smart audit mode is disabled, skipping auto scan")
		return nil
	}

	log.Info("Smart audit mode enabled",
		zap.Bool("smart_audit_enabled", smartAuditEnabled),
		zap.Int("auto_approval_threshold", autoApprovalThreshold))

	// Query all notes with status = 0 (pending audit)
	payload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"status": map[string]interface{}{
					"$eq": 0,
				},
			},
		},
		"pageSize":   100,
		"pageNumber": 1,
		"getCount":   true,
		"select": map[string]interface{}{
			"_id":       true,
			"_openid":   true,
			"createdAt": true,
			"updatedAt": true,
			"status":    true,
			"url":       true,
			"user_id":   true,
			"card_id":   true,
			"order_id":  true,
			"shop_id":   true,
			"feedback":  true,
			"rating":    true,
			"remark":    true,
		},
		"orderBy": []map[string]interface{}{
			{
				"createdAt": "asc",
			},
		},
	}

	result, err := cloudbase.GetItems(ctx, "notes", payload)
	if err != nil {
		log.Error("Failed to query pending notes", zap.Error(err))
		return err
	}

	notesArray, ok := result["records"].([]interface{})
	if !ok || len(notesArray) == 0 {
		log.Info("No pending notes found")
		return nil
	}

	log.Info("Found pending notes", zap.Int("count", len(notesArray)))

	approvedCount := 0
	var approvedNoteIDs []string

	// Process each pending note
	for _, noteItem := range notesArray {
		note, ok := noteItem.(map[string]interface{})
		if !ok {
			continue
		}

		// prettyjson.Print(note)

		noteID, ok := note["_id"].(string)
		if !ok {
			continue
		}

		user, ok := note["user_id"].(map[string]interface{})
		if !ok {
			log.Warn("Note missing user_id field", zap.String("user_id", noteID))
			continue
		}

		userID, ok := user["_id"].(string)
		if !ok {
			continue
		}
		prettyjson.Printf("user_id: %s\n", userID)

		envFlags, err := client.GetEnvironmentFlags(ctx)
		if err != nil {
			log.Error("Failed to get environment flags from Flagsmith",
				zap.String("user_id", userID),
				zap.String("note_id", noteID),
				zap.Error(err))
			continue
		}

		f1, err := envFlags.GetFlag("note_auto_approval_eligible")
		if err != nil {
			return err
		}
		prettyjson.Printf("env Flag: %s\n", f1)

		userFlags, err := client.GetIdentityFlags(ctx, userID, []*flagsmithClient.Trait{})
		if err != nil {
			log.Error("Failed to get identity flags from Flagsmith",
				zap.String("user_id", userID),
				zap.String("note_id", noteID),
				zap.Error(err))
			continue
		}

		uf1, _ := userFlags.GetFlag("note_auto_approval_eligible")
		prettyjson.Printf("user Flag note_auto_approval_eligible: %s\n", uf1)

		qualifiesForAutoApproval := false
		if uf1.Enabled {
			if eligibleValue, err := userFlags.GetFeatureValue("note_auto_approval_eligible"); err == nil {
				// The value could be a boolean or string depending on how it's configured in Flagsmith
				switch v := eligibleValue.(type) {
				case bool:
					qualifiesForAutoApproval = v
				case string:
					// Handle string values like "true", "false", "yes", "no"
					qualifiesForAutoApproval = v == "true" || v == "yes" || v == "1"
				case float64:
					// Handle numeric values where non-zero means true
					qualifiesForAutoApproval = v != 0
				default:
					// Default to false if we can't determine the value
					qualifiesForAutoApproval = false
				}
				log.Info("Auto-approval flag evaluation",
					zap.String("user_id", userID),
					zap.Any("flag_value", eligibleValue),
					zap.Bool("qualifies", qualifiesForAutoApproval))
			}
		}

		// for _, flag := range userFlags.AllFlags() {
		// 	if flag.FeatureName == "note_auto_approval_eligible" {
		// 		log.Info("Flag with HIGH trait value",
		// 			zap.String("feature_name", flag.FeatureName),
		// 			zap.Bool("enabled", flag.Enabled),
		// 			zap.Any("value", flag.Value),
		// 			zap.Bool("is_default", flag.IsDefault))
		// 		break
		// 	}
		// }

		// Log all flags to see what's being returned
		log.Info("All flags for user",
			zap.String("user_id", userID),
			zap.Int("flag_count", len(userFlags.AllFlags())))

		for _, flag := range userFlags.AllFlags() {
			log.Info("Flag details",
				zap.String("feature_name", flag.FeatureName),
				zap.Bool("enabled", flag.Enabled),
				zap.Any("value", flag.Value),
				zap.Bool("is_default", flag.IsDefault))
		}

		// prettyjson.Print(userFlags)

		// Check if user qualifies for auto-approval
		// This feature flag should be configured in Flagsmith with a segment rule:
		// notes_approved_count >= autoApprovalThreshold
		qualifiesForAutoApproval = false
		userApprovedCount := 0

		// prettyjson.Printf("userFlags: %s\n", userFlags.AllFlags())

		// Check for the auto-approval eligibility flag
		// Important: We check the VALUE, not just if it's enabled
		// The segment override should set the value to true for eligible users
		if eligibleValue, err := userFlags.GetFeatureValue("note_auto_approval_eligible"); err == nil {
			// The value could be a boolean or string depending on how it's configured in Flagsmith
			switch v := eligibleValue.(type) {
			case bool:
				qualifiesForAutoApproval = v
			case string:
				// Handle string values like "true", "false", "yes", "no"
				qualifiesForAutoApproval = v == "true" || v == "yes" || v == "1"
			case float64:
				// Handle numeric values where non-zero means true
				qualifiesForAutoApproval = v != 0
			default:
				// Default to false if we can't determine the value
				qualifiesForAutoApproval = false
			}
			log.Info("Auto-approval flag evaluation",
				zap.String("user_id", userID),
				zap.Any("flag_value", eligibleValue),
				zap.Bool("qualifies", qualifiesForAutoApproval))
		}

		// Get the user's approved count from a feature flag value if available
		if approvedCount, err := userFlags.GetFeatureValue("notes_approved_count"); err == nil {
			if val, ok := approvedCount.(float64); ok {
				userApprovedCount = int(val)
			}
		}

		log.Debug("User note stats",
			zap.String("user_id", userID),
			zap.String("note_id", noteID),
			zap.Bool("qualifies_for_auto_approval", qualifiesForAutoApproval),
			zap.Int("approved_count", userApprovedCount))

		// Auto-approve if user qualifies based on Flagsmith configuration
		if qualifiesForAutoApproval {
			// Update note status to 10 (auto-approved) and mark as auto_approved
			updatePayload := map[string]interface{}{
				"filter": map[string]interface{}{
					"where": map[string]interface{}{
						"_id": map[string]interface{}{
							"$eq": noteID,
						},
					},
				},
				"data": map[string]interface{}{
					"status":      10,
					"approved_by": "system_auto",
					"approved_at": time.Now().Unix() * 1000,
					"updatedAt":   time.Now().Unix() * 1000,
				},
			}

			prettyjson.Print(updatePayload)

			_, err = cloudbase.UpdateItem(ctx, "notes", updatePayload)
			if err != nil {
				log.Error("Failed to update note status",
					zap.String("note_id", noteID),
					zap.Error(err))
				continue
			}

			approvedCount++
			approvedNoteIDs = append(approvedNoteIDs, noteID)

			log.Info("Auto-approved note",
				zap.String("note_id", noteID),
				zap.String("user_id", userID),
				zap.Int("user_approved_count", userApprovedCount))

			// // Enqueue task to send note link to WeCom group (status 3 -> 4)
			// err = enqueueAutoSendNoteLinkTask(noteID)
			// if err != nil {
			// 	log.Error("Failed to enqueue send note link task",
			// 		zap.String("note_id", noteID),
			// 		zap.Error(err))
			// }
		}
	}

	// Send summary message to WeCom group if any notes were approved
	if approvedCount > 0 {
		// Use markdown format for better visual presentation
		message := fmt.Sprintf("## 🤖 自动审核完成\n\n"+
			"> **自动通过**: %d 条笔记\n"+
			"> **审核阈值**: %d 条已通过笔记\n\n"+
			"### 📝 笔记ID列表\n",
			approvedCount,
			autoApprovalThreshold)

		for i, noteID := range approvedNoteIDs {
			message += fmt.Sprintf("%d. `%s`\n", i+1, noteID)
		}

		message += fmt.Sprintf("\n---\n*时间: %s*", time.Now().Format("2006-01-02 15:04:05"))

		err = sendNoteDetailToWecomGroup(message)
		if err != nil {
			log.Error("Failed to send WeCom notification", zap.Error(err))
		}
	}

	// Write task result
	resultData := map[string]interface{}{
		"smart_audit_enabled": smartAuditEnabled,
		"total_processed":     len(notesArray),
		"auto_approved":       approvedCount,
		"approval_threshold":  autoApprovalThreshold,
		"approved_note_ids":   approvedNoteIDs,
		"completed_at":        time.Now().Format(time.RFC3339),
	}

	resultBytes, _ := json.Marshal(resultData)
	task.ResultWriter().Write(resultBytes)

	log.Info("Auto scan notes task completed",
		zap.Int("total_processed", len(notesArray)),
		zap.Int("auto_approved", approvedCount))

	return nil
}

// sendNoteDetailToWecomGroup sends a markdown message to WeCom group.
func sendNoteDetailToWecomGroup(message string) error {
	// Use webhook to send message to external WeCom group
	// You should configure this webhook URL in your config or environment
	webhookURL := "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY"

	// Check if webhook URL is configured in config
	// if config.AppConfig.Wecom.WebhookKey != "" {
	// 	webhookURL = fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s",
	// 		config.AppConfig.Wecom.WebhookKey)
	// }

	payload := notify.NewMarkdownPayload(message)
	return notify.SendToWecomGroup(webhookURL, payload)
}
