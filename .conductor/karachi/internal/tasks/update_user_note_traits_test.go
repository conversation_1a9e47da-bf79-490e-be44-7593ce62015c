package tasks

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
	"tandian-server/internal/logger"
)

func TestNewUpdateUserNoteTraitsTask(t *testing.T) {
	// Initialize logger for tests
	err := logger.Init("test", "info", nil)
	assert.NoError(t, err)
	
	userID := "test_user_123"
	
	task, err := NewUpdateUserNoteTraitsTask(userID)
	assert.NoError(t, err)
	assert.NotNil(t, task)
	assert.Equal(t, TypeUpdateUserNoteTraits, task.Type())
	
	// Verify payload
	var payload UpdateUserNoteTraitsPayload
	err = json.Unmarshal(task.Payload(), &payload)
	assert.NoError(t, err)
	assert.Equal(t, userID, payload.UserID)
}

func TestHandleUpdateUserNoteTraitsTask_InvalidPayload(t *testing.T) {
	// Initialize logger for tests
	err := logger.Init("test", "info", nil)
	assert.NoError(t, err)
	
	// Create task with invalid payload
	task := asynq.NewTask(TypeUpdateUserNoteTraits, []byte("invalid json"))
	
	err = HandleUpdateUserNoteTraitsTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "json.Unmarshal failed")
}

func TestEnqueueUpdateUserNoteTraitsTask(t *testing.T) {
	// This test would require a mock Redis setup
	// For now, we just verify the function exists and can be called
	t.Skip("Skipping integration test that requires Redis")
	
	userID := "test_user_456"
	err := EnqueueUpdateUserNoteTraitsTask(userID)
	_ = err // Would check error in real test with Redis
}