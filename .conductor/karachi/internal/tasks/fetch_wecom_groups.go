package tasks

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"text/template"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
	"tandian-server/internal/util/cloudbase"
)

const (
	TypeNotifyBoss            = "scheduler:notify_boss"
	TypeFetchWecomGroups      = "scheduler:fetch_wecom_groups"
	TypeFetchWecomGroupDetail = "scheduler:fetch_wecom_group_detail"
)

type FetchGroupDetailTaskPayload struct {
	ChatID string `json:"chat_id"`
}

func NewFetchWecomGroupsTask() (*asynq.Task, error) {
	return asynq.NewTask(
		TypeFetchWecomGroups,
		nil,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("default"),
		asynq.Retention(30*24*time.Hour),
	), nil
}

func NewFetchWecomGroupDetailTask(chatId string) (*asynq.Task, error) {
	log := logger.Get("tasks.wecom")

	body := map[string]string{
		"chat_id": chatId,
	}

	payload, err := json.Marshal(body)
	if err != nil {
		log.Error("Error marshaling bodypayload:", zap.Error(err))
		return nil, err
	}

	return asynq.NewTask(
		TypeFetchWecomGroupDetail,
		payload,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("default"),
		asynq.Retention(30*24*time.Hour),
	), nil
}

func HandleFetchWecomGroupsTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks.wecom").With(zap.String("task_type", task.Type()))

	rdb := redis.GetClient()

	accessToken, err := rdb.Get(context.Background(), "wecom_access_token").Result()
	if err != nil {
		log.Error("get wecom_access_token from redis error", zap.Error(err))
		return err
	}
	// accessToken := "y8f9x_Lfm6E19Fr1z-ftAWChrBD1elMgzR9UXtIwtZHmSCEqntYZJ7mDFZouhX5O6_7CwgXYkGr6hMdIdIq1fIt4iT1p1BIUtYLy7egBuoKml_D3_HP_hhI_IVRDUb4ZaTWU3wR4VfmOVdop6obp5U1Wk9vo1UmYBEsw75Ym5Tpm2dheQ0hh2zT4hCuHrXbyEvv5uZrr2sr-nwSw1Re5dw"
	url := fmt.Sprintf(
		"https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/list?access_token=%s&debug=1",
		accessToken,
	)
	method := "POST"

	payload := strings.NewReader(`{
		"status_filter": 0,
		"limit": 1000
	}`)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Error("new http request error", zap.Error(err))
		return err
	}

	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Error("do http request error", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("read all body error", zap.Error(err))
		return err
	}

	log.Info(string(body))

	resp := &WeComGroupChatListResponse{}
	if err := json.Unmarshal(body, resp); err != nil {
		return fmt.Errorf("error unmarshalling response: %s", err.Error())
	}

	if resp.Errcode != 0 {
		return fmt.Errorf("error fetching WeCom groups: %s", resp.Errmsg)
	}

	if resp.Errcode == 0 {
		for _, group := range resp.GroupChatList {
			enqueueFetchWecomGroupDetailTask(group.ChatID)
		}
	}

	return nil
}

func HandleFetchWecomGroupDetailTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks.wecom").With(zap.String("task_type", task.Type()))

	rdb := redis.GetClient()

	accessToken, err := rdb.Get(context.Background(), "wecom_access_token").Result()
	if err != nil {
		log.Error("get wecom_access_token from redis error", zap.Error(err))
		return err
	}

	var p FetchGroupDetailTaskPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	// accessToken := "y8f9x_Lfm6E19Fr1z-ftAWChrBD1elMgzR9UXtIwtZHmSCEqntYZJ7mDFZouhX5O6_7CwgXYkGr6hMdIdIq1fIt4iT1p1BIUtYLy7egBuoKml_D3_HP_hhI_IVRDUb4ZaTWU3wR4VfmOVdop6obp5U1Wk9vo1UmYBEsw75Ym5Tpm2dheQ0hh2zT4hCuHrXbyEvv5uZrr2sr-nwSw1Re5dw"
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get?access_token=%s", accessToken)
	method := "POST"

	// restyClient := resty.New()
	// defer restyClient.Close()
	// res, err := restyClient.R().
	// 	SetBody(map[string]interface{}{
	// 		"chat_id":   p.ChatID,
	// 		"need_name": 1,
	// 	}).                                         // default request content type is JSON
	// 	SetResult(&WeComGroupChatDetailResponse{}). // or SetResult(LoginResponse{}).
	// 	Post(url)

	// if err != nil {
	// 	log.Error("Error making request:", zap.Error(err))
	// 	return err
	// }

	// log.Info(res.Result().(*WeComGroupChatDetailResponse))

	// detail := res.Result().(*WeComGroupChatDetailResponse)
	// if detail.Errcode != 0 {
	// 	return fmt.Errorf("error fetching WeCom group detail: %s", detail.Errmsg)
	// }

	// Define your template)

	tmplString := `{
			"chat_id":"{{.ChatID}}",
			"need_name" : 1
		}`
	// Data to inject into the template
	data := struct {
		ChatID string
	}{
		ChatID: p.ChatID,
	}

	// Create and execute the template
	tmpl := template.Must(template.New("payload").Parse(tmplString))

	var buf bytes.Buffer

	err = tmpl.Execute(&buf, data)
	if err != nil {
		log.Error("Template error:", zap.Error(err))
		return err
	}

	payload := strings.NewReader(buf.String())

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Error("new http request error", zap.Error(err))
		return err
	}

	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Error("do http request error", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("read all body error", zap.Error(err))
		return err
	}

	resp := &WeComGroupChatDetailResponse{}
	if err := json.Unmarshal(body, resp); err != nil {
		return fmt.Errorf("error unmarshalling response: %s", err.Error())
	}

	if resp.Errcode != 0 {
		return fmt.Errorf("error fetching WeCom group detail %s", resp.Errmsg)
	}

	if resp.Errcode == 0 {
		// Save Group Chat Detail
		tmplString := `{
			"filter": {
				"where": {
					"chat_id": {
						"$eq": "{{.ChatID}}"
					}
				}
			},
			"create": {
				"name": "{{.Name}}",
				"chat_id": "{{.ChatID}}",
				"create_time": {{.CreateTime}}
			},
			"update": {
				"name": "{{.Name}}",
				"chat_id": "{{.ChatID}}",
				"create_time": {{.CreateTime}}
			}
		}`
		// Data to inject into the template
		data := struct {
			Name       string
			ChatID     string
			CreateTime int64
		}{
			Name:       resp.GroupChat.Name,
			ChatID:     resp.GroupChat.ChatID,
			CreateTime: resp.GroupChat.CreateTime,
		}

		// Create and execute the template
		tmpl := template.Must(template.New("payload").Parse(tmplString))

		var buf bytes.Buffer

		err := tmpl.Execute(&buf, data)
		if err != nil {
			log.Error("Template error:", zap.Error(err))
			return err
		}

		// _, err = util.AddOrUpdateItem(ctx, "wecom_groups", strings.NewReader(buf.String()))
		// if err != nil {
		// 	log.Error("Error saving group chat detail:", zap.Error(err))
		// 	return err
		// }

		q := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"chat_id": map[string]interface{}{
						"$eq": p.ChatID,
					},
				},
			},
			"select": map[string]interface{}{
				"_id":   true,
				"owner": true,
			},
		}

		record, err := cloudbase.GetItem(ctx, "wecom_groups", q)
		if err != nil {
			log.Error("Error retrieving item:", zap.Error(err))
			return err
		}

		log.Info("Retrieved item:", zap.Any("item", record))

		// Check if record exists
		if len(record) == 0 {
			// create new item
			_, err = cloudbase.CreateItem(ctx, "wecom_groups", map[string]interface{}{
				"data": map[string]interface{}{
					"name":        resp.GroupChat.Name,
					"chat_id":     resp.GroupChat.ChatID,
					"create_time": resp.GroupChat.CreateTime,
					// "group_owner": resp.GroupChat.Owner,
					// "member_list": resp.GroupChat.MemberList,
				},
			})
			if err != nil {
				log.Error("Error creating item:", zap.Error(err))
				return err
			}

			log.Info(
				"Created new WeCom group chat detail:",
				zap.String("name", resp.GroupChat.Name),
				zap.String("chat_id", resp.GroupChat.ChatID),
			)
		}
	}

	return nil
}

func enqueueFetchWecomGroupDetailTask(chatID string) {
	log := logger.Get("tasks.wecom").With(zap.String("chat_id", chatID))

	task, err := NewFetchWecomGroupDetailTask(chatID)
	if err != nil {
		log.Error("Error creating fetch WeCom group detail task:", zap.Error(err))
		return
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	info, err := client.Enqueue(
		task,
		// asynq.ProcessIn(delay),
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("default"),
		asynq.Retention(30*24*time.Hour),
	)
	if err != nil {
		log.Error("enqueue task error", zap.Error(err))
		return
	}

	log.Info("enqueued task", zap.String("id", info.ID), zap.String("queue", info.Queue))
}

type WeComGroupChatListResponse struct {
	Errcode       int    `json:"errcode"`
	Errmsg        string `json:"errmsg"`
	GroupChatList []struct {
		ChatID string `json:"chat_id"`
		Status int    `json:"status"`
	} `json:"group_chat_list"`
	NextCursor string `json:"next_cursor"`
}

type WeComGroupChatDetailResponse struct {
	Errcode   int    `json:"errcode"`
	Errmsg    string `json:"errmsg"`
	GroupChat struct {
		ChatID        string `json:"chat_id"`
		Name          string `json:"name"`
		CreateTime    int64  `json:"create_time"`
		MemberList    []any  `json:"member_list"`
		AdminList     []any  `json:"admin_list"`
		Owner         string `json:"owner"`
		MemberVersion string `json:"member_version"`
	} `json:"group_chat"`
}
