package tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/logger"
	"tandian-server/internal/util/cloudbase"
)

const (
	TypeUpdateHelpCampaignStats = "scheduler:update_help_campaign_stats"
)

type HelpCampaignStatsPayload struct {
	Date string `json:"date"`
}

func NewUpdateHelpCampaignStatsTask() (*asynq.Task, error) {
	log := logger.Get("tasks:help_campaign")

	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Format("2006-01-02")

	payload, err := json.Marshal(HelpCampaignStatsPayload{Date: todayStart})
	if err != nil {
		log.Error("Failed to marshal payload", zap.Error(err))
		return nil, err
	}

	log.Debug("Creating new update help campaign stats task", zap.String("date", todayStart))

	return asynq.NewTask(
		TypeUpdateHelpCampaignStats,
		payload,
		asynq.MaxRetry(3),
		asynq.Timeout(10*time.Minute),
		asynq.Queue("daily"),
		asynq.Retention(30*24*time.Hour),
	), nil
}

func HandleUpdateHelpCampaignStatsTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:help_campaign").With(zap.String("task_type", task.Type()))
	log.Info("Handling update help campaign stats task")

	var p HelpCampaignStatsPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		log.Error("Failed to unmarshal payload", zap.Error(err))
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	// Calculate stats for the given date
	stats, err := calculateDailyStats(ctx, p.Date)
	if err != nil {
		log.Error("Failed to calculate daily stats", zap.Error(err), zap.String("date", p.Date))
		return err
	}

	// Save or update stats in database
	err = saveOrUpdateStats(ctx, stats)
	if err != nil {
		log.Error("Failed to save stats", zap.Error(err))
		return err
	}

	log.Info("Successfully updated help campaign stats",
		zap.String("date", p.Date),
		zap.Int("initiated_count", stats.InitiatedCount),
		zap.Int("helped_count", stats.HelpedCount),
		zap.Int("campaign_completed_count", stats.CampaignCompletedCount))

	// Write task result
	resultData := map[string]any{
		"date":                     p.Date,
		"initiated_count":          stats.InitiatedCount,
		"helped_count":             stats.HelpedCount,
		"campaign_completed_count": stats.CampaignCompletedCount,
		"status":                   "success",
		"updated_at":               time.Now().Format(time.RFC3339),
	}

	resultBytes, err := json.Marshal(resultData)
	if err != nil {
		log.Error("Failed to marshal task result", zap.Error(err))
		// Don't return error since the main task succeeded
	}

	// Asynq will store this result with the task
	_, err = task.ResultWriter().Write(resultBytes)
	if err != nil {
		log.Error("Failed to write task result", zap.Error(err))
		// Don't return error since the main task succeeded
	}

	return nil
}

type DailyStats struct {
	Date                   string `json:"date"`
	InitiatedCount         int    `json:"initiated_count"`
	HelpedCount            int    `json:"helped_count"`
	CampaignCompletedCount int    `json:"campaign_completed_count"`
}

func calculateDailyStats(ctx context.Context, date string) (*DailyStats, error) {
	log := logger.Get("tasks:help_campaign")

	// Parse date to get start and end timestamps for the day
	t, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	startOfDay := t.Unix() * 1000 // Convert to milliseconds
	endOfDay := t.Add(24*time.Hour).Unix() * 1000

	stats := &DailyStats{
		Date: date,
	}

	// 1. Count initiated campaigns (all campaigns created on this date)
	initiatedFilter := map[string]any{
		"created_at": map[string]any{
			"$gte": startOfDay,
			"$lt":  endOfDay,
		},
	}

	initiatedCount, err := cloudbase.Count(ctx, "help_campaigns", initiatedFilter)
	if err != nil {
		log.Error("Failed to count initiated campaigns", zap.Error(err))
		return nil, err
	}
	stats.InitiatedCount = int(initiatedCount)

	// 2. Count completed campaigns
	completedFilter := map[string]any{
		"created_at": map[string]any{
			"$gte": startOfDay,
			"$lt":  endOfDay,
		},
		"status": "completed",
	}

	completedCount, err := cloudbase.Count(ctx, "help_campaigns", completedFilter)
	if err != nil {
		log.Error("Failed to count completed campaigns", zap.Error(err))
		return nil, err
	}
	stats.CampaignCompletedCount = int(completedCount)

	// 3. Count help assists
	assistsFilter := map[string]any{
		"created_at": map[string]any{
			"$gte": startOfDay,
			"$lt":  endOfDay,
		},
	}

	helpedCount, err := cloudbase.Count(ctx, "help_records", assistsFilter)
	if err != nil {
		log.Error("Failed to count help assists", zap.Error(err))
		return nil, err
	}
	stats.HelpedCount = int(helpedCount)

	return stats, nil
}

func saveOrUpdateStats(ctx context.Context, stats *DailyStats) error {
	log := logger.Get("tasks:help_campaign")

	// Parse date to get timestamp for the date
	t, err := time.Parse("2006-01-02", stats.Date)
	if err != nil {
		log.Error("Failed to parse date", zap.Error(err))
		return err
	}
	dateTimestamp := t.Unix() * 1000 // Convert to milliseconds

	// Check if stats for this date already exist
	filter := map[string]any{
		"date": dateTimestamp,
	}

	existingStats, err := cloudbase.FindOne(ctx, "help_campaign_stats", filter)
	if err != nil {
		log.Error("Failed to check existing stats", zap.Error(err))
		return err
	}

	currentTime := time.Now().Unix() * 1000

	if len(existingStats) > 0 {
		// Update existing record
		statsID, ok := existingStats["_id"].(string)
		if !ok {
			return fmt.Errorf("failed to get stats ID from existing record")
		}

		updateData := map[string]any{
			"initiated_count":          stats.InitiatedCount,
			"helped_count":             stats.HelpedCount,
			"campaign_completed_count": stats.CampaignCompletedCount,
			"updatedAt":                currentTime,
		}

		result, err := cloudbase.UpdateById(ctx, "help_campaign_stats", statsID, updateData)
		if err != nil {
			log.Error("Failed to update stats", zap.Error(err))
			return err
		}

		log.Info("Updated help campaign stats",
			zap.String("id", statsID),
			zap.Any("result", result))

		return nil
	}

	// Create new record
	createData := map[string]any{
		"date":                     dateTimestamp, // Store as timestamp
		"initiated_count":          stats.InitiatedCount,
		"helped_count":             stats.HelpedCount,
		"campaign_completed_count": stats.CampaignCompletedCount,
		"createdAt":                currentTime,
		"updatedAt":                currentTime,
	}

	result, err := cloudbase.Create(ctx, "help_campaign_stats", createData)
	if err != nil {
		log.Error("Failed to create stats", zap.Error(err))
		return err
	}

	log.Info("Created help campaign stats", zap.Any("result", result))

	return nil
}
