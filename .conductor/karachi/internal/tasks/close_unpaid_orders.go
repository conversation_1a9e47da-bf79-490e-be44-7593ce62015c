package tasks

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"text/template"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
	"tandian-server/internal/pay"
	"tandian-server/internal/redis"
	"tandian-server/internal/util/cloudbase"
)

const (
	TypeSchedulerQueryUnpaidOrders = "scheduler:query_unpaid_orders"
	TypeSchedulerCloseUnpaidOrder  = "scheduler:close_unpaid_order"
)

// HandleQueryUnpaidOrdersTask finds and closes orders that have been unpaid for more than 5 minutes.
func HandleQueryUnpaidOrdersTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:query_unpaid_orders").With(zap.String("task_type", task.Type()))
	log.Info("Starting query unpaid orders task")

	// Calculate the cutoff time (5 minutes ago)
	cutoffTime := time.Now().Add(-5*time.Minute).Unix() * 1000 // CloudBase uses milliseconds

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/orders/list"
	method := "POST"

	// Define template for query
	tmplString := `{
		"filter": {
			"where": {
				"status": {
					"$eq": "0"
				},
				"createdAt": {
					"$lt": {{.CutoffTime}}
				}
			}
		},
		"select": {
			"_id": true,
			"order_no": true,
			"createdAt": true
		},
		"pageSize": {{.PageSize}},
		"pageNumber": {{.PageNumber}},
		"getCount": true
	}`

	pageSize := 100
	currentPage := 1
	totalCount := 0
	processedOrders := 0
	closedCount := 0

	for {
		// Data to inject into the template
		data := struct {
			CutoffTime int64
			PageSize   int
			PageNumber int
		}{
			CutoffTime: cutoffTime,
			PageSize:   pageSize,
			PageNumber: currentPage,
		}

		// Create and execute the template
		tmpl := template.Must(template.New("payload").Parse(tmplString))

		var buf bytes.Buffer

		err := tmpl.Execute(&buf, data)
		if err != nil {
			log.Error("Template error", zap.Error(err))
			return err
		}

		httpClient := &http.Client{}

		req, err := http.NewRequest(method, url, strings.NewReader(buf.String()))
		if err != nil {
			log.Error("Request creation error", zap.Error(err))
			return err
		}

		rdb := redis.GetClient()

		cloudbaseAccessToken, err := rdb.Get(ctx, "cloudbase_access_token").Result()
		if err != nil {
			log.Error("Failed to get cloudbase_access_token", zap.Error(err))
			return err
		}

		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
		req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

		res, err := httpClient.Do(req)
		if err != nil {
			log.Error("Request execution error", zap.Error(err))
			return err
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Error("Response reading error", zap.Error(err))
			return err
		}

		var resp OrderListResponse

		err = json.Unmarshal(body, &resp)
		if err != nil {
			log.Error("JSON unmarshal error", zap.Error(err))
			return err
		}

		// If this is the first page, get total count
		if currentPage == 1 {
			if total, ok := resp.Data.Total.(float64); ok {
				totalCount = int(total)
				log.Info("Total unpaid orders to process", zap.Int("total", totalCount))
			} else {
				return nil
			}
		}

		// Process each unpaid order
		for _, record := range resp.Data.Records {
			processedOrders++
			orderNo := record.OrderNo
			orderID := record.ID
			// orderType := record.OrderType
			// createdAt := record.CreatedAt

			// // Get promotion info
			// promotionInfo := extractPromotionInfo(record)

			// log.Info("Processing unpaid order",
			// 	zap.String("order_id", orderID),
			// 	zap.String("order_no", orderNo),
			// 	zap.String("order_type", orderType),
			// 	zap.Float64("created_at", createdAt),
			// 	zap.Int64("cutoff_time", cutoffTime),
			// )

			// // Prepare close order request
			// closeReq := pay.CloseOrderRequest{
			// 	OutTradeNo:    orderNo,
			// 	PromotionID:   promotionInfo.promotionID,
			// 	PromotionType: orderType,
			// 	IsChain:       promotionInfo.isChain,
			// 	ProductID:     promotionInfo.productID,
			// 	ShopID:        promotionInfo.shopID,
			// }

			// Enqueue close order task instead of closing directly
			err := enqueueCloseUnpaidOrderTask(orderID, orderNo)
			if err != nil {
				log.Error("Failed to enqueue close order task",
					zap.String("order_no", orderNo),
					zap.Error(err),
				)
				// Continue processing other orders even if one fails
				continue
			}

			log.Info("Successfully enqueued close order task",
				zap.String("order_no", orderNo),
				zap.String("order_id", orderID),
			)

			closedCount++
		}

		// Check if there are more pages
		if currentPage*pageSize >= totalCount {
			break
		}

		currentPage++
	}

	log.Info("Completed query unpaid orders task",
		zap.Int("total_processed", processedOrders),
		zap.Int("enqueued_count", closedCount),
	)

	return nil
}

// CloseUnpaidOrderPayload represents the payload for closing a single unpaid order.
type CloseUnpaidOrderPayload struct {
	OrderID string `json:"order_id"`
	OrderNo string `json:"order_no"`
}

// HandleCloseUnpaidOrderTask handles closing a single unpaid order.
func HandleCloseUnpaidOrderTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:close_unpaid_order").With(zap.String("task_type", task.Type()))
	log.Info("Starting close unpaid order task")

	var p CloseUnpaidOrderPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		log.Error("json.Unmarshal failed", zap.Error(err))
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	log.Info("Processing order close",
		zap.String("order_id", p.OrderID),
		zap.String("order_no", p.OrderNo),
	)

	// Query order details from database to get all necessary information
	orderQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": p.OrderID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":          true,
			"order_no":     true,
			"order_type":   true,
			"promotion_id": true,
			"p_id":         true,
			"shop_id":      true,
			"status":       true,
			"createdAt":    true,
		},
	}

	// Get order details
	orderData, err := cloudbase.GetItem(ctx, "orders", orderQuery)
	if err != nil {
		log.Error("Failed to get order details", zap.String("order_id", p.OrderID), zap.Error(err))
		return fmt.Errorf("failed to get order details: %w", err)
	}

	// Check if orderData is empty (order not found)
	if len(orderData) == 0 {
		log.Error("Order not found", zap.String("order_id", p.OrderID))
		return fmt.Errorf("order not found: %s", p.OrderID)
	}

	// Check if order still needs to be closed (status should be "0")
	if status, ok := orderData["status"].(string); ok && status != "0" {
		log.Info("Order already processed, skipping",
			zap.String("order_id", p.OrderID),
			zap.String("status", status),
		)

		return nil
	}

	// Extract order type
	orderType := ""
	if ot, ok := orderData["order_type"].(string); ok {
		orderType = ot
	}

	// Extract promotion ID
	promotionID := ""

	if promotions, ok := orderData["promotion_id"].([]interface{}); ok && len(promotions) > 0 {
		if promotion, ok := promotions[0].(map[string]interface{}); ok {
			if id, ok := promotion["_id"].(string); ok {
				promotionID = id
			}
		}
	} else {
		promotionID = orderData["p_id"].(string)
	}

	// Extract createdAt timestamp
	var createdAt int64
	if createdAtVal, ok := orderData["createdAt"].(float64); ok {
		createdAt = int64(createdAtVal)
	} else if createdAtVal, ok := orderData["createdAt"].(int64); ok {
		createdAt = createdAtVal
	}

	// Create WeChat Pay client
	client := pay.NewWechatPayClient()

	// Prepare close order request
	closeReq := pay.CloseOrderRequest{
		OutTradeNo:    p.OrderNo,
		PromotionID:   promotionID,
		PromotionType: orderType,
		// IsChain:       isChain,
		// ProductID:     productID,
		// ShopID:        shopID,
		CreatedAt: createdAt,
	}

	// Close the order
	err = client.CloseOrder(ctx, closeReq)
	if err != nil {
		log.Error("Failed to close order",
			zap.String("order_no", p.OrderNo),
			zap.Error(err),
		)

		return err
	}

	log.Info("Successfully closed unpaid order",
		zap.String("order_id", p.OrderID),
		zap.String("order_no", p.OrderNo),
	)

	return nil
}

// OrderListResponse represents the response from CloudBase orders list API.
type OrderListResponse struct {
	Data struct {
		Records []OrderRecord `json:"records"`
		Total   interface{}   `json:"total"`
	} `json:"data"`
	RequestID string `json:"requestId"`
}

// OrderRecord represents a single order record.
type OrderRecord struct {
	ID        string  `json:"_id"`
	OrderNo   string  `json:"order_no"`
	CreatedAt float64 `json:"createdAt"`
}

// NewQueryUnpaidOrdersTask creates a new task for querying unpaid orders.
func NewQueryUnpaidOrdersTask() (*asynq.Task, error) {
	return asynq.NewTask(
		TypeSchedulerQueryUnpaidOrders,
		nil,
		asynq.MaxRetry(3),
		asynq.Timeout(10*time.Minute),
		asynq.Queue("order"),
	), nil
}

// NewCloseUnpaidOrderTask creates a new task for closing a single unpaid order.
func NewCloseUnpaidOrderTask(orderID, orderNo string) (*asynq.Task, error) {
	payload, err := json.Marshal(CloseUnpaidOrderPayload{
		OrderID: orderID,
		OrderNo: orderNo,
	})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(TypeSchedulerCloseUnpaidOrder,
		payload,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("order"),
	), nil
}

// enqueueCloseUnpaidOrderTask enqueues a task to close a single unpaid order.
func enqueueCloseUnpaidOrderTask(orderID, orderNo string) error {
	log := logger.Get("tasks:close_unpaid_order").With(zap.String("order_id", orderID), zap.String("order_no", orderNo))
	task, err := NewCloseUnpaidOrderTask(orderID, orderNo)
	if err != nil {
		log.Error("NewCloseUnpaidOrderTask failed", zap.Error(err))
		return err
	}

	client := asynq.NewClient(asynq.RedisClientOpt{
		Addr:     config.AppConfig.Asynq.RedisAddr,
		Password: config.AppConfig.Asynq.RedisPassword,
		DB:       config.AppConfig.Asynq.RedisDB,
	})
	defer client.Close()

	// Process with some delay to avoid overwhelming the system
	delay := time.Duration(rand.Intn(10)+1) * time.Second

	info, err := client.Enqueue(
		task,
		asynq.ProcessIn(delay),
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("order"),
		asynq.Retention(24*time.Hour),
	)
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return err
	}

	log.Info(
		"enqueued close order task",
		zap.String("id", info.ID),
		zap.String("queue", info.Queue),
		zap.String("type", task.Type()),
	)

	return nil
}
