package tasks

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"text/template"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
)

const (
	TypeEnqueueQueryTransferBill = "scheduler:enqueue_query_transfer_bill"
	TypeQueryTransferBill        = "scheduler:query_transfer_bill"
)

type QueryTransferBillPayload struct {
	ID        string `json:"id"`
	OutBillNo string `json:"out_bill_no"`
}

func NewQueryTransferBillTask(id, outBillNo string) (*asynq.Task, error) {
	payload, err := json.Marshal(
		QueryTransferBillPayload{
			ID:        id,
			OutBillNo: outBillNo,
		})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(
		TypeQueryTransferBill,
		payload,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("critical"),
		asynq.Retention(30*24*time.Hour),
	), nil
}

// call query_transfer_bill cloud function.
func HandleQueryTransferBillTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:transfer_bill").With(zap.String("task_type", task.Type()))

	var p QueryTransferBillPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	log.Info("HandleQueryTransferBillTask", zap.String("OutBillNo", p.OutBillNo))

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/functions/query_transfer_bill"
	method := "POST"

	log.Debug("request", zap.String("url", url), zap.String("method", method))

	// payload := strings.NewReader(`{
	// 	"out_bill_no": "{{.p.OutBillNo}}"
	// }`)

	// Define your template
	tmplString := `{
		"out_bill_no": "{{.OutBillNo}}"
	}`
	// Data to inject into the template
	data := struct {
		OutBillNo string
	}{
		OutBillNo: p.OutBillNo,
	}

	// Create and execute the template
	tmpl := template.Must(template.New("payload").Parse(tmplString))

	var buf bytes.Buffer

	err := tmpl.Execute(&buf, data)
	if err != nil {
		log.Error("tmpl.Execute error", zap.Error(err))
		return err
	}

	// Get the final payload
	payload := strings.NewReader(buf.String())

	// Send the request
	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Error("http.NewRequest error", zap.Error(err))
		return err
	}

	rdb := redis.GetClient()

	cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
	if err != nil {
		log.Error("get cloudbase_access_token from redis error", zap.Error(err))
		return err
	}

	log.Debug("get cloudbase access_token from redis:", zap.String("cloudbaseAccessToken", cloudbaseAccessToken))

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

	res, err := client.Do(req)
	if err != nil {
		log.Error("client.Do error", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("io.ReadAll error", zap.Error(err))
		return err
	}

	log.Debug("response body", zap.String("body", string(body)))

	return nil
}

// 自动enqueue 对于pending的withdrawls 需要定时去查该笔提现的transfer_bill_no
// 处理提现成功却不点完成的漏洞.
func AutoEnqueueQueryPendingWithdrawalsTask() (*asynq.Task, error) {
	return asynq.NewTask(
		TypeEnqueueQueryTransferBill,
		nil,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("critical"),
		asynq.Retention(30*24*time.Hour),
	), nil
}

func HandleAutoEnqueueQueryPendingWithdrawalsTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:transfer_bill").With(zap.String("task_type", task.Type()))
	log.Info("HandleAutoEnqueueQueryPendingWithdrawalsTask")

	rdb := redis.GetClient()

	cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
	if err != nil {
		log.Error("Failed to get cloudbase_access_token from redis", zap.Error(err))
		return err
	}

	log.Debug("get cloudbase access_token from redis:", zap.String("cloudbaseAccessToken", cloudbaseAccessToken))

	url := "https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/withdrawals/list"
	method := "POST"

	// select pending withdrawals
	tmplString := `{
		"filter": {
			"where": {
				"status": {
					"$eq": "pending"
				}
			}
		},
		"select": {
			"_id": true,
			"out_bill_no": true,
			"status": true
		},
		"pageSize": {{.PageSize}},
		"pageNumber": {{.PageNumber}},
		"getCount": true,
		"orderBy": [
			{
				"createdAt": "asc"
			}
		]
	}`

	pageSize := 200
	currentPage := 1
	totalItems := 0

	for {
		// Data to inject into the template
		data := struct {
			PageSize   int
			PageNumber int
		}{
			PageSize:   pageSize,
			PageNumber: currentPage,
		}

		// Create and execute the template
		tmpl := template.Must(template.New("payload").Parse(tmplString))

		var buf bytes.Buffer

		err := tmpl.Execute(&buf, data)
		if err != nil {
			log.Error("Template error", zap.Error(err))
			return err
		}

		client := &http.Client{}

		req, err := http.NewRequest(method, url, strings.NewReader(buf.String()))
		if err != nil {
			log.Error("Request creation error", zap.Error(err))
			return err
		}

		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
		req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))

		res, err := client.Do(req)
		if err != nil {
			log.Error("Request execution error", zap.Error(err))
			return err
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Error("Response reading error", zap.Error(err))
			return err
		}

		var resp ListItemsResponse

		err = json.Unmarshal(body, &resp)
		if err != nil {
			log.Error("JSON unmarshal error", zap.Error(err))
			return err
		}

		log.Debug("resp", zap.Any("resp", resp))

		// 如果是第一页，获取总数
		if currentPage == 1 {
			if total, ok := resp.Data.Total.(float64); ok {
				log.Info("totalItems", zap.Float64("total", total))
				totalItems = int(total)
				// totalItems = 5
			} else {
				log.Error("Failed to get total items count")
				return nil
			}
		}

		// 处理当前页的数据
		for index, item := range resp.Data.Records {
			log.Debug(
				"Processing item",
				zap.String("ID", item.ID),
				zap.Int("index", (pageSize*(currentPage-1))+index+1),
			)

			err := enqueueQueryTransferBillTask(item.ID, item.OutBillNo)
			if err != nil {
				log.Error("enqueueQueryTransferBillTask error", zap.Error(err))
			}
		}

		log.Debug("Finished processing all items", zap.Int("totalItems", totalItems))

		// 检查是否还有下一页
		if currentPage*pageSize >= totalItems {
			break
		}

		currentPage++
	}

	return nil
}

type ListItemsResponse struct {
	Data struct {
		Records []struct {
			OutBillNo string `json:"out_bill_no"`
			ID        string `json:"_id"`
		} `json:"records"`
		Total interface{} `json:"total"`
	} `json:"data"`
	RequestID string `json:"requestId"`
}

func enqueueQueryTransferBillTask(id, outBillNo string) error {
	log := logger.Get("tasks:transfer_bill").With(zap.String("OutBillNo", outBillNo))

	task, err := NewQueryTransferBillTask(id, outBillNo)
	if err != nil {
		log.Error("NewQueryTransferBillTask error", zap.String("OutBillNo", outBillNo))
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	// Process between 1-10 seconds later
	delay := time.Duration(rand.Intn(10)+1) * time.Second

	info, err := client.Enqueue(
		task,
		asynq.ProcessIn(delay),
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("critical"),
		asynq.Retention(30*24*time.Hour),
	)
	if err != nil {
		log.Error("Enqueue error", zap.String("OutBillNo", outBillNo), zap.Error(err))
		return err
	}

	log.Info(
		"Enqueued task",
		zap.String("id", info.ID),
		zap.String("queue", info.Queue),
		zap.String("type", task.Type()),
	)

	return nil
}
