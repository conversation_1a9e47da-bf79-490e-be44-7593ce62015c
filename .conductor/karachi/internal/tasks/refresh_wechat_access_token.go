package tasks

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
	"tandian-server/internal/util"
)

const (
	TypeRefreshWechatAccessToken = "scheduler:refresh_wechat_access_token"
)

func NewRefreshWechatAccessTokenTask() (*asynq.Task, error) {
	return asynq.NewTask(
		TypeRefreshWechatAccessToken,
		nil,
		asynq.MaxRetry(2),
		asynq.Timeout(5*time.Minute),
		asynq.Queue("default"),
		asynq.Retention(5*time.Minute),
	), nil
}

func HandleRefreshWechatAccessTokenTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:wechat_token").With(zap.String("task_type", task.Type()))
	rdb := redis.GetClient()
	log.Debug("ping redis", zap.String("ping", rdb.Ping(ctx).String()))

	var result map[string]interface{}

	token, err := rdb.Get(ctx, "wechat_access_token").Result()
	switch {
	case errors.Is(err, redis.Nil):
		log.Debug("CACHE MISS, wechat_access_token does not exist")

		if err := RefreshWechatAccessToken(); err != nil {
			result = map[string]interface{}{
				"status": "failed",
				"error":  err.Error(),
			}
		} else {
			result = map[string]interface{}{
				"status": "refreshed",
				"source": "api",
			}
		}
	case err != nil:
		log.Error("failed to get wechat_access_token", zap.Error(err))
		result = map[string]interface{}{
			"status": "failed",
			"error":  err.Error(),
		}
	default:
		log.Debug("CACHE HIT")
		util.UpdateWechatAccessToken()

		result = map[string]interface{}{
			"status": "cache_hit",
			"token":  token,
		}
	}

	resultJSON, err := json.Marshal(result)
	if err != nil {
		log.Error("Failed to marshal result", zap.Error(err))
		return err
	}

	if _, err := task.ResultWriter().Write(resultJSON); err != nil {
		log.Error("Failed to write task result", zap.Error(err))
		return err
	}

	return nil
}

func RefreshWechatAccessToken() error {
	log := logger.Get("tasks:wechat_token")
	rdb := redis.GetClient()

	url := "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wxf77ccd1007594f39&secret=4410df97e5c0da0524f99748b0300302"
	method := "GET"

	client := &http.Client{}
	req, err := http.NewRequest(method, url, http.NoBody)
	if err != nil {
		log.Error("failed to create http request", zap.Error(err))
		return err
	}

	res, err := client.Do(req)
	if err != nil {
		log.Error("failed to send http request", zap.Error(err))
		return err
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("failed to read response body", zap.Error(err))
		return err
	}

	log.Info("wechat access token response", zap.String("body", string(body)))

	var resp AccessTokenResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		log.Error("json.Unmarshal failed", zap.Error(err))
		return err
	}

	err = rdb.SetNX(ctx, "wechat_access_token", resp.AccessToken, time.Duration(resp.ExpiresIn)*time.Second).Err()
	if err != nil {
		log.Error("rdb.SetNX failed", zap.Error(err))
		return err
	}

	util.UpdateWechatAccessToken()

	return nil
}

type AccessTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}
