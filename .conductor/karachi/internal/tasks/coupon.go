package tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"time"

	"github.com/hibiken/asynq"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/merchantexclusivecoupon"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
	"tandian-server/internal/pay"
	"tandian-server/internal/util/cloudbase"
)

const (
	TypeRedeemCouponCode = "task:redeem_coupon_code"
	TypeReturnCouponCode = "task:return_coupon_code"
)

type RedeemCouponPayload struct {
	TransactionId string `json:"transaction_id"`
	OutTradeNo    string `json:"out_trade_no"`
	Openid        string `json:"openid"`
}

type ReturnCouponPayload struct {
	CouponCode      string `json:"coupon_code"`
	StockId         string `json:"stock_id"`
	ReturnRequestNo string `json:"return_request_no"`
}

func NewRedeemCouponCodeTask(orderNo, transactionId, openid string) (*asynq.Task, error) {
	payload, err := json.Marshal(RedeemCouponPayload{
		TransactionId: transactionId,
		OutTradeNo:    orderNo,
		Openid:        openid,
	})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(
			TypeRedeemCouponCode,
			payload,
			asynq.MaxRetry(2),
			asynq.Timeout(5*time.Minute),
			asynq.Queue("coupon"),
			asynq.Retention(30*24*time.Hour),
			asynq.Unique(30*24*time.Hour),
			asynq.TaskID(fmt.Sprintf("REDEEM_COUPON_FOR:%s", orderNo)),
		),
		nil
}

func NewRetrunCouponCodeTask(couponCode, stockId, returnRequestNo string) (*asynq.Task, error) {
	payload, err := json.Marshal(ReturnCouponPayload{
		CouponCode:      couponCode,
		StockId:         stockId,
		ReturnRequestNo: returnRequestNo,
	})
	if err != nil {
		return nil, err
	}

	return asynq.NewTask(
			TypeRedeemCouponCode,
			payload,
			asynq.MaxRetry(2),
			asynq.Timeout(5*time.Minute),
			asynq.Queue("coupon"),
			asynq.Retention(30*24*time.Hour),
			asynq.Unique(30*24*time.Hour),
			asynq.TaskID(fmt.Sprintf("RETURN_COUPON_FOR:%s", couponCode)),
		),
		nil
}

func EnqueueRedeemCouponCodeTask(orderNo, transactionId, openid string) error {
	log := logger.Get("tasks:coupon")
	task, err := NewRedeemCouponCodeTask(orderNo, transactionId, openid)
	if err != nil {
		log.Error("Failed to create NewRedeemCouponCodeTask", zap.Error(err))
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	// Process between 1-5 seconds later
	delay := time.Duration(rand.Intn(5)+1) * time.Second

	info, err := client.Enqueue(task,
		asynq.ProcessIn(delay),
	)
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return err
	}

	log.Info("Enqueued task", zap.String("id", info.ID), zap.String("queue", info.Queue))

	return nil
}

func EnqueueReturnCouponCodeTask(couponCode, stockId, returnRequestNo string) error {
	log := logger.Get("tasks:coupon")
	task, err := NewRetrunCouponCodeTask(couponCode, stockId, returnRequestNo)
	if err != nil {
		log.Error("Failed to create NewRetrunCouponCodeTask", zap.Error(err))
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	// Process between 1-5 seconds later
	delay := time.Duration(rand.Intn(5)+1) * time.Second

	info, err := client.Enqueue(task, asynq.ProcessIn(delay))
	if err != nil {
		log.Error("Failed to enqueue task", zap.Error(err))
		return err
	}

	log.Info("Enqueued task", zap.String("id", info.ID), zap.String("queue", info.Queue))

	return nil
}

// HandleRedeemCouponCodeTask.
func HandleRedeemCouponCodeTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("task:redeem_coupon_code").With(zap.String("task_type", task.Type()))
	log.Info("Starting query redeem_coupon_code task")

	var p RedeemCouponPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	// Query order details
	orderQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"order_no": map[string]interface{}{
					"$eq": p.OutTradeNo,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":             true,
			"order_no":        true,
			"coupon_code":     true,
			"coupon_stock_id": true,
			"status":          true,
		},
	}

	orderData, err := cloudbase.GetItem(ctx, "orders", orderQuery)
	if err != nil {
		log.Error("Failed to get order", zap.Error(err), zap.String("order_no", p.OutTradeNo))
		return fmt.Errorf("failed to get order: %w", err)
	}

	// Check if orderData is empty (order not found)
	if len(orderData) == 0 {
		log.Error("Order not found", zap.String("order_no", p.OutTradeNo))
		return nil
	}

	// Check if order has coupon information
	couponCode, hasCouponCode := orderData["coupon_code"].(string)
	couponStockId, hasCouponStockId := orderData["coupon_stock_id"].(string)

	// If no coupon associated with this order, return successfully
	if !hasCouponCode || !hasCouponStockId || couponCode == "" || couponStockId == "" {
		log.Info("No coupon associated with order", zap.String("order_no", p.OutTradeNo))

		// Write empty result to indicate no coupon was redeemed
		emptyResult := map[string]interface{}{
			"message":  "No coupon associated with order",
			"order_no": p.OutTradeNo,
		}
		resultPayload, err := json.Marshal(emptyResult)
		if err != nil {
			log.Error("failed to marshal empty result payload", zap.Error(err))
		} else {
			if _, err := task.ResultWriter().Write(resultPayload); err != nil {
				log.Error("failed to write task result", zap.Error(err))
			}

			inspector := asynq.NewInspector(
				asynq.RedisClientOpt{
					Addr:     config.AppConfig.Asynq.RedisAddr,
					Password: config.AppConfig.Asynq.RedisPassword,
					DB:       config.AppConfig.Asynq.RedisDB,
				},
			)
			inspector.CancelProcessing(task.ResultWriter().TaskID())
			inspector.ArchiveTask("coupon", task.ResultWriter().TaskID())
			// inspector.DeleteTask("coupon", task.ResultWriter().TaskID())
		}

		return nil
	}

	// Build request
	req := merchantexclusivecoupon.UseCouponRequest{
		CouponCode: core.String(couponCode),
		Openid:     core.String(p.Openid),
		StockId:    core.String(couponStockId),
	}

	// Call API
	resp, result, err := pay.UseCoupon(ctx, req)
	if err != nil {
		return err
	}

	if result.Response.StatusCode != http.StatusOK {
		return err
	}

	resultPayload, err := json.Marshal(resp)
	if err != nil {
		log.Error("failed to marshal result payload", zap.Error(err))
	} else {
		if _, err := task.ResultWriter().Write(resultPayload); err != nil {
			log.Error("failed to write task result", zap.Error(err))
		}
	}

	log.Info("Successfully redeemed coupon for order",
		zap.String("order_no", p.OutTradeNo),
		zap.String("coupon_code", couponCode),
		zap.String("coupon_stock_id", *resp.StockId),
		zap.String("transaction_id", p.TransactionId),
		zap.String("openid", p.Openid),
		zap.String("usetime", *resp.WechatpayUseTime),
	)

	return nil
}

// HandleReturnCouponCodeTask.
func HandleReturnCouponCodeTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("task:return_coupon_code").With(zap.String("task_type", task.Type()))
	log.Info("Starting query return_coupon_code task")

	var p ReturnCouponPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	// // Query order details
	// orderQuery := map[string]interface{}{
	// 	"filter": map[string]interface{}{
	// 		"where": map[string]interface{}{
	// 			"coupon_code": map[string]interface{}{
	// 				"$eq": p.CouponCode,
	// 			},
	// 		},
	// 	},
	// 	"select": map[string]interface{}{
	// 		"_id":             true,
	// 		"order_no":        true,
	// 		"coupon_code":     true,
	// 		"coupon_stock_id": true,
	// 		"status":          true,
	// 	},
	// }

	// orderData, err := cloudbase.GetItem(ctx, "orders", orderQuery)
	// if err != nil {
	// 	// log.Error("Failed to get order", zap.Error(err), zap.String("order_no", p.OutTradeNo))
	// 	return fmt.Errorf("failed to get order: %w", err)
	// }

	// // Check if order has coupon information
	// couponCode, hasCouponCode := orderData["coupon_code"].(string)
	// couponStockId, hasCouponStockId := orderData["coupon_stock_id"].(string)

	// // If no coupon associated with this order, return successfully
	// if !hasCouponCode || !hasCouponStockId || couponCode == "" || couponStockId == "" {
	// 	log.Info("No coupon associated with order", zap.String("order_no", p.OutTradeNo))
	// 	return nil
	// }

	// Build request
	req := merchantexclusivecoupon.ReturnCouponRequest{
		CouponCode: core.String(p.CouponCode),
		StockId:    core.String(p.StockId),
	}

	// Call API
	resp, result, err := pay.ReturnCoupon(ctx, req)
	if err != nil {
		return err
	}

	if result.Response.StatusCode != http.StatusOK {
		return err
	}

	resultPayload, err := json.Marshal(resp)
	if err != nil {
		log.Error("failed to marshal result payload", zap.Error(err))
	} else {
		if _, err := task.ResultWriter().Write(resultPayload); err != nil {
			log.Error("failed to write task result", zap.Error(err))
		}
	}

	log.Info("Return coupon for order",
		zap.String("coupon_code", p.CouponCode),
		zap.String("coupon_stock_id", p.StockId),
		zap.String("wechatpay_return_time", resp.WechatpayReturnTime.String()),
	)

	return nil
}
