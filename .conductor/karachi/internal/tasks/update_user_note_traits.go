package tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	flagsmithClient "github.com/Flagsmith/flagsmith-go-client/v4"
	"github.com/hibiken/asynq"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/flagsmith"
	"tandian-server/internal/logger"
	"tandian-server/internal/util/cloudbase"
)

const (
	TypeUpdateUserNoteTraits = "flagsmith:update_user_note_traits"
)

type UpdateUserNoteTraitsPayload struct {
	UserID string `json:"user_id"`
}

// NewUpdateUserNoteTraitsTask creates a new task to update user note traits in Flagsmith.
func NewUpdateUserNoteTraitsTask(userID string) (*asynq.Task, error) {
	log := logger.Get("tasks:flagsmith")
	payload, err := json.Marshal(UpdateUserNoteTraitsPayload{UserID: userID})
	if err != nil {
		if log != nil {
			log.Error("Failed to marshal payload", zap.Error(err))
		}
		return nil, err
	}

	if log != nil {
		log.Debug("Creating update user note traits task", zap.String("userID", userID))
	}

	return asynq.NewTask(
		TypeUpdateUserNoteTraits,
		payload,
		asynq.MaxRetry(3),
		asynq.Timeout(2*time.Minute),
		asynq.Queue("default"),
		asynq.Retention(7*24*time.Hour),
	), nil
}

// HandleUpdateUserNoteTraitsTask handles the task to update user note traits.
func HandleUpdateUserNoteTraitsTask(ctx context.Context, task *asynq.Task) error {
	log := logger.Get("tasks:flagsmith").With(zap.String("task_type", task.Type()))
	if log != nil {
		log.Info("Handling update user note traits task")
	}

	var p UpdateUserNoteTraitsPayload
	if err := json.Unmarshal(task.Payload(), &p); err != nil {
		if log != nil {
			log.Error("Failed to unmarshal payload", zap.Error(err))
		}
		return fmt.Errorf("json.Unmarshal failed: %w: %w", err, asynq.SkipRetry)
	}

	// Check if Flagsmith is enabled
	if !config.AppConfig.Flagsmith.Enabled {
		if log != nil {
			log.Info("Flagsmith is disabled, skipping task", zap.String("user_id", p.UserID))
		}

		// Write task result even when Flagsmith is disabled
		resultData := map[string]interface{}{
			"user_id":    p.UserID,
			"status":     "skipped",
			"reason":     "flagsmith_disabled",
			"updated_at": time.Now().Format(time.RFC3339),
		}

		resultBytes, _ := json.Marshal(resultData)
		task.ResultWriter().Write(resultBytes)

		return nil
	}

	// Get user note statistics from CloudBase
	stats, err := getUserNoteStats(ctx, p.UserID)
	if err != nil {
		if log != nil {
			log.Error("Failed to get user note stats",
				zap.String("user_id", p.UserID),
				zap.Error(err))
		}
		return err
	}

	// Create traits map with note-related data
	traits := map[string]interface{}{
		"has_uploaded_notes":   stats.TotalNotes > 0,
		"notes_approved_count": stats.ApprovedCount,
		"notes_rejected_count": stats.RejectedCount,
		"notes_total_count":    stats.TotalNotes,
	}

	// Calculate approval rate if user has submitted notes
	if stats.ApprovedCount+stats.RejectedCount > 0 {
		approvalRate := float64(stats.ApprovedCount) / float64(stats.ApprovedCount+stats.RejectedCount) * 100
		traits["note_approval_rate"] = approvalRate

		// Set user trust level based on approval rate and volume
		if stats.ApprovedCount >= 10 && approvalRate >= 90 {
			traits["note_trust_level"] = "high"
		} else if stats.ApprovedCount >= 5 && approvalRate >= 80 {
			traits["note_trust_level"] = "medium"
		} else {
			traits["note_trust_level"] = "low"
		}
	}

	// Check if Flagsmith client is initialized before using it
	if !flagsmith.IsInitialized() {
		if log != nil {
			log.Error("Flagsmith client not initialized, skipping trait update")
		}

		// Write task result even when client is not initialized
		resultData := map[string]interface{}{
			"user_id":        p.UserID,
			"approved_count": stats.ApprovedCount,
			"rejected_count": stats.RejectedCount,
			"total_notes":    stats.TotalNotes,
			"status":         "error",
			"error":          "flagsmith_client_not_initialized",
			"updated_at":     time.Now().Format(time.RFC3339),
		}

		resultBytes, _ := json.Marshal(resultData)
		task.ResultWriter().Write(resultBytes)

		return fmt.Errorf("flagsmith client not initialized")
	}

	// Update the traits in Flagsmith
	client := flagsmith.GetClient()

	_, err = client.GetIdentityFlags(ctx, p.UserID, mapToTraitSlice(traits))
	if err != nil {
		if log != nil {
			log.Error("Failed to update user note traits",
				zap.String("user_id", p.UserID),
				zap.Error(err))
		}
		return err
	}

	if log != nil {
		log.Info("Successfully updated user note traits",
			zap.String("user_id", p.UserID),
			zap.Int("approved", stats.ApprovedCount),
			zap.Int("rejected", stats.RejectedCount),
			zap.Int("total", stats.TotalNotes))
	}

	// Write task result for retention
	resultData := map[string]interface{}{
		"user_id":        p.UserID,
		"approved_count": stats.ApprovedCount,
		"rejected_count": stats.RejectedCount,
		"total_notes":    stats.TotalNotes,
		"status":         "success",
		"updated_at":     time.Now().Format(time.RFC3339),
	}

	// Add approval rate if calculated
	if stats.ApprovedCount+stats.RejectedCount > 0 {
		approvalRate := float64(stats.ApprovedCount) / float64(stats.ApprovedCount+stats.RejectedCount) * 100
		resultData["approval_rate"] = approvalRate
	}

	// Add trust level if it was set
	if level, exists := traits["note_trust_level"]; exists {
		resultData["trust_level"] = level
	}

	resultBytes, err := json.Marshal(resultData)
	if err != nil {
		if log != nil {
			log.Error("Failed to marshal task result", zap.Error(err))
		}
		// Don't return error since the main task succeeded
	}

	// Asynq will store this result with the task
	_, err = task.ResultWriter().Write(resultBytes)
	if err != nil {
		if log != nil {
			log.Error("Failed to write task result", zap.Error(err))
		}
		// Don't return error since the main task succeeded
	}

	return nil
}

// UserNoteStats represents note statistics for a user.
type UserNoteStats struct {
	TotalNotes    int
	ApprovedCount int
	RejectedCount int
}

// getUserNoteStats queries CloudBase for user's note statistics.
func getUserNoteStats(ctx context.Context, userID string) (*UserNoteStats, error) {
	log := logger.Get("tasks:flagsmith")

	// Query note_stats table for user's statistics
	filter := map[string]interface{}{
		"user_id": userID,
	}

	// Get the note stats record for this user
	stat, err := cloudbase.FindOne(ctx, "note_stats", filter)
	if err != nil {
		if log != nil {
			log.Error("Failed to get note stats", zap.Error(err))
		}
		return nil, err
	}

	// If no stats found, return zero values
	if len(stat) == 0 {
		if log != nil {
			log.Info("No note stats found for user", zap.String("user_id", userID))
		}
		return &UserNoteStats{
			TotalNotes:    0,
			ApprovedCount: 0,
			RejectedCount: 0,
		}, nil
	}

	// Helper function to safely get int value from map
	getIntValue := func(key string) int {
		if val, ok := stat[key]; ok {
			switch v := val.(type) {
			case float64:
				return int(v)
			case int:
				return v
			case int64:
				return int(v)
			}
		}
		return 0
	}

	// Get the counts from the note_stats record
	// Note: field names in DB are "approved_counts" and "rejected_counts" (plural)
	approved := getIntValue("approved_counts")
	rejected := getIntValue("rejected_counts")
	total := approved + rejected

	return &UserNoteStats{
		TotalNotes:    total,
		ApprovedCount: approved,
		RejectedCount: rejected,
	}, nil
}

// Helper function to convert map to trait slice.
func mapToTraitSlice(m map[string]interface{}) []*flagsmithClient.Trait {
	traitSlice := make([]*flagsmithClient.Trait, 0, len(m))
	for k, v := range m {
		traitSlice = append(traitSlice, &flagsmithClient.Trait{TraitKey: k, TraitValue: v})
	}
	return traitSlice
}

// EnqueueUpdateUserNoteTraitsTask enqueues a task to update user note traits.
func EnqueueUpdateUserNoteTraitsTask(userID string) error {
	log := logger.Get("tasks:flagsmith")

	task, err := NewUpdateUserNoteTraitsTask(userID)
	if err != nil {
		if log != nil {
			log.Error("Failed to create update user note traits task", zap.Error(err))
		}
		return err
	}

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	info, err := client.Enqueue(task)
	if err != nil {
		if log != nil {
			log.Error("Failed to enqueue task", zap.Error(err))
		}
		return err
	}

	if log != nil {
		log.Info("Enqueued update user note traits task",
			zap.String("task_id", info.ID),
			zap.String("queue", info.Queue),
			zap.String("user_id", userID))
	}

	return nil
}
