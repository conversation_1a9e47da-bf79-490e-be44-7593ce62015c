package flagsmith

import (
	"context"
	"fmt"
	"time"

	flagsmith "github.com/Flagsmith/flagsmith-go-client/v4"
	"go.uber.org/zap"
)

// UserTraits defines common traits for users.
type UserTraits struct {
	// Order-related traits
	TotalFinishedOrders int       `json:"total_finished_orders"`
	LastOrderDate       time.Time `json:"last_order_date"`
	TotalSpent          float64   `json:"total_spent_yuan"` // In Yuan
	AverageOrderValue   float64   `json:"average_order_value_yuan"`

	// User behavior traits and Note-related traits
	UserType           string `json:"user_type"` // new, regular, vip
	HasUploadedNotes   bool   `json:"has_uploaded_notes"`
	NotesApprovedCount int    `json:"notes_approved_count"`
	NotesRejectedCount int    `json:"notes_rejected_count"`

	// Campaign participation
	HelpCampaignsInitiated int `json:"help_campaigns_initiated"`
	HelpCampaignsCompleted int `json:"help_campaigns_completed"`
	HelpCampaignsAssisted  int `json:"help_campaigns_assisted"`

	// Coupon usage
	CouponsUsed      int `json:"coupons_used"`
	CouponsAvailable int `json:"coupons_available"`

	// Account info
	RegistrationDate       time.Time `json:"registration_date"`
	DaysSinceRegistration  int       `json:"days_since_registration"`
	PreferredPaymentMethod string    `json:"preferred_payment_method"` // wechat, balance
}

// TraitService handles user trait operations.
type TraitService struct {
	client *flagsmith.Client
	logger *zap.Logger
}

// NewTraitService creates a new trait service.
func NewTraitService() *TraitService {
	return &TraitService{
		client: GetClient(),
		logger: logger,
	}
}

// UpdateUserTraits updates traits for a specific user.
func (s *TraitService) UpdateUserTraits(ctx context.Context, userID string, traits UserTraits) error {
	traitsMap := s.traitsToMap(traits)
	traitSlice := mapToTraitSlice(traitsMap)

	// Get identity flags with traits
	flags, err := s.client.GetIdentityFlags(ctx, userID, traitSlice)
	if err != nil {
		s.logger.Error("Failed to update user traits",
			zap.String("user_id", userID),
			zap.Error(err))
		return fmt.Errorf("failed to update traits for user %s: %w", userID, err)
	}

	s.logger.Info("User traits updated successfully",
		zap.String("user_id", userID),
		zap.Int("traits_count", len(traitsMap)))

	// Log if user has special segments
	if s.isVIPUser(flags) {
		s.logger.Info("User identified as VIP",
			zap.String("user_id", userID))
	}

	return nil
}

// UpdateOrderTraits updates order-related traits after an order event.
func (s *TraitService) UpdateOrderTraits(ctx context.Context, userID string, orderCount int, totalSpent float64) error {
	traits := make(map[string]interface{})

	traits["total_orders"] = orderCount
	traits["finished_ten_or_more_orders"] = orderCount >= 10
	traits["total_spent_yuan"] = totalSpent

	if orderCount > 0 {
		traits["average_order_value_yuan"] = totalSpent / float64(orderCount)
	}

	traits["last_order_date"] = time.Now().Format(time.RFC3339)

	// Determine user type based on order count
	userType := "new"
	if orderCount >= 10 {
		userType = "vip"
	} else if orderCount >= 3 {
		userType = "regular"
	}
	traits["user_type"] = userType

	_, err := s.client.GetIdentityFlags(ctx, userID, mapToTraitSlice(traits))
	if err != nil {
		s.logger.Error("Failed to update order traits",
			zap.String("user_id", userID),
			zap.Int("order_count", orderCount),
			zap.Error(err))
		return err
	}

	s.logger.Info("Order traits updated",
		zap.String("user_id", userID),
		zap.Int("order_count", orderCount),
		zap.String("user_type", userType))

	return nil
}

// UpdateCampaignTraits updates help campaign related traits.
func (s *TraitService) UpdateCampaignTraits(
	ctx context.Context,
	userID string,
	initiated, completed, assisted int,
) error {
	traits := map[string]interface{}{
		"help_campaigns_initiated": initiated,
		"help_campaigns_completed": completed,
		"help_campaigns_assisted":  assisted,
	}

	_, err := s.client.GetIdentityFlags(ctx, userID, mapToTraitSlice(traits))
	if err != nil {
		s.logger.Error("Failed to update campaign traits",
			zap.String("user_id", userID),
			zap.Error(err))
		return err
	}

	return nil
}

// GetUserFlags gets all flags for a user with their current traits.
func (s *TraitService) GetUserFlags(ctx context.Context, userID string) (*flagsmith.Flags, error) {
	flags, err := s.client.GetIdentityFlags(ctx, userID, nil)
	if err != nil {
		s.logger.Error("Failed to get user flags",
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, err
	}

	return &flags, nil
}

// CheckFeatureForUser checks if a feature is enabled for a specific user.
func (s *TraitService) CheckFeatureForUser(ctx context.Context, userID, featureName string) (bool, error) {
	flags, err := s.GetUserFlags(ctx, userID)
	if err != nil {
		return false, err
	}

	enabled, err := flags.IsFeatureEnabled(featureName)
	if err != nil {
		s.logger.Warn("Feature not found",
			zap.String("user_id", userID),
			zap.String("feature", featureName))
		return false, nil
	}

	return enabled, nil
}

// GetFeatureValueForUser gets a feature value for a specific user.
func (s *TraitService) GetFeatureValueForUser(ctx context.Context, userID, featureName string) (interface{}, error) {
	flags, err := s.GetUserFlags(ctx, userID)
	if err != nil {
		return nil, err
	}

	value, err := flags.GetFeatureValue(featureName)
	if err != nil {
		s.logger.Warn("Feature value not found",
			zap.String("user_id", userID),
			zap.String("feature", featureName))
		return nil, nil
	}

	return value, nil
}

// Helper methods

func mapToTraitSlice(m map[string]interface{}) []*flagsmith.Trait {
	traitSlice := make([]*flagsmith.Trait, 0, len(m))
	for k, v := range m {
		traitSlice = append(traitSlice, &flagsmith.Trait{TraitKey: k, TraitValue: v})
	}
	return traitSlice
}

func (s *TraitService) traitsToMap(traits UserTraits) map[string]interface{} {
	m := make(map[string]interface{})

	// Order traits
	m["total_orders"] = traits.TotalFinishedOrders
	m["finished_ten_or_more_orders"] = traits.TotalFinishedOrders >= 10
	if !traits.LastOrderDate.IsZero() {
		m["last_order_date"] = traits.LastOrderDate.Format(time.RFC3339)
	}
	m["total_spent_yuan"] = traits.TotalSpent
	m["average_order_value_yuan"] = traits.AverageOrderValue

	// User behavior
	if traits.UserType != "" {
		m["user_type"] = traits.UserType
	}
	m["has_uploaded_notes"] = traits.HasUploadedNotes
	m["notes_approved_count"] = traits.NotesApprovedCount
	m["notes_rejected_count"] = traits.NotesRejectedCount

	// Campaigns
	m["help_campaigns_initiated"] = traits.HelpCampaignsInitiated
	m["help_campaigns_completed"] = traits.HelpCampaignsCompleted
	m["help_campaigns_assisted"] = traits.HelpCampaignsAssisted

	// Coupons
	m["coupons_used"] = traits.CouponsUsed
	m["coupons_available"] = traits.CouponsAvailable

	// Account
	if !traits.RegistrationDate.IsZero() {
		m["registration_date"] = traits.RegistrationDate.Format(time.RFC3339)
		m["days_since_registration"] = traits.DaysSinceRegistration
	}
	if traits.PreferredPaymentMethod != "" {
		m["preferred_payment_method"] = traits.PreferredPaymentMethod
	}

	return m
}

func (s *TraitService) isVIPUser(flags flagsmith.Flags) bool {
	// Check if user has VIP feature enabled
	vip, _ := flags.IsFeatureEnabled("vip_features")
	return vip
}
