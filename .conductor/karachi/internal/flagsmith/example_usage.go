package flagsmith

import (
	"context"
	"time"

	"go.uber.org/zap"

	"tandian-server/internal/config"
)

// Example usage and initialization code

// InitializeFlagsmith initializes the Flagsmith client with config.
func InitializeFlagsmith(logger *zap.Logger) error {
	cfg := config.AppConfig.Flagsmith

	if !cfg.Enabled {
		logger.Info("Flagsmith is disabled in configuration")
		return nil
	}

	flagsmithConfig := Config{
		EnvironmentKey: cfg.EnvironmentKey,
		BaseURL:        cfg.BaseURL,
		LocalEval:      cfg.LocalEval,
		Timeout:        10 * time.Second,
	}

	return Initialize(flagsmithConfig, logger)
}

// ExampleUsageInHandlers shows how to use Flagsmith in your handlers.
func ExampleUsageInHandlers() {
	ctx := context.Background()
	userID := "user_123456"

	// 1. Update traits after order completion
	orderService := NewOrderService()
	err := orderService.OnOrderCompleted(ctx, userID, "order_789", 299.99)
	if err != nil {
		// Handle error
	}

	// 2. Check feature eligibility
	traitService := NewTraitService()

	// Check if user can access VIP features
	isVIP, err := traitService.CheckFeatureForUser(ctx, userID, "vip_features")
	if err != nil {
		// Handle error
	}
	if isVIP {
		// Show VIP content
	}

	// 3. Get remote config value
	discountRate, err := traitService.GetFeatureValueForUser(ctx, userID, "special_discount_rate")
	if err != nil {
		// Handle error
	}
	// Use discountRate in calculation
	_ = discountRate

	// 4. Update campaign participation traits
	err = traitService.UpdateCampaignTraits(ctx, userID, 2, 1, 5)
	if err != nil {
		// Handle error
	}

	// 5. Check A/B test variants
	variant, err := traitService.GetFeatureValueForUser(ctx, userID, "checkout_flow_variant")
	if err != nil {
		// Handle error
	}
	switch variant {
	case "variant_a":
		// Show checkout flow A
	case "variant_b":
		// Show checkout flow B
	default:
		// Show default checkout flow
	}
}

// ExampleSegmentUseCases shows practical segment examples.
func ExampleSegmentUseCases() {
	// In Flagsmith dashboard, you can create segments based on traits:

	// 1. VIP Users Segment
	// Rule: finished_ten_or_more_orders = true
	// Use: Enable special features, discounts, or early access

	// 2. New Users Segment
	// Rule: total_orders < 3
	// Use: Show onboarding tooltips, first-time buyer discounts

	// 3. High Value Users Segment
	// Rule: total_spent_yuan > 1000
	// Use: Premium support, exclusive offers

	// 4. Active Campaign Participants
	// Rule: help_campaigns_assisted > 5
	// Use: Community rewards, social features

	// 5. Note Contributors
	// Rule: notes_approved_count > 10
	// Use: Content creator badges, review privileges

	// 6. Recent Shoppers
	// Rule: last_order_date > (now - 7 days)
	// Use: Time-sensitive promotions

	// 7. Preferred Payment Method Segments
	// Rule: preferred_payment_method = "wechat"
	// Use: Payment-specific promotions
}

// ExampleFeatureFlags shows practical feature flag examples.
func ExampleFeatureFlags() {
	// Feature flags you can create in Flagsmith:

	// 1. new_checkout_flow
	// Type: Boolean
	// Use: Gradually roll out new checkout experience

	// 2. special_discount_rate
	// Type: Number (Remote Config)
	// Use: Dynamic discount rates per segment

	// 3. max_help_campaigns_per_day
	// Type: Number (Remote Config)
	// Use: Control campaign creation limits

	// 4. enable_express_checkout
	// Type: Boolean
	// Use: Enable one-click checkout for VIP users

	// 5. coupon_stacking_enabled
	// Type: Boolean
	// Use: Allow multiple coupon usage

	// 6. note_auto_approval_threshold
	// Type: Number (Remote Config)
	// Use: Auto-approve notes from trusted users

	// 7. referral_bonus_amount
	// Type: Number (Remote Config)
	// Use: Dynamic referral rewards
}
