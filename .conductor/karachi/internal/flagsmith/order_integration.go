package flagsmith

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"tandian-server/internal/util/cloudbase"
)

// OrderService provides order-related Flagsmith integrations.
type OrderService struct {
	traitService *TraitService
	logger       *zap.Logger
}

// NewOrderService creates a new order service.
func NewOrderService() *OrderService {
	return &OrderService{
		traitService: NewTraitService(),
		logger:       logger,
	}
}

// OnOrderCompleted updates user traits when an order is completed.
func (s *OrderService) OnOrderCompleted(ctx context.Context, userID, orderID string, orderAmount float64) error {
	// Get user's order statistics from CloudBase
	stats, err := s.getUserOrderStats(ctx, userID)
	if err != nil {
		s.logger.Error("Failed to get user order stats",
			zap.String("user_id", userID),
			zap.Error(err))
		return err
	}

	// Update order traits
	err = s.traitService.UpdateOrderTraits(ctx, userID, stats.TotalOrders, stats.TotalSpent)
	if err != nil {
		return fmt.Errorf("failed to update order traits: %w", err)
	}

	// Check for special offers based on order count
	s.checkSpecialOffers(ctx, userID, stats.TotalOrders)

	return nil
}

// OnOrderVerified updates traits when an order is verified (status 20).
func (s *OrderService) OnOrderVerified(ctx context.Context, userID, orderID string) error {
	// Update note upload trait
	traits := map[string]interface{}{
		"has_uploaded_notes":  false, // Will be true after note upload
		"last_verified_order": orderID,
	}

	_, err := s.traitService.client.GetIdentityFlags(ctx, userID, mapToTraitSlice(traits))
	if err != nil {
		s.logger.Error("Failed to update verification traits",
			zap.String("user_id", userID),
			zap.String("order_id", orderID),
			zap.Error(err))
		return err
	}

	return nil
}

// OnNoteApproved updates traits when a note is approved.
func (s *OrderService) OnNoteApproved(ctx context.Context, userID, orderID string) error {
	stats, err := s.getUserNoteStats(ctx, userID)
	if err != nil {
		return err
	}

	traits := map[string]interface{}{
		"has_uploaded_notes":   true,
		"notes_approved_count": stats.ApprovedCount,
		"notes_rejected_count": stats.RejectedCount,
		"note_approval_rate":   float64(stats.ApprovedCount) / float64(stats.ApprovedCount+stats.RejectedCount),
	}

	_, err = s.traitService.client.GetIdentityFlags(ctx, userID, mapToTraitSlice(traits))
	return err
}

// CheckUserEligibility checks if a user is eligible for certain features.
func (s *OrderService) CheckUserEligibility(ctx context.Context, userID, feature string) (bool, interface{}, error) {
	// Check feature flag
	enabled, err := s.traitService.CheckFeatureForUser(ctx, userID, feature)
	if err != nil {
		return false, nil, err
	}

	// Get feature value (config)
	value, err := s.traitService.GetFeatureValueForUser(ctx, userID, feature)
	if err != nil {
		return enabled, nil, err
	}

	return enabled, value, nil
}

// Helper methods

type UserOrderStats struct {
	TotalOrders int
	TotalSpent  float64
}

func (s *OrderService) getUserOrderStats(ctx context.Context, userID string) (*UserOrderStats, error) {
	// This would query CloudBase for actual order statistics
	// Query orders collection for user statistics
	filter := map[string]interface{}{
		"user_id": userID,
		"status": map[string]interface{}{
			"$in": []string{"20", "30", "31"}, // Verified, under review, approved
		},
	}

	// Count total orders for this user
	totalOrders, err := cloudbase.Count(ctx, "orders", filter)
	if err != nil {
		return nil, err
	}

	// For now, returning mock total spent value
	// TODO: Calculate actual total spent from orders
	return &UserOrderStats{
		TotalOrders: int(totalOrders),
		TotalSpent:  500.0, // Mock value
	}, nil
}

type UserNoteStats struct {
	ApprovedCount int
	RejectedCount int
}

func (s *OrderService) getUserNoteStats(ctx context.Context, userID string) (*UserNoteStats, error) {
	// This would query CloudBase for actual note statistics
	// Mock implementation
	return &UserNoteStats{
		ApprovedCount: 3,
		RejectedCount: 1,
	}, nil
}

func (s *OrderService) checkSpecialOffers(ctx context.Context, userID string, orderCount int) {
	// Check for milestone rewards
	milestones := []int{1, 5, 10, 20, 50, 100}

	for _, milestone := range milestones {
		if orderCount == milestone {
			feature := fmt.Sprintf("milestone_%d_reward", milestone)
			enabled, err := s.traitService.CheckFeatureForUser(ctx, userID, feature)
			if err != nil {
				s.logger.Error("Failed to check milestone feature",
					zap.String("user_id", userID),
					zap.Int("milestone", milestone),
					zap.Error(err))
				continue
			}

			if enabled {
				s.logger.Info("User eligible for milestone reward",
					zap.String("user_id", userID),
					zap.Int("milestone", milestone))
				// Trigger reward logic here
			}
		}
	}
}
