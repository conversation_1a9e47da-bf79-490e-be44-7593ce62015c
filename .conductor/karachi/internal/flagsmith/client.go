package flagsmith

import (
	"context"
	"sync"
	"time"

	flagsmith "github.com/Flagsmith/flagsmith-go-client/v4"
	"go.uber.org/zap"
)

var (
	client *flagsmith.Client
	once   sync.Once
	logger *zap.Logger
)

// Config holds the Flagsmith configuration.
type Config struct {
	EnvironmentKey string
	BaseURL        string // Optional: for self-hosted instances
	LocalEval      bool   // Enable local evaluation mode
	Timeout        time.Duration
}

// Initialize sets up the Flagsmith client.
func Initialize(cfg Config, log *zap.Logger) error {
	var err error
	once.Do(func() {
		logger = log

		options := []flagsmith.Option{
			flagsmith.WithRequestTimeout(cfg.Timeout),
		}

		if cfg.BaseURL != "" {
			options = append(options, flagsmith.WithBaseURL(cfg.BaseURL))
		}

		if cfg.LocalEval {
			options = append(options, flagsmith.WithLocalEvaluation(context.Background()))
			options = append(options, flagsmith.WithEnvironmentRefreshInterval(60*time.Second))
		}

		// Add retry configuration
		options = append(options, flagsmith.WithRetries(3, 5*time.Second))

		client = flagsmith.NewClient(cfg.EnvironmentKey, options...)

		logger.Info("Flagsmith client initialized",
			zap.Bool("local_eval", cfg.LocalEval),
			zap.String("base_url", cfg.BaseURL))
	})

	return err
}

// GetClient returns the Flagsmith client instance.
func GetClient() *flagsmith.Client {
	if client == nil {
		panic("Flagsmith client not initialized. Call Initialize() first")
	}
	return client
}

// IsInitialized checks if the client is initialized.
func IsInitialized() bool {
	return client != nil
}
