package logger

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

const (
	// TraceLevel defines a custom log level for trace messages.
	// It's set to -2, which is below zap's DebugLevel.
	TraceLevel = zapcore.Level(-2)
)

var (
	loggers       = make(map[string]*zap.Logger)
	atomicLevels  = make(map[string]zap.AtomicLevel)
	defaultLogger *zap.Logger
	mu            sync.RWMutex
)

// parseLevel converts a string to a zapcore.Level, with support for "trace".
func parseLevel(levelStr string) (zapcore.Level, error) {
	switch strings.ToLower(levelStr) {
	case "trace":
		return TraceLevel, nil
	default:
		return zapcore.ParseLevel(levelStr)
	}
}

// customLevelEncoder adds support for encoding the custom TraceLevel.
func customLevelEncoder(level zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	if level == TraceLevel {
		enc.AppendString("TRACE")
		return
	}

	zapcore.CapitalLevelEncoder(level, enc)
}

// Init initializes the loggers based on the provided configuration.
func Init(appEnv, defaultLevel string, moduleLevels map[string]string) error {
	mu.Lock()
	defer mu.Unlock()

	isDevelopment := appEnv == "development"

	// Create a default atomic level
	parsedDefaultLevel, err := parseLevel(defaultLevel)
	if err != nil {
		return fmt.Errorf("invalid default log level: %s", defaultLevel)
	}

	defaultAtomicLevel := zap.NewAtomicLevelAt(parsedDefaultLevel)

	// Create the default logger
	defaultLogger, err = newLogger(defaultAtomicLevel, isDevelopment)
	if err != nil {
		return fmt.Errorf("failed to create default logger: %w", err)
	}

	loggers["default"] = defaultLogger
	atomicLevels["default"] = defaultAtomicLevel

	// Create loggers for each module
	for module, levelStr := range moduleLevels {
		parsedLevel, err := parseLevel(levelStr)
		if err != nil {
			fmt.Printf(
				"warn: invalid log level '%s' for module '%s', using default. error: %v\n",
				levelStr,
				module,
				err,
			)

			loggers[module] = defaultLogger
			atomicLevels[module] = defaultAtomicLevel

			continue
		}

		atomicLevel := zap.NewAtomicLevelAt(parsedLevel)

		logger, err := newLogger(atomicLevel, isDevelopment)
		if err != nil {
			fmt.Printf("warn: failed to create logger for module '%s', using default. error: %v\n", module, err)

			loggers[module] = defaultLogger
			atomicLevels[module] = defaultAtomicLevel

			continue
		}

		loggers[module] = logger
		atomicLevels[module] = atomicLevel
	}

	return nil
}

// newLogger creates a new zap.Logger with a specific atomic level.
func newLogger(level zap.AtomicLevel, isDevelopment bool) (*zap.Logger, error) {
	var encoderConfig zapcore.EncoderConfig
	if isDevelopment {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
	} else {
		encoderConfig = zap.NewProductionEncoderConfig()
	}

	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = customLevelEncoder

	config := zap.Config{
		Level:             level,
		Development:       isDevelopment,
		DisableCaller:     true,
		DisableStacktrace: true,
		Sampling: &zap.SamplingConfig{
			Initial:    100,
			Thereafter: 100,
		},
		Encoding:         "json",
		EncoderConfig:    encoderConfig,
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
	}

	return config.Build()
}

// Get returns a logger for the specified module.
// If the module is not found, it returns the default logger.
func Get(module string) *zap.Logger {
	mu.RLock()
	defer mu.RUnlock()

	if logger, ok := loggers[strings.ToLower(module)]; ok {
		return logger
	}

	return defaultLogger
}

// LevelUpdatePayload is the struct for the dynamic level update request.
type LevelUpdatePayload struct {
	Module string `json:"module"`
	Level  string `json:"level"`
}

// LogLevelHandler returns an http.HandlerFunc that handles both GET and PUT requests
// for retrieving and updating log levels.
func LogLevelHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodGet:
			getLogLevels(w, r)
		case http.MethodPut:
			setLogLevel(w, r)
		default:
			http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
		}
	}
}

// getLogLevels handles GET requests to fetch current log levels.
func getLogLevels(w http.ResponseWriter, r *http.Request) {
	mu.RLock()
	defer mu.RUnlock()

	levels := make(map[string]string)

	for module, atomicLevel := range atomicLevels {
		level := atomicLevel.Level()
		if level == TraceLevel {
			levels[module] = "trace"
		} else {
			levels[module] = level.String()
		}
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(levels); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}

// setLogLevel handles PUT requests to update log levels.
func setLogLevel(w http.ResponseWriter, r *http.Request) {
	var payload LevelUpdatePayload
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	module := strings.ToLower(payload.Module)

	mu.Lock()
	defer mu.Unlock()

	atomicLevel, ok := atomicLevels[module]
	if !ok {
		http.Error(w, fmt.Sprintf("module '%s' not found", module), http.StatusNotFound)
		return
	}

	newLevel, err := parseLevel(payload.Level)
	if err != nil {
		http.Error(w, fmt.Sprintf("invalid log level: %s", payload.Level), http.StatusBadRequest)
		return
	}

	atomicLevel.SetLevel(newLevel)
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if err := json.NewEncoder(w).Encode(map[string]string{
		"message": fmt.Sprintf("log level for module '%s' updated to '%s'", module, payload.Level),
	}); err != nil {
		// Response headers already sent, can't return error to client
		// Just print to stderr for debugging
		fmt.Printf("Failed to encode response: %v\n", err)
	}
}

// A Comprehensive Guide to Zap Logging in Go
// https://betterstack.com/community/guides/logging/go/zap/

// Custom Level
// https://github.com/uber-go/zap/issues/680

// Since Trace is a custom level, you can't use a built-in method like log.Trace(). Instead, you need to use the Check() method to see if the TraceLevel is enabled and then write the log entry.

// The Correct Way

// This is the correct and most performant way to use the custom trace level you just added:

// 1 import (
// 2     "tandian-server/internal/logger"
// 3     "go.uber.org/zap"
// 4 )
// 5
// 6 // ...
// 7
// 8 log := logger.Get("tasks:cloudbase")
// 9
// 10 // Use log.Check() for the custom TraceLevel
// 11 if ce := log.Check(logger.TraceLevel, "Starting cloudbase token refresh."); ce != nil {
// 12     ce.Write(zap.String("component", "authentication"))
// 13 }

// Why log.Check()?

// The log.Check() method is zap's recommended way to handle conditional and custom-level logging. It's highly optimized:

// 1. It first checks if TraceLevel is enabled for the tasks:cloudbase logger.
// 2. If it's not enabled, it returns nil immediately, and your code does nothing further. This is extremely fast.
// 3. If it is enabled, it returns a CheckedEntry (ce), which you then use to write your final log message and fields.

// While it's a bit more verbose than log.Info(), it's the correct and idiomatic way to use custom levels with zap.

// https://github.com/uber-go/zap/issues/680#issuecomment-1501147315
// zap.L().Log(logger.TraceLevel, "Updating cloudbase access token in util")
