package notify

import (
	"context"

	"github.com/dfang/go-prettyjson"
	"resty.dev/v3"

	"tandian-server/internal/redis"
)

// Payload represents a WeCom message payload that supports multiple message types.
type Payload struct {
	Msgtype  string    `json:"msgtype"`
	Text     *Text     `json:"text,omitempty"`
	Markdown *Markdown `json:"markdown,omitempty"`
}

// Text represents a text message.
type Text struct {
	Content string `json:"content"`
}

// Markdown represents a markdown message.
type Markdown struct {
	Content string `json:"content"`
}

func SendToWecomGroup(url string, payload Payload) error {
	// create a Resty client
	client := resty.New()
	defer client.Close()

	rdb := redis.GetClient()

	wecomAccessToken, err := rdb.Get(context.Background(), "wecom_access_token").Result()
	if err != nil {
		return err
	}

	res, err := client.R().
		SetBody(payload). // default request content type is JSON
		SetAuthToken(wecomAccessToken).
		Put(url)

	prettyjson.Print(res)

	if err != nil {
		return err
	}

	return nil
}

// NewTextPayload creates a text message payload.
func NewTextPayload(content string) Payload {
	return Payload{
		Msgtype: "text",
		Text: &Text{
			Content: content,
		},
	}
}

// NewMarkdownPayload creates a markdown message payload.
func NewMarkdownPayload(content string) Payload {
	return Payload{
		Msgtype: "markdown",
		Markdown: &Markdown{
			Content: content,
		},
	}
}

// Example usage:
//
// Text message:
// payload := NewTextPayload("Hello World")
// SendToWecomGroup(webhookURL, payload)
//
// Markdown message:
// content := `## 标题
// > 引用文本
// **加粗文本**
// [链接文本](https://example.com)
// `
// payload := NewMarkdownPayload(content)
// SendToWecomGroup(webhookURL, payload)
//
// WeCom API Reference: https://developer.work.weixin.qq.com/document/path/99110#markdown类型
