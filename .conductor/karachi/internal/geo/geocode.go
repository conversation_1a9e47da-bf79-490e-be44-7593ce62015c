package geo

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"

	"go.uber.org/zap"
	"resty.dev/v3"
)

var (
	ErrAllKeysExhausted   = errors.New("all API keys exhausted")
	ErrInvalidCoordinates = errors.New("invalid coordinates")
	ErrServiceUnavailable = errors.New("geocoding service unavailable")
)

// KeyManager manages API keys with round-robin rotation.
type KeyManager struct {
	keys    []string
	current atomic.Uint32
	mu      sync.RWMutex
	failed  map[string]bool // Track failed keys
}

func NewKeyManager(keys []string) *KeyManager {
	return &KeyManager{
		keys:   keys,
		failed: make(map[string]bool),
	}
}

func (km *KeyManager) GetNextKey() (string, bool) {
	km.mu.RLock()
	defer km.mu.RUnlock()

	if len(km.keys) == 0 {
		return "", false
	}

	// Try all keys starting from current position
	for i := 0; i < len(km.keys); i++ {
		idx := (km.current.Add(1) - 1) % uint32(len(km.keys))
		key := km.keys[idx]

		if !km.failed[key] {
			return key, true
		}
	}

	return "", false
}

func (km *KeyManager) MarkFailed(key string) {
	km.mu.Lock()
	defer km.mu.Unlock()
	km.failed[key] = true
}

func (km *KeyManager) ResetFailures() {
	km.mu.Lock()
	defer km.mu.Unlock()
	km.failed = make(map[string]bool)
}

var (
	tdtKeyManager     *KeyManager
	tencentKeyManager *KeyManager
)

func init() {
	// Initialize Tianditu keys
	tdtKeys := []string{
		"c9780be129f2aa2283c07cb984072444",
		"020f97dfd63780eb18bdbbf98229720d",
		// Add more keys here
	}
	tdtKeyManager = NewKeyManager(tdtKeys)

	// Initialize Tencent keys
	tencentKeys := []string{
		"57GBZ-FAAKI-YSGGZ-UJ4PW-NBYSH-EIBEM",
		// Add more Tencent Map API keys here
	}
	tencentKeyManager = NewKeyManager(tencentKeys)
}

// GetGeo performs reverse geocoding with automatic fallback.
func GetGeo(lat, lng float64) (string, error) {
	ctx := context.Background()
	return GetGeoWithContext(ctx, lat, lng)
}

// GetGeoWithContext performs reverse geocoding with context support.
func GetGeoWithContext(ctx context.Context, lat, lng float64) (string, error) {
	result, err := GetGeoDetailWithContext(ctx, lat, lng)
	if err != nil {
		return "", err
	}
	return result.FormattedAddress, nil
}

// GetGeoDetailWithContext performs reverse geocoding with context support and returns detailed result.
func GetGeoDetailWithContext(ctx context.Context, lat, lng float64) (RegeocodeResult, error) {
	// Validate coordinates
	if lat < -90 || lat > 90 || lng < -180 || lng > 180 {
		return RegeocodeResult{}, ErrInvalidCoordinates
	}

	// // Try Tianditu first
	// resp, err := GetGeoViaTDT(ctx, lat, lng)
	// if err == nil && resp.FormattedAddress != "" {
	// 	return resp, nil
	// }

	// If Tianditu fails, try Tencent Maps
	tencentResp, err := GetGeoViaTencent(ctx, lat, lng)
	if err == nil && tencentResp.FormattedAddress != "" {
		return tencentResp, nil
	}

	return RegeocodeResult{}, ErrServiceUnavailable
}

type AddressComponent struct {
	Nation          string `json:"nation"`
	Province        string `json:"province"`
	City            string `json:"city"`
	District        string `json:"district"` // Renamed from 'county' to 'district' for common usage
	Street          string `json:"street"`   // Renamed from 'road' to 'street'
	StreetNumber    string `json:"street_number"`
	Adcode          string `json:"adcode"` // Renamed from 'county_code'
	Township        string `json:"township"`
	Towncode        string `json:"town_code"`
	Neighborhood    string `json:"neighborhood"`
	Building        string `json:"building"`
	Poi             string `json:"poi"`
	Address         string `json:"address"`
	RoadDistance    int    `json:"road_distance"`
	AddressDistance int    `json:"address_distance"`
	PoiDistance     int    `json:"poi_distance"`
	CityCode        string `json:"city_code"`
	ProvinceCode    string `json:"province_code"`
	PoiPosition     string `json:"poi_position"`
	AddressPosition string `json:"address_position"`
}

type RegeocodeResult struct {
	AddressComponent AddressComponent `json:"addressComponent"`
	FormattedAddress string           `json:"formatted_address"`
}

type TDTGeocodeResponse struct {
	Result struct {
		Formatted   string `json:"formatted_address"`
		AddressComp struct {
			Nation       string `json:"nation"`
			Province     string `json:"province"`
			City         string `json:"city"`
			District     string `json:"district"`
			Street       string `json:"street"`
			StreetNumber string `json:"street_number"`
		} `json:"addressComponent"`
	} `json:"result"`
	Status string `json:"status"`
	Msg    string `json:"msg"`
}

func (t *TDTGeocodeResponse) ToRegeocodeResult() RegeocodeResult {
	return RegeocodeResult{
		FormattedAddress: t.Result.Formatted,
		AddressComponent: AddressComponent{
			Nation:       t.Result.AddressComp.Nation,
			Province:     t.Result.AddressComp.Province,
			City:         t.Result.AddressComp.City,
			District:     t.Result.AddressComp.District,
			Street:       t.Result.AddressComp.Street,
			StreetNumber: t.Result.AddressComp.StreetNumber,
			Address:      t.Result.Formatted,
		},
	}
}

// Tencent Maps Response.
type TencentGeocodeResponse struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Result  struct {
		Address       string `json:"address"`
		FormattedAddr struct {
			Recommend string `json:"recommend"`
			Rough     string `json:"rough"`
		} `json:"formatted_addresses"`
		AddressComponent struct {
			Nation       string `json:"nation"`
			Province     string `json:"province"`
			City         string `json:"city"`
			District     string `json:"district"`
			Street       string `json:"street"`
			StreetNumber string `json:"street_number"`
		} `json:"address_component"`
	} `json:"result"`
}

func (t *TencentGeocodeResponse) ToRegeocodeResult() RegeocodeResult {
	addr := t.Result.Address
	if t.Result.FormattedAddr.Recommend != "" {
		addr = t.Result.FormattedAddr.Recommend
	}
	return RegeocodeResult{
		FormattedAddress: addr,
		AddressComponent: AddressComponent{
			Nation:       t.Result.AddressComponent.Nation,
			Province:     t.Result.AddressComponent.Province,
			City:         t.Result.AddressComponent.City,
			District:     t.Result.AddressComponent.District,
			Street:       t.Result.AddressComponent.Street,
			StreetNumber: t.Result.AddressComponent.StreetNumber,
			Address:      addr,
		},
	}
}

// GetGeoViaTDT performs reverse geocoding using Tianditu API with key rotation.
func GetGeoViaTDT(ctx context.Context, lat, lng float64) (RegeocodeResult, error) {
	var lastErr error
	attempts := 0
	maxAttempts := len(tdtKeyManager.keys)

	for attempts < maxAttempts {
		key, ok := tdtKeyManager.GetNextKey()
		if !ok {
			return RegeocodeResult{}, ErrAllKeysExhausted
		}

		postStr := fmt.Sprintf("{'lon':%f,'lat':%f,'ver':1}", lng, lat)
		url := fmt.Sprintf("https://api.tianditu.gov.cn/geocoder?postStr=%s&type=geocode&tk=%s", postStr, key)
		zap.L().Info("Tianditu API request URL:", zap.String("url", url))

		client := resty.New()
		var result TDTGeocodeResponse

		resp, err := client.R().
			SetContext(ctx).
			SetHeader("Accept", "application/json").
			SetResult(&result).
			Get(url)
		if err != nil {
			lastErr = fmt.Errorf("request failed: %w", err)
			attempts++
			continue
		}

		// Check if response indicates quota exceeded
		if resp.StatusCode() == 403 || (result.Status != "0" && result.Msg != "") {
			// Mark this key as failed and try next
			tdtKeyManager.MarkFailed(key)
			lastErr = fmt.Errorf("API key quota exceeded: %s", result.Msg)
			attempts++
			continue
		}

		// Success
		if result.Status == "0" || result.Result.Formatted != "" {
			return result.ToRegeocodeResult(), nil
		}

		lastErr = fmt.Errorf("invalid response: %s", result.Msg)
		attempts++
	}

	if lastErr != nil {
		return RegeocodeResult{}, lastErr
	}
	return RegeocodeResult{}, ErrAllKeysExhausted
}

// GetGeoViaTencent performs reverse geocoding using Tencent Maps API with key rotation.
func GetGeoViaTencent(ctx context.Context, lat, lng float64) (RegeocodeResult, error) {
	if len(tencentKeyManager.keys) == 0 {
		return RegeocodeResult{}, errors.New("no Tencent Maps API keys configured")
	}

	var lastErr error
	attempts := 0
	maxAttempts := len(tencentKeyManager.keys)

	for attempts < maxAttempts {
		key, ok := tencentKeyManager.GetNextKey()
		if !ok {
			return RegeocodeResult{}, ErrAllKeysExhausted
		}

		url := fmt.Sprintf("https://apis.map.qq.com/ws/geocoder/v1/?location=%f,%f&key=%s", lat, lng, key)
		zap.L().Info("Tencent Maps API request URL:", zap.String("url", url))

		client := resty.New()
		var result TencentGeocodeResponse

		resp, err := client.R().
			SetContext(ctx).
			SetHeader("Accept", "application/json").
			SetResult(&result).
			Get(url)
		if err != nil {
			lastErr = fmt.Errorf("request failed: %w", err)
			attempts++
			continue
		}

		// Check if response indicates quota exceeded (status 121: key quota exceeded)
		if resp.StatusCode() == 403 || result.Status == 121 {
			// Mark this key as failed and try next
			tencentKeyManager.MarkFailed(key)
			lastErr = fmt.Errorf("API key quota exceeded: %s", result.Message)
			attempts++
			continue
		}

		// Success
		if result.Status == 0 {
			return result.ToRegeocodeResult(), nil
		}

		lastErr = fmt.Errorf("API error: %s", result.Message)
		attempts++
	}

	if lastErr != nil {
		return RegeocodeResult{}, lastErr
	}
	return RegeocodeResult{}, ErrAllKeysExhausted
}
