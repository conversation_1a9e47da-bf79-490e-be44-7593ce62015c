# GEO Service

地理位置服务模块，提供经纬度逆向地理编码功能。

## 目标

需要调用天地图或腾讯地图根据经纬度逆向获取地理位置，由于天地图或腾讯地图对免费用户限制了每日调用次数，所以需要多个key轮询，并且自动重试下一个key

## 功能特性

- **逆向地理编码**: 根据经纬度获取详细地理位置信息
- **多地图服务支持**: 支持天地图和腾讯地图
- **API Key 轮询**: 多个 API Key 自动轮询，避免单个 Key 超过调用限制
- **自动重试机制**: 当某个 Key 达到限制时，自动切换到下一个可用 Key
- **负载均衡**: 在多个 Key 之间均匀分配请求

## 配置说明

### 天地图配置
```yaml
tianditu:
  keys:
    - key1
    - key2
    - key3
  daily_limit: 10000  # 每个 key 的日调用限制
```

### 腾讯地图配置
```yaml
tencent_map:
  keys:
    - key1
    - key2
    - key3
  daily_limit: 10000  # 每个 key 的日调用限制
```

## 使用示例

```go
// 初始化 GEO 服务
geoService := geo.NewService(config)

// 逆向地理编码
location, err := geoService.ReverseGeocode(latitude, longitude)
if err != nil {
    // 处理错误
}

// location 包含：
// - Province: 省份
// - City: 城市
// - District: 区县
// - Street: 街道
// - Address: 详细地址
```

## 测试用例
```
go test -v ./cmd/app -run TestHandleReverseGeocode_JSONBody

for i in {1..10}; do echo "Run $i:"; go test ./cmd/app -run TestHandleReverseGeocode_JSONBody -v 2>&1 | grep "Generated coordinates"; sleep 1; done


for i in {1..100}; do echo "Run $i:"; go test ./cmd/app -run TestHandleReverseGeocode_JSONBody -v -count=1; done;
```

## 重试策略

1. **请求失败时的处理**:
   - 如果当前 Key 返回限额错误，自动切换到下一个 Key
   - 记录每个 Key 的使用次数和最后使用时间
   - 优先使用调用次数最少的 Key

2. **Key 状态管理**:
   - 每日凌晨重置所有 Key 的调用计数
   - 实时监控每个 Key 的可用状态
   - 当所有 Key 都达到限制时，返回服务不可用错误

## 错误处理

- `ErrAllKeysExhausted`: 所有 API Key 都已达到调用限制
- `ErrInvalidCoordinates`: 无效的经纬度坐标
- `ErrServiceUnavailable`: 地图服务暂时不可用




 Implemented Features:

  1. Key Rotation Mechanism:
    - KeyManager struct with atomic round-robin counter
    - Thread-safe key selection and failure tracking
    - Automatic skip of failed keys
  2. Tianditu (天地图) Support:
    - GetGeoViaTDT function with proper response handling
    - Automatic retry with next key on quota exceeded (HTTP 403)
    - Proper error detection and key marking
  3. Tencent Maps (腾讯地图) Support:
    - GetGeoViaTencent function with key rotation
    - Status code 121 detection for quota exceeded
    - Automatic fallback to next available key
  4. Main GetGeo Function:
    - Coordinate validation (-90 to 90 for latitude, -180 to 180 for longitude)
    - Automatic fallback from Tianditu to Tencent Maps
    - Context support for cancellation
  5. Error Handling:
    - ErrAllKeysExhausted: When all keys reach their limit
    - ErrInvalidCoordinates: For invalid lat/lng values
    - ErrServiceUnavailable: When both services fail
    - Per-key failure tracking with automatic retry
