package util

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"text/template"

	"go.uber.org/zap"

	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
)

func UpdateItem(model string, payload io.Reader) error {
	log := logger.Get("util")

	rdb := redis.GetClient()

	cloudbaseAccessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
	if err != nil {
		return err
	}

	log.Debug("get cloudbase access_token from redis:", zap.String("cloudbaseAccessToken", cloudbaseAccessToken))

	url := fmt.Sprintf("https://cloud1-0gpy573m8caa7db3.api.tcloudbasegateway.com/v1/model/prod/%s/update", model)
	method := "PUT"

	log.Debug("request", zap.String("url", url), zap.String("method", method))

	// payload = strings.NewReader(`{
	//   "filter": {
	//     "where": {
	//       "_id": {
	//         "$eq": "BGKPU87ATA"
	//       }
	//     }
	//   },
	//   "data": {
	//     "cloudbase_access_token": "92_MzXbAMv05SogOZj7MTkuQ4jFvaFjHDRfpGn8XcQUIDJrsZDmadTA7cpGzOy8hn4MjB80KniUJ0hLsXWHFyY2VFCrcds0m9Z-2AuAhGpMv5isyv8hd6125PYgt00IBFfAGAQJN"
	//   }
	// }`)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Error("Failed to create new request", zap.Error(err))
		return err
	}

	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cloudbaseAccessToken))
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Error("Failed to client.Do", zap.Error(err))
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("Failed to io.ReadAll", zap.Error(err))
		return err
	}

	log.Debug("UpdateItem response", zap.String("body", string(body)))

	return nil
}

func UpdateWechatAccessToken() error {
	rdb := redis.GetClient()

	accessToken, err := rdb.Get(context.Background(), "wechat_access_token").Result()
	if err != nil {
		return err
	}

	// Define your template
	tmpl := `{
		"filter": {
			"where": {
				"_id": {
					"$eq": "{{.ID}}"
				}
			}
		},
		"data": {
			"wechat_access_token": "{{.WechatAccessToken}}"
		}
	}`
	// Data to inject into the template
	data := struct {
		ID                string
		WechatAccessToken string
	}{
		ID:                "BGKPU87ATA",
		WechatAccessToken: accessToken, // Replace with your actual value
	}

	// Create and execute the template
	t := template.Must(template.New("payload").Parse(tmpl))

	var buf bytes.Buffer

	err = t.Execute(&buf, data)
	if err != nil {
		panic(err)
	}
	// fmt.Println("payload:", buf.String())

	// Get the final payload
	payload := strings.NewReader(buf.String())
	// fmt.Println(payload)

	UpdateItem("cache", payload)

	return nil
}

func UpdateWecomAccessToken() error {
	rdb := redis.GetClient()

	accessToken, err := rdb.Get(context.Background(), "wecom_access_token").Result()
	if err != nil {
		return err
	}

	// Define your template
	tmpl := `{
		"filter": {
			"where": {
				"_id": {
					"$eq": "{{.ID}}"
				}
			}
		},
		"data": {
			"wecom_access_token": "{{.WecomAccessToken}}"
		}
	}`
	// Data to inject into the template
	data := struct {
		ID               string
		WecomAccessToken string
	}{
		ID:               "BGKPU87ATA",
		WecomAccessToken: accessToken, // Replace with your actual value
	}

	// Create and execute the template
	t := template.Must(template.New("payload").Parse(tmpl))

	var buf bytes.Buffer

	err = t.Execute(&buf, data)
	if err != nil {
		panic(err)
	}
	// fmt.Println("payload:", buf.String())

	// Get the final payload
	payload := strings.NewReader(buf.String())
	// fmt.Println(payload)

	UpdateItem("cache", payload)

	return nil
}

func UpdateCloudbaseAccessToken() error {
	rdb := redis.GetClient()

	accessToken, err := rdb.Get(context.Background(), "cloudbase_access_token").Result()
	if err != nil {
		return err
	}

	// Define your template
	tmpl := `{
		"filter": {
			"where": {
				"_id": {
					"$eq": "{{.ID}}"
				}
			}
		},
		"data": {
			"cloudbase_access_token": "{{.CloudbaseAccessToken}}"
		}
	}`
	// Data to inject into the template
	data := struct {
		ID                   string
		CloudbaseAccessToken string
	}{
		ID:                   "BGKPU87ATA",
		CloudbaseAccessToken: accessToken, // Replace with your actual value
	}

	// Create and execute the template
	t := template.Must(template.New("payload").Parse(tmpl))

	var buf bytes.Buffer

	err = t.Execute(&buf, data)
	if err != nil {
		panic(err)
	}
	// fmt.Println("payload:", buf.String())

	// Get the final payload
	payload := strings.NewReader(buf.String())

	UpdateItem("cache", payload)

	return nil
}
