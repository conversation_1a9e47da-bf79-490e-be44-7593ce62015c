package util

import (
	"testing"
	"time"
)

func TestIsOrderCreatedToday(t *testing.T) {
	tests := []struct {
		name      string
		createdAt int64
		want      bool
	}{
		{
			name:      "Today in seconds",
			createdAt: time.Now().Unix(),
			want:      true,
		},
		{
			name:      "Today in milliseconds",
			createdAt: time.Now().UnixMilli(),
			want:      true,
		},
		{
			name:      "Yesterday",
			createdAt: time.Now().AddDate(0, 0, -1).Unix(),
			want:      false,
		},
		{
			name:      "Tomorrow",
			createdAt: time.Now().AddDate(0, 0, 1).Unix(),
			want:      false,
		},
		{
			name:      "Zero timestamp",
			createdAt: 0,
			want:      false,
		},
		{
			name:      "A week ago",
			createdAt: time.Now().AddDate(0, 0, -7).Unix(),
			want:      false,
		},
		{
			name:      "13 digits timestamp",
			createdAt: 1754754791193,
			want:      true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsOrderCreatedToday(tt.createdAt); got != tt.want {
				t.Errorf("IsOrderCreatedToday() = %v, want %v", got, tt.want)
			}
		})
	}
}
