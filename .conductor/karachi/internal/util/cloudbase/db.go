package cloudbase

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/dfang/go-prettyjson"
	"resty.dev/v3"

	"tandian-server/internal/config"
	"tandian-server/internal/redis"
)

// FindResponse represents the response from CloudBase get-item API
// See: https://docs.cloudbase.net/http-api/model/get-item
type FindResponse struct {
	Data struct {
		Record map[string]interface{} `json:"record"`
	} `json:"data"`
	RequestID string `json:"requestId"`
}

// ErrorResponse represents an error response from CloudBase API.
type ErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	ReuqestID string `json:"requestId"`
}

// CreateResponse represents the response from CloudBase create-item API.
type CreateResponse struct {
	Data struct {
		ID string `json:"id"`
	} `json:"data"`
	RequestID string `json:"requestId"`
}

// UpdateDeleteResponse represents the response from CloudBase update-item, delete-item API.
type UpdateDeleteResponse struct {
	Data struct {
		Count int `json:"count"`
	} `json:"data"`
	RequestID string `json:"requestId"`
}

// UpdateDeleteManyResponse represents the response from CloudBase update-items, delete-items API.
type UpdateDeleteManyResponse struct {
	Data struct {
		Count int `json:"count"`
		// legalIdList
		// illegalIdList
	} `json:"data"`
	RequestID string `json:"requestId"`
}

// cloudbaseAPIRequest performs a POST request to the CloudBase HTTP API.
func cloudbaseAPIRequest(
	ctx context.Context,
	http_method, model, action string,
	payload interface{},
) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s%s/%s/%s", cloudbaseAPIHost, cloudbaseDbPath, model, action)

	rdb := redis.GetClient()

	authToken, err := rdb.Get(ctx, "cloudbase_access_token").Result()
	if err != nil {
		return nil, err
	}
	// zap.ReplaceGlobals(zap.Must(zap.NewDevelopment()))
	// zap.L().Info("Making CloudBase API request", zap.String("url", url), zap.Any("payload", payload))

	fmt.Println(url)

	client := resty.New()

	var result map[string]interface{}

	switch http_method {
	case "POST":
		resp, err := client.R().
			SetContext(ctx).
			SetHeader("Accept", "application/json").
			SetAuthToken(authToken). // This should come from a config or a token manager
			SetBody(payload).
			SetResult(&result).
			// SetDebug(true).
			Post(url)
		if err != nil {
			return nil, fmt.Errorf("error making request to %s: %w", url, err)
		}

		if resp.IsError() {
			var errResp ErrorResponse
			if err := json.Unmarshal([]byte(resp.String()), &errResp); err == nil && errResp.Message != "" {
				log.Printf(
					"Error response from %s: %s\nCode: %s, Message: %s",
					url,
					resp.Status(),
					errResp.Code,
					errResp.Message,
				)
				return nil, fmt.Errorf("cloudbase error [%s]: %s", errResp.Code, errResp.Message)
			}

			log.Printf("Error response from %s: %s\nBody: %s", url, resp.Status(), resp.String())

			return nil, fmt.Errorf("error response from cloudbase: %s", resp.Status())
		}

		return result, nil

	case "PUT":
		resp, err := client.R().
			SetContext(ctx).
			SetHeader("Accept", "application/json").
			SetAuthToken(authToken). // This should come from a config or a token manager
			SetBody(payload).
			SetResult(&result).
			// SetDebug(true).
			Put(url)
		if err != nil {
			return nil, fmt.Errorf("error making request to %s: %w", url, err)
		}

		if resp.IsError() {
			var errResp ErrorResponse
			if err := json.Unmarshal([]byte(resp.String()), &errResp); err == nil && errResp.Message != "" {
				log.Printf(
					"Error response from %s: %s\nCode: %s, Message: %s",
					url,
					resp.Status(),
					errResp.Code,
					errResp.Message,
				)
				return nil, fmt.Errorf("cloudbase error [%s]: %s", errResp.Code, errResp.Message)
			}

			log.Printf("Error response from %s: %s\nBody: %s", url, resp.Status(), resp.String())

			return nil, fmt.Errorf("error response from cloudbase: %s", resp.Status())
		}

		return result, nil
	case "DELETE":
		resp, err := client.R().
			SetContext(ctx).
			SetHeader("Accept", "application/json").
			SetAuthToken(authToken). // This should come from a config or a token manager
			SetBody(payload).
			SetResult(&result).
			// SetDebug(true).
			Delete(url)
		if err != nil {
			return nil, fmt.Errorf("error making request to %s: %w", url, err)
		}

		if resp.IsError() {
			var errResp ErrorResponse
			if err := json.Unmarshal([]byte(resp.String()), &errResp); err == nil && errResp.Message != "" {
				log.Printf(
					"Error response from %s: %s\nCode: %s, Message: %s",
					url,
					resp.Status(),
					errResp.Code,
					errResp.Message,
				)
				return nil, fmt.Errorf("cloudbase error [%s]: %s", errResp.Code, errResp.Message)
			}

			log.Printf("Error response from %s: %s\nBody: %s", url, resp.Status(), resp.String())

			return nil, fmt.Errorf("error response from cloudbase: %s", resp.Status())
		}

		return result, nil
	}

	return nil, fmt.Errorf("invalid http method: %s", http_method)
}

// GetItem retrieves items from a CloudBase model.
// See: https://docs.cloudbase.net/http-api/model/get-item
// payload example:
//
//	payload := map[string]interface{}{
//		"filter": map[string]interface{}{
//			"where": map[string]interface{}{
//				"_id": map[string]interface{}{
//					"$eq": "foobar",
//				},
//			},
//		},
//		"select": map[string]interface{}{
//			"_id":   true,
//			"owner": true,
//		},
//	}
//
// Returns the record directly from the CloudBase response.
func GetItem(ctx context.Context, model string, payload interface{}) (map[string]interface{}, error) {
	resp, err := cloudbaseAPIRequest(ctx, "POST", model, "get", payload)
	if err != nil {
		return nil, err
	}

	// Parse response into FindResponse
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var findResp FindResponse
	if err := json.Unmarshal(respBytes, &findResp); err != nil {
		// Fallback to manual extraction if unmarshal fails
		if data, ok := resp["data"].(map[string]interface{}); ok {
			if record, ok := data["record"].(map[string]interface{}); ok {
				return record, nil
			}
		}

		return map[string]interface{}{}, nil
	}

	// Return the record from typed response
	if findResp.Data.Record != nil {
		return findResp.Data.Record, nil
	}

	return map[string]interface{}{}, nil
}

// CreateItem creates an item in a CloudBase model.
// See: https://docs.cloudbase.net/http-api/model/create-item
// payload example:
//
//	payload := map[string]interface{}{
//		"data": map[string]interface{}{
//			"foo": "bar",
//		},
//	}
//
// Returns response with "id" field extracted.
func CreateItem(ctx context.Context, model string, payload interface{}) (map[string]interface{}, error) {
	if config.AppConfig.AppEnv == "development" {
		s, _ := prettyjson.Marshal(payload)
		fmt.Println("CreateItem payload:", string(s))
	}

	resp, err := cloudbaseAPIRequest(ctx, "POST", model, "create", payload)
	if err != nil {
		return nil, err
	}

	// Parse response into CreateResponse
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var createResp CreateResponse
	if err := json.Unmarshal(respBytes, &createResp); err != nil {
		// Fallback to manual extraction if unmarshal fails
		result := make(map[string]interface{})

		if data, ok := resp["data"].(map[string]interface{}); ok {
			if id, ok := data["id"].(string); ok {
				result["id"] = id
			}
		}

		result["requestId"] = resp["requestId"]

		return result, nil
	}

	// Return structured response
	result := make(map[string]interface{})
	result["id"] = createResp.Data.ID
	result["requestId"] = createResp.RequestID

	return result, nil
}

// UpdateItem update item from a CloudBase model.
// See: https://docs.cloudbase.net/http-api/model/update-item
// payload example:
//
//	payload := map[string]interface{}{
//		"filter": map[string]interface{}{
//			"where": map[string]interface{}{
//				"_id": map[string]interface{}{
//					"$eq": "foobar",
//				},
//			},
//		},
//		"data": map[string]interface{}{
//			"foo": "bar",
//		},
//	}
//
// Returns response with "count" field indicating number of updated items.
func UpdateItem(ctx context.Context, model string, payload interface{}) (map[string]interface{}, error) {
	if config.AppConfig.AppEnv == "development" {
		s, _ := prettyjson.Marshal(payload)
		fmt.Println("UpdateItem payload:", string(s))
	}

	resp, err := cloudbaseAPIRequest(ctx, "PUT", model, "update", payload)
	if err != nil {
		return nil, err
	}

	// Parse response into UpdateDeleteResponse
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var updateResp UpdateDeleteResponse
	if err := json.Unmarshal(respBytes, &updateResp); err != nil {
		// Fallback to manual extraction if unmarshal fails
		result := make(map[string]interface{})

		if data, ok := resp["data"].(map[string]interface{}); ok {
			if count, ok := data["count"].(float64); ok {
				result["count"] = int(count)
			}
		}

		result["requestId"] = resp["requestId"]

		return result, nil
	}

	// Return structured response
	result := make(map[string]interface{})
	result["count"] = updateResp.Data.Count
	result["requestId"] = updateResp.RequestID

	return result, nil
}

// DeleteItem delete item from a CloudBase model.
// See: https://docs.cloudbase.net/http-api/model/delete-item
// payload example:
// Returns response with "count" field indicating number of deleted items.
func DeleteItem(ctx context.Context, model string, payload interface{}) (map[string]interface{}, error) {
	if config.AppConfig.AppEnv == "development" {
		s, _ := prettyjson.Marshal(payload)
		fmt.Println("DeleteItem payload:", string(s))
	}

	resp, err := cloudbaseAPIRequest(ctx, "DELETE", model, "delete", payload)
	if err != nil {
		return nil, err
	}

	// Parse response into UpdateDeleteResponse
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var deleteResp UpdateDeleteResponse
	if err := json.Unmarshal(respBytes, &deleteResp); err != nil {
		// Fallback to manual extraction if unmarshal fails
		result := make(map[string]interface{})

		if data, ok := resp["data"].(map[string]interface{}); ok {
			if count, ok := data["count"].(float64); ok {
				result["count"] = int(count)
			}
		}

		result["requestId"] = resp["requestId"]

		return result, nil
	}

	// Return structured response
	result := make(map[string]interface{})
	result["count"] = deleteResp.Data.Count
	result["requestId"] = deleteResp.RequestID

	return result, nil
}

// GetItems retrieves multiple items from a CloudBase model.
// See: https://docs.cloudbase.net/http-api/model/get-records
// payload example:
//
//	payload := map[string]interface{}{
//		"filter": map[string]interface{}{
//			"where": map[string]interface{}{
//				"status": map[string]interface{}{
//					"$eq": "active",
//				},
//			},
//		},
//		"select": map[string]interface{}{
//			"_id":   true,
//			"name": true,
//		},
//		"limit": 100,
//		"offset": 0,
//		"orderBy": []interface{}{
//			map[string]interface{}{
//				"field": "createdAt",
//				"direction": "desc",
//			},
//		},
//	}
//
// Returns response with "records" array and pagination info.
func GetItems(ctx context.Context, model string, payload interface{}) (map[string]interface{}, error) {
	resp, err := cloudbaseAPIRequest(ctx, "POST", model, "list", payload)
	if err != nil {
		return nil, err
	}

	// The response should contain data.records array
	result := make(map[string]interface{})

	if data, ok := resp["data"].(map[string]interface{}); ok {
		if records, ok := data["records"]; ok {
			result["records"] = records
		} else {
			result["records"] = []interface{}{}
		}
		// Include pagination info if available
		if total, ok := data["total"]; ok {
			result["total"] = total
		}

		if limit, ok := data["limit"]; ok {
			result["limit"] = limit
		}

		if offset, ok := data["offset"]; ok {
			result["offset"] = offset
		}
	} else {
		result["records"] = []interface{}{}
	}

	result["requestId"] = resp["requestId"]

	return result, nil
}

// UpdateItems update items from a CloudBase model.
// See: https://docs.cloudbase.net/http-api/model/update-items
// payload example:
//
//	payload := map[string]interface{}{
//		"filter": map[string]interface{}{
//			"where": map[string]interface{}{
//				"_id": map[string]interface{}{
//					"$eq": "foobar",
//				},
//			},
//		},
//		"data": map[string]interface{}{
//			"foo": "bar",
//		},
//	}
//
// Returns response with "count" field indicating number of updated items.
func UpdateItems(ctx context.Context, model string, payload interface{}) (map[string]interface{}, error) {
	if config.AppConfig.AppEnv == "development" {
		s, _ := prettyjson.Marshal(payload)
		fmt.Println("UpdateItems payload:", string(s))
	}

	resp, err := cloudbaseAPIRequest(ctx, "PUT", model, "updateMany", payload)
	if err != nil {
		return nil, err
	}

	// Parse response into UpdateDeleteManyResponse
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var updateManyResp UpdateDeleteManyResponse
	if err := json.Unmarshal(respBytes, &updateManyResp); err != nil {
		// Fallback to manual extraction if unmarshal fails
		result := make(map[string]interface{})

		if data, ok := resp["data"].(map[string]interface{}); ok {
			if count, ok := data["count"].(float64); ok {
				result["count"] = int(count)
			}
			// Include legal/illegal lists if present
			if legalList, ok := data["legalIdList"]; ok {
				result["legalIdList"] = legalList
			}

			if illegalList, ok := data["illegalIdList"]; ok {
				result["illegalIdList"] = illegalList
			}
		}

		result["requestId"] = resp["requestId"]

		return result, nil
	}

	// Return structured response
	result := make(map[string]interface{})
	result["count"] = updateManyResp.Data.Count
	result["requestId"] = updateManyResp.RequestID
	// Note: legalIdList and illegalIdList are not defined in the struct yet
	// Would need to update UpdateDeleteManyResponse to include these fields

	return result, nil
}

// Helper functions for easier usage

// Count returns the count of documents matching the filter.
func Count(ctx context.Context, model string, filter map[string]interface{}) (float64, error) {
	payload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": filter,
		},
	}

	resp, err := GetItems(ctx, model, payload)
	if err != nil {
		return 0, err
	}

	if total, ok := resp["total"].(float64); ok {
		return total, nil
	}

	// If total is not provided, count the records array
	if records, ok := resp["records"].([]interface{}); ok {
		return float64(len(records)), nil
	}

	return 0, nil
}

// FindOne returns the first document matching the filter.
func FindOne(ctx context.Context, model string, filter map[string]interface{}) (map[string]interface{}, error) {
	payload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": filter,
		},
	}
	prettyjson.Printf("FindOne payload: %s\n", payload)
	return GetItem(ctx, model, payload)
}

// FindById returns a document by its ID.
func FindById(ctx context.Context, model, id string) (map[string]interface{}, error) {
	filter := map[string]interface{}{
		"_id": map[string]interface{}{
			"$eq": id,
		},
	}

	return FindOne(ctx, model, filter)
}

// Find returns multiple documents matching the filter with pagination.
func Find(
	ctx context.Context,
	model string,
	filter map[string]interface{},
	offset, limit int,
) ([]map[string]interface{}, error) {
	payload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": filter,
		},
		"limit":  limit,
		"offset": offset,
		"orderBy": []interface{}{
			map[string]interface{}{
				"field":     "created_at",
				"direction": "desc",
			},
		},
	}

	resp, err := GetItems(ctx, model, payload)
	if err != nil {
		return nil, err
	}

	if records, ok := resp["records"].([]interface{}); ok {
		result := make([]map[string]interface{}, len(records))
		for i, record := range records {
			if r, ok := record.(map[string]interface{}); ok {
				result[i] = r
			}
		}
		return result, nil
	}

	return []map[string]interface{}{}, nil
}

// Create creates a new document.
func Create(ctx context.Context, model string, data map[string]interface{}) (map[string]interface{}, error) {
	payload := map[string]interface{}{
		"data": data,
	}

	resp, err := CreateItem(ctx, model, payload)
	if err != nil {
		return nil, err
	}

	// Return a map with the created document ID
	if id, ok := resp["id"].(string); ok {
		return map[string]interface{}{
			"_id": id,
		}, nil
	}

	return resp, nil
}

// UpdateById updates a document by its ID.
func UpdateById(ctx context.Context, model, id string, data map[string]interface{}) (map[string]interface{}, error) {
	payload := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": id,
				},
			},
		},
		"data": data,
	}

	return UpdateItem(ctx, model, payload)
}
