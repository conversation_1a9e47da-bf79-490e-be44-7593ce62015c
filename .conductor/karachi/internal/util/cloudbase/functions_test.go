package cloudbase

import (
	"context"
	"fmt"
	"reflect"
	"testing"
)

func TestCallFunction(t *testing.T) {
	type args struct {
		ctx      context.Context
		function interface{}
		payload  interface{}
	}

	tests := []struct {
		name    string
		args    args
		want    map[string]interface{}
		wantErr bool
	}{
		{
			name: "echo",
			args: args{
				ctx:      t.Context(),
				function: "echo",
				payload:  map[string]interface{}{"hello": "world"},
			},
			want: map[string]interface{}{
				"code":    200,
				"message": "ok",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CallFunction(tt.args.ctx, tt.args.function, tt.args.payload)
			if (err != nil) != tt.wantErr {
				t.<PERSON>rf("CallFunction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			fmt.Println("got", got)
			fmt.Println("want", tt.want)

			if !reflect.DeepEqual(got["code"], tt.want["code"]) {
				t.<PERSON><PERSON>("CallFunction() = %v, want %v", got, tt.want)
			}
		})
	}
}
