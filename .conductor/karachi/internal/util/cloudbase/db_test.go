package cloudbase

import (
	"context"
	"reflect"
	"testing"
)

func TestGetItem(t *testing.T) {
	type args struct {
		ctx     context.Context
		model   string
		payload interface{}
	}

	tests := []struct {
		name    string
		args    args
		want    map[string]interface{}
		wantErr bool
	}{
		{
			name: "successful get item",
			args: args{
				ctx:   t.Context(),
				model: "users",
				payload: map[string]interface{}{
					"filter": map[string]interface{}{
						"where": map[string]interface{}{
							"_id": map[string]interface{}{
								"$eq": "B67HPFE4TS",
							},
						},
					},
					"select": map[string]interface{}{
						"_id": true,
					},
				},
			},
			want: map[string]interface{}{
				"data": map[string]interface{}{
					"record": map[string]interface{}{
						"_id": "B67HPFE4TS",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetItem(tt.args.ctx, tt.args.model, tt.args.payload)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetItem() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got["data"], tt.want["data"]) {
				t.Errorf("GetItem() = %v, want %v", got, tt.want)
			}
		})
	}
}
