package cloudbase

import (
	"context"
	"fmt"
	"log"

	"go.uber.org/zap"
	"resty.dev/v3"

	"tandian-server/internal/redis"
)

func CallFunction(ctx context.Context, function, payload interface{}) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s%s/%s", cloudbaseAPIHost, cloudbaseFunctionPath, function)

	zap.L().Info("Calling Cloudbase function", zap.String("url", url),
		zap.Any("payload", payload))

	rdb := redis.GetClient()

	authToken, err := rdb.Get(ctx, "cloudbase_access_token").Result()
	if err != nil {
		zap.L().Error("Failed to get Cloudbase access token", zap.Error(err))
		return nil, err
	}

	client := resty.New().SetAuthScheme("Bearer")

	var result map[string]interface{}

	resp, err := client.R().
		SetContext(ctx).
		SetHeader("Accept", "application/json").
		SetAuthToken(authToken). // This should come from a config or a token manager
		SetBody(payload).
		SetResult(&result).
		EnableDebug().
		Post(url)
	if err != nil {
		return nil, fmt.Errorf("error making request to %s: %w", url, err)
	}

	if resp.IsError() {
		log.Printf("Error response from %s: %s\nBody: %s", url, resp.Status(), resp.String())
		return nil, fmt.Errorf("error response from cloudbase: %s", resp.Status())
	}

	return result, nil
}
