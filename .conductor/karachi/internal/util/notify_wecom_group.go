package util

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"

	"tandian-server/internal/redis"
)

// appchat/send 只能往企业内部群发送消息， 要往外部群推送消息，需要用到机器人webhook.
func SendMsgToWeComGroup(chatID string) error {
	rdb := redis.GetClient()

	accessToken, err := rdb.Get(context.Background(), "wecom_access_token").Result()
	if err != nil {
		fmt.Println(err)
		return err
	}

	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/appchat/send?access_token=%s&debug=1", accessToken)
	method := "POST"

	payload := strings.NewReader(`{
		"chatid": "wroDFqVQAApp6IyyiXa9abba8_8y0CaQ",
		"msgtype": "text",
		"text": {"content": "测试"}
		}`)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		fmt.Println(err)
		return err
	}

	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return err
	}

	fmt.Println(string(body))

	return nil
}
