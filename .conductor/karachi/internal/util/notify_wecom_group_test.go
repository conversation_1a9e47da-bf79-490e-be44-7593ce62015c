package util

import (
	"testing"
)

func TestSendMsgToWeComGroup(t *testing.T) {
	type args struct {
		chatID string
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Test with valid chat ID", args: args{chatID: "your_test_chat_id"}, wantErr: false,
		}, // Replace "your_test_chat_id" with a valid chat ID for testing
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SendMsgToWeComGroup(tt.args.chatID); (err != nil) != tt.wantErr {
				t.Errorf("SendMsgToWeComGroup() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
