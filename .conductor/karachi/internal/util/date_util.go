package util

import "time"

// IsOrderCreatedToday checks if the given createdAt timestamp represents today's date.
func IsOrderCreatedToday(createdAt int64) bool {
	if createdAt == 0 {
		return false
	}

	// Parse the createdAt timestamp (already an int64)
	var orderTime time.Time

	// Handle both seconds and milliseconds timestamps
	if createdAt > 1e12 { // If timestamp is in milliseconds
		orderTime = time.Unix(createdAt/1000, (createdAt%1000)*1e6)
	} else { // If timestamp is in seconds
		orderTime = time.Unix(createdAt, 0)
	}

	// Get today's date in the same timezone as the order
	orderDate := orderTime.Format("2006-01-02")
	todayDate := time.Now().Format("2006-01-02")

	return orderDate == todayDate
}
