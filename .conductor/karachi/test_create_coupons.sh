#!/bin/bash

# Test create coupons API endpoint

# Get current time and time 30 days from now in RFC3339 format
BEGIN_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
END_TIME=$(date -u -v+30d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d "+30 days" +"%Y-%m-%dT%H:%M:%SZ")

# Generate unique request number with timestamp
OUT_REQUEST_NO="TEST_$(date +%Y%m%d%H%M%S)"

echo "Testing create coupons API..."
echo "Begin time: $BEGIN_TIME"
echo "End time: $END_TIME"
echo "Out request no: $OUT_REQUEST_NO"
echo ""

curl -X POST http://localhost:8000/api/v1/create_coupons \
  -H "Content-Type: application/json" \
  -d "{
    \"stock_name\": \"测试优惠券批次\",
    \"belong_merchant\": \"1234567890\",
    \"comment\": \"测试备注\",
    \"goods_name\": \"全场商品\",
    \"available_begin_time\": \"$BEGIN_TIME\",
    \"available_end_time\": \"$END_TIME\",
    \"max_coupons\": 100000,
    \"max_coupons_per_user\": 5,
    \"coupon_amount\": 500,
    \"transaction_minimum\": 1000,
    \"out_request_no\": \"$OUT_REQUEST_NO\"
  }" | jq .