# EXAMPLE USAGE:
#
#   Refer for explanation to following link:
#   https://lefthook.dev/configuration/
#
# pre-push:
#   jobs:
#     - name: packages audit
#       tags:
#         - frontend
#         - security
#       run: yarn audit
#
#     - name: gems audit
#       tags:
#         - backend
#         - security
#       run: bundle audit
#
pre-commit:
  parallel: true
  jobs:
    - name: golangci-lint
      files: git diff --cached --name-only --diff-filter=ACM
      glob: "*.go"
      run: |
        files="{files}"
        if [ -n "$files" ]; then
          # Get unique directories containing the changed files
          dirs=$(echo "$files" | xargs dirname | sort -u)
          # Run golangci-lint on each directory (not individual files)
          for dir in $dirs; do
            golangci-lint run --fix "$dir/..."
          done
        fi
    - name: govet
      files: git diff --cached --name-only --diff-filter=ACM
      glob: "*.go"
      run: |
        files="{files}"
        if [ -n "$files" ]; then
          # Get unique directories containing the changed files
          dirs=$(echo "$files" | xargs dirname | sort -u)
          # Run go vet on each directory package (not individual files)
          for dir in $dirs; do
            go vet "./$dir/..."
          done
        fi
