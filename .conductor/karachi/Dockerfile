# STAGE 1: The builder stage
# This stage builds the Go binaries.
FROM golang:1.24.3-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

ENV GOPROXY=https://goproxy.cn,direct

# Copy go.mod and go.sum to leverage Docker cache
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the rest of the source code
COPY . .

# Build the server binary
# CGO_ENABLED=0 and GOOS=linux are best practices for creating static binaries for Linux containers
RUN CGO_ENABLED=0 GOOS=linux go build -o /server ./cmd/server/*.go

# Build the client binary
RUN CGO_ENABLED=0 GOOS=linux go build -o /client ./cmd/client/*.go

# Build the app binary
RUN CGO_ENABLED=0 GOOS=linux go build -o /app ./cmd/app/*.go

# Build the cli
RUN CGO_ENABLED=0 GOOS=linux go build -o /cli ./cmd/cli/*.go


# STAGE 2: The server-image stage
# This stage creates the final, minimal server image.
FROM alpine:latest AS server-image

# Install tzdata package for timezone support
RUN apk --no-cache add tzdata

ENV TZ="Asia/Shanghai"

# Copy the server binary from the builder stage
COPY --from=builder /server /server
# Copy the client binary from the builder stage
COPY --from=builder /client /client
# Copy the app binary from the builder stage
COPY --from=builder /app /app
# Copy the cli binary from the builder stage
COPY --from=builder /app /app
# Copy the cli binary from the builder stage
COPY --from=builder /cli /cli

# Expose port 8080 (if your server uses it, otherwise change or remove)
EXPOSE 8080

# Command to run the server
CMD ["/server"]
