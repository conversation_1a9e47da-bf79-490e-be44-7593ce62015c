#!/bin/bash
# This script syncs the project directory to the remote server 'w2'.

# Exit immediately if a command exits with a non-zero status.
set -e

# Remove local binaries, -f ignores non-existent files.
rm -f app cli server client

# Sync the project directory to the remote server.
# -a: archive mode
# -v: verbose
# -P: progress
# --exclude: exclude files and directories
rsync -avP --exclude config.yaml --exclude periodic_task_config.yml --exclude .env.local --exclude-from='.gitignore' --exclude .git . w2:~/tandian-server/

echo "Sync complete."