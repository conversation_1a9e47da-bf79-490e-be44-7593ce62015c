#!/bin/bash

# Test script for graceful restart with tableflip

echo "========================================="
echo "Testing Graceful Restart with tableflip"
echo "========================================="
echo ""

# Function to check version endpoint
check_version() {
    echo "Checking /version endpoint:"
    curl -s http://localhost:8182/version
    echo ""
    echo "-----------------------------------------"
}

echo "1. Initial version check:"
check_version

echo ""
echo "2. Triggering graceful restart (sending SIGHUP)..."
echo "   Running: ./tableflip.sh"
./tableflip.sh

echo ""
echo "3. Waiting 2 seconds for restart to complete..."
sleep 2

echo ""
echo "4. Version after restart:"
check_version

echo ""
echo "Notice how:"
echo "- PID changes (new process)"
echo "- Started time changes (new process start time)"
echo "- Uptime resets to near 0"
echo ""
echo "This confirms graceful restart is working!"