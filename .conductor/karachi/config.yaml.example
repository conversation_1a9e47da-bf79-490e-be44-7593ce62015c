app_env: "development" # development or production
redis:
  addr: "127.0.0.1:6379"
  password: "PASSWORD"
  db: 0
asynq:
  redis_addr: "127.0.0.1:6379"
  redis_password: "PASSWORD"
  redis_db: 0
wechat_pay:
  app_id: wxf77ccd1007594f39
  mch_id: 1656753137
  mch_certificate_serial_number: 501074248E70FF853CFC617D79488B3CA1A99A39
  mch_api_v3_key: API_V3_KEY
  mch_api_v2_key: API_V2_KEY
  private_key_path: ./apiclient_key.pem
  use_dynamic_task_manager: true
monitoring_port: 8080
app_server_port: 8182
log:
  level: "info"
  modules:
    redis: "warn"
    server: "info"
    util: "error"
    tasks:note: "info"
    tasks:payment: "info"
    tasks:user: "debug"
    tasks:cloudbase_token: "info"
    tasks:wechat_token: "info"
    tasks:wecom_token: "info"
    tasks:transfer_bill: "info"
flagsmith:
  enabled: false # Set to true to enable Flagsmith integration
  environment_key: "YOUR_FLAGSMITH_ENVIRONMENT_KEY" # Get from Flagsmith dashboard
  base_url: "" # Optional: for self-hosted Flagsmith instances (default: https://edge.api.flagsmith.com/api/v1/)
  local_eval: false # Set to true for local evaluation mode (requires server-side SDK key)
