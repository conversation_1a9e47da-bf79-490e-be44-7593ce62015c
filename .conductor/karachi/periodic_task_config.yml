configs:
  - cronspec: "@every 30s"
    task_type: scheduler:refresh_cloudbase_access_token
    opts:
      queue: "default"
      max_retry: 4
      retention: "1h"

  - cronspec: "@every 30s"
    task_type: scheduler:refresh_wechat_access_token
    opts:
      queue: "default"
      max_retry: 3
      retention: "1h"

  - cronspec: "@every 30s"
    task_type: scheduler:refresh_wecom_access_token
    opts:
      queue: "default"
      max_retry: 3
      retention: "1h"

  - cronspec: "@every 2m"
    task_type: scheduler:auto_enqueue_send_note_link

  - cronspec: "@every 10m"
    task_type: scheduler:auto_enqueue_pass_note

  - cronspec: "@every 3s"
    task_type: scheduler:enqueue_query_transfer_bill
    opts:
      queue: "critical"
      max_retry: 3
      retention: "24h"

  - cronspec: "5 4 * * *"
    task_type: scheduler:enqueue_refresh_user_money

  - cronspec: "@every 30m"
    task_type: scheduler:fetch_wecom_groups

  - cronspec: "@every 1m"
    task_type: scheduler:query_unpaid_orders
    opts:
      queue: "order"
      max_retry: 3
      retention: "24h"

  - cronspec: "5 * * * *"
    task_type: scheduler:update_help_campaign_stats
    opts:
      queue: "daily"
      max_retry: 3
      retention: "720h"

  - cronspec: "@every 5m"
    task_type: scheduler:auto_scan_notes
    opts:
      queue: "note"
      max_retry: 2
      retention: "24h"

  - cronspec: "@every 5m"
    task_type: scheduler:auto_scan_cards
    opts:
      queue: "card"
      max_retry: 2
      retention: "24h"
