package main

import (
	"fmt"

	"github.com/Oudwins/zog"
	"github.com/dfang/go-prettyjson"
	"github.com/gofiber/fiber/v3"

	"tandian-server/internal/pay"
)

func HandleCreateTransferBillTest(c fiber.Ctx) error {
	payload := new(CreateTransferBillRequest)
	if err := c.Bind().JSON(payload); err != nil {
		return err
	}

	fmt.Printf("%+v\n", payload)

	s, _ := prettyjson.Marshal(payload)
	fmt.Println("prettyjson:", string(s))

	// todo: Validate req coming from frontend first
	errsMap := CreateTransferBillRequestSchema.Validate(payload)
	if errsMap != nil {
		sanitized := zog.Issues.SanitizeMap(errsMap)

		return c.JSON(fiber.Map{
			"code":    -1,
			"message": sanitized,
		})
	}

	outBillNo := pay.GenerateOutBillNo()
	transferBillNo := fmt.Sprintf("T%s", outBillNo)
	// Create Pending Withdrawals and Transactions Record
	req1 := pay.CreatePendingWithdrawalRequest{
		TransferAmount: float64(payload.TransferAmount),
		OutBillNo:      outBillNo,
		TransferBillNo: transferBillNo,
		OpenID:         payload.OpenID,
		UserID:         payload.UserID,
	}

	err := pay.CreatePendingRecordsWhenRequestWithdrawal(req1)
	if err != nil {
		return c.JSON(fiber.Map{
			"code":    -1,
			"message": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"code": 200,
	})
}
