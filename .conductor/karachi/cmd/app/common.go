package main

import (
	"log"

	"github.com/gofiber/fiber/v3"
)

// StandardResponse represents a standardized API response.
type StandardResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
	Errors  any    `json:"errors,omitempty"`
}

// ErrorResponse creates a standardized error response
// It logs the actual error internally but only returns a safe message to the client.
func ErrorResponse(c fiber.Ctx, statusCode, code int, message string, err error) error {
	if err != nil {
		// Log the actual error for debugging (includes request context)
		log.Printf("[ERROR] %s %s - %s: %v", c.Method(), c.Path(), message, err)
	}

	return c.Status(statusCode).JSON(StandardResponse{
		Code:    code,
		Message: message,
	})
}

// SuccessResponse creates a standardized success response.
func SuccessResponse(c fiber.Ctx, message string, data any) error {
	return c.Status(fiber.StatusOK).JSON(StandardResponse{
		Code:    200,
		Message: message,
		Data:    data,
	})
}

// ValidationErrorResponse creates a response for validation errors.
func ValidationErrorResponse(c fiber.Ctx, message string, errors any) error {
	return c.Status(fiber.StatusBadRequest).JSON(StandardResponse{
		Code:    -1,
		Message: message,
		Errors:  errors,
	})
}

// Constants for common error messages.
const (
	ErrMsgInternalServer = "服务器内部错误"
	ErrMsgBadRequest     = "请求参数错误"
	ErrMsgUnauthorized   = "未授权访问"
	ErrMsgNotFound       = "资源不存在"
	ErrMsgValidation     = "参数验证失败"
)
