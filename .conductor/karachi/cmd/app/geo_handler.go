package main

import (
	"strconv"

	"github.com/gofiber/fiber/v3"

	"tandian-server/internal/geo"
)

// ReverseGeocodeRequest represents the request structure for reverse geocoding.
type ReverseGeocodeRequest struct {
	Latitude  float64 `json:"latitude"  query:"latitude"`
	Longitude float64 `json:"longitude" query:"longitude"`
}

// AddressComponentResponse represents the address component structure.
type AddressComponentResponse struct {
	Nation       string `json:"nation"`
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	Street       string `json:"street"`
	StreetNumber string `json:"street_number"`
}

// ReverseGeocodeResponse represents the response structure.
type ReverseGeocodeResponse struct {
	Address          string                    `json:"address"`
	Latitude         float64                   `json:"latitude"`
	Longitude        float64                   `json:"longitude"`
	Name             string                    `json:"name,omitempty"`
	AddressComponent *AddressComponentResponse `json:"address_component,omitempty"`
	Error            string                    `json:"error,omitempty"`
}

// HandleReverseGeocode handles reverse geocoding requests
// It accepts latitude and longitude as query parameters or JSON body
// Example: GET /api/geo/reverse?latitude=39.9042&longitude=116.4074
// Example: POST /api/geo/reverse with JSON body {"latitude": 39.9042, "longitude": 116.4074}.
func HandleReverseGeocode(c fiber.Ctx) error {
	var req ReverseGeocodeRequest

	// Try to parse from query parameters first
	latStr := c.Query("latitude")
	lngStr := c.Query("longitude")

	if latStr != "" && lngStr != "" {
		// Parse from query parameters
		lat, err := strconv.ParseFloat(latStr, 64)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(ReverseGeocodeResponse{
				Error: "Invalid latitude format",
			})
		}

		lng, err := strconv.ParseFloat(lngStr, 64)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(ReverseGeocodeResponse{
				Error: "Invalid longitude format",
			})
		}

		req.Latitude = lat
		req.Longitude = lng
	} else {
		// Try to parse from JSON body
		if err := c.Bind().JSON(&req); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(ReverseGeocodeResponse{
				Error: "Invalid request format. Please provide latitude and longitude",
			})
		}
	}

	// Validate coordinates
	if req.Latitude < -90 || req.Latitude > 90 {
		return c.Status(fiber.StatusBadRequest).JSON(ReverseGeocodeResponse{
			Error: "Latitude must be between -90 and 90",
		})
	}

	if req.Longitude < -180 || req.Longitude > 180 {
		return c.Status(fiber.StatusBadRequest).JSON(ReverseGeocodeResponse{
			Error: "Longitude must be between -180 and 180",
		})
	}

	// Perform reverse geocoding with context from request
	result, err := geo.GetGeoDetailWithContext(c.Context(), req.Latitude, req.Longitude)
	if err != nil {
		// Handle specific error types
		switch err {
		case geo.ErrInvalidCoordinates:
			return c.Status(fiber.StatusBadRequest).JSON(ReverseGeocodeResponse{
				Error: "Invalid coordinates provided",
			})
		case geo.ErrAllKeysExhausted:
			return c.Status(fiber.StatusTooManyRequests).JSON(ReverseGeocodeResponse{
				Error: "API rate limit exceeded. Please try again later",
			})
		case geo.ErrServiceUnavailable:
			return c.Status(fiber.StatusServiceUnavailable).JSON(ReverseGeocodeResponse{
				Error: "Geocoding service temporarily unavailable",
			})
		default:
			return c.Status(fiber.StatusInternalServerError).JSON(ReverseGeocodeResponse{
				Error: "Failed to get address: " + err.Error(),
			})
		}
	}

	// Build response with detailed information
	response := ReverseGeocodeResponse{
		Address:   result.FormattedAddress,
		Latitude:  req.Latitude,
		Longitude: req.Longitude,
	}

	// Add address components if available
	if result.AddressComponent.Nation != "" || result.AddressComponent.Province != "" {
		response.AddressComponent = &AddressComponentResponse{
			Nation:       result.AddressComponent.Nation,
			Province:     result.AddressComponent.Province,
			City:         result.AddressComponent.City,
			District:     result.AddressComponent.District,
			Street:       result.AddressComponent.Street,
			StreetNumber: result.AddressComponent.StreetNumber,
		}
	}

	// Add POI name if available
	if result.AddressComponent.Poi != "" {
		response.Name = result.AddressComponent.Poi
	}

	// Return successful response
	return c.JSON(response)
}
