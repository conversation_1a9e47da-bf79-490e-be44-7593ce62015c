package main

import (
	"context"
	"errors"
	"fmt"

	"tandian-server/internal/util/cloudbase"
)

// CheckUserHasTandianOrder checks if user has placed any tandian type order
// It accepts either openid or userID (at least one must be provided).
func CheckUserHasTandianOrder(ctx context.Context, openid, userID string) (bool, error) {
	// Validate input - at least one identifier must be provided
	if openid == "" && userID == "" {
		return false, errors.New("either openid or userID must be provided")
	}

	// Build query conditions
	andConditions := []interface{}{
		map[string]interface{}{
			"order_type": map[string]interface{}{
				"$eq": "tandian1", // 探店类订单
			},
		},
		map[string]interface{}{
			"status": map[string]interface{}{
				"$in": []string{"10", "20", "30", "31", "32"}, // 已支付、已核销、笔记审核中，已通过或驳回的订单
			},
		},
	}

	// Add user identifier condition
	if userID != "" {
		andConditions = append(andConditions, map[string]interface{}{
			"user_id": map[string]interface{}{
				"$eq": userID,
			},
		})
	} else if openid != "" {
		andConditions = append(andConditions, map[string]interface{}{
			"openid": map[string]interface{}{
				"$eq": openid,
			},
		})
	}

	// Query CloudBase to check if user has any tandian orders
	query := map[string]interface{}{
		"select": map[string]interface{}{
			"id": true,
		},
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"$and": andConditions,
			},
		},
		"limit": 1, // We only need to know if at least one exists
	}

	result, err := cloudbase.GetItems(ctx, "orders", query)
	if err != nil {
		return false, fmt.Errorf("failed to query tandian orders: %w", err)
	}

	// Check if we have any records
	if records, ok := result["records"].([]interface{}); ok && len(records) > 0 {
		return true, nil
	}

	return false, nil
}
