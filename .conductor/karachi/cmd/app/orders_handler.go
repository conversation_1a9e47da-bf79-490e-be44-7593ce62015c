package main

import (
	"context"
	"encoding/json"

	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gofiber/fiber/v3"
	"go.uber.org/zap"

	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
	"tandian-server/internal/util/cloudbase"
)

func CreateOrder() {
	// Create a pool with go-redis (or redigo) which is the pool redisync will
	// use while communicating with Redis. This can also be any pool that
	// implements the `redis.Pool` interface.
	rdb := redis.GetClient()
	pool := goredis.NewPool(rdb) // or, pool := redigo.NewPool(...)

	// Create an instance of redisync to be used to obtain a mutual exclusion
	// lock.
	rs := redsync.New(pool)

	// Obtain a new mutex by using the same name for all instances wanting the
	// same lock.
	mutexname := "my-global-mutex"
	mutex := rs.NewMutex(mutexname)

	// Obtain a lock for our given mutex. After this is successful, no one else
	// can obtain the same lock (the same mutex name) until we unlock it.
	if err := mutex.Lock(); err != nil {
		panic(err)
	}

	// Do your work that requires the lock.

	// create order
	// reduct stock
	// ctx := context.Background()
	// result, err := cloudbase.CreateItem(ctx, "orders", map[string]interface{}{
	// 	"data": map[string]interface{}{
	// 		"name":        resp.GroupChat.Name,
	// 		"chat_id":     resp.GroupChat.ChatID,
	// 		"create_time": resp.GroupChat.CreateTime,
	// 		// "group_owner": resp.GroupChat.Owner,
	// 		// "member_list": resp.GroupChat.MemberList,
	// 	},
	// })

	// Release the lock so other processes or threads can obtain a lock.
	if ok, err := mutex.Unlock(); !ok || err != nil {
		panic("unlock failed")
	}
}

func HandleTest(c fiber.Ctx) error {
	log := logger.Get("app")
	ctx := context.Background()
	promotionID := c.Params("id")

	// Define a struct for the promotion data for easier handling.
	type Promotion struct {
		ID                  string `json:"_id"`
		Name                string `json:"name"`
		RealtimeQuantity    int64  `json:"realtime_quantity"`
		PromotionType       string `json:"promotion_type"`
		IsChain             bool   `json:"is_chain"`
		ProductID           string `json:"product_id"`
		RemainTotalQuantity int64  `json:"remain_total_quantity"`
	}

	// The query to send to cloudbase.
	q := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": promotionID,
				},
			},
		},
		"select": map[string]interface{}{
			"_id":                   true,
			"name":                  true,
			"realtime_quantity":     true,
			"promotion_type":        true,
			"is_chain":              true,
			"product_id":            true,
			"remain_total_quantity": true, // Added missing field
		},
	}

	// Call the cloudbase GetItem function.
	record, err := cloudbase.GetItem(ctx, "promotions", q)
	if err != nil {
		log.Error("failed to get item from cloudbase", zap.Error(err))

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "failed to get promotion",
		})
	}

	// Check if the record is empty
	if len(record) == 0 {
		log.Info("promotion not found for id", zap.String("promotionID", promotionID))

		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "promotion not found",
		})
	}

	// Marshal the record map into JSON bytes.
	recordBytes, err := json.Marshal(record)
	if err != nil {
		log.Error("failed to marshal record", zap.Error(err))

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "failed to process promotion data",
		})
	}

	// Unmarshal the JSON bytes into our Promotion struct.
	var promotion Promotion
	if err := json.Unmarshal(recordBytes, &promotion); err != nil {
		log.Error("failed to unmarshal promotion", zap.Error(err))

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "failed to process promotion data",
		})
	}

	log.Info("retrieved promotion successfully", zap.Any("promotion", promotion))

	// Stock checking logic.
	switch promotion.PromotionType {
	case "ykj":
		log.Info("checking stock for 'ykj' promotion", zap.Bool("is_chain", promotion.IsChain))

		if promotion.IsChain {
			log.Info("chain promotion stock", zap.Int64("remain_total_quantity", promotion.RemainTotalQuantity))

			if promotion.RemainTotalQuantity <= 0 {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
					"error": "out of stock",
				})
			}
		}
	case "tandian1":
		log.Info("checking stock for 'tandian1' promotion", zap.Int64("realtime_quantity", promotion.RealtimeQuantity))

		if promotion.RealtimeQuantity <= 0 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "out of stock",
			})
		}
	}

	return c.JSON(promotion)
}
