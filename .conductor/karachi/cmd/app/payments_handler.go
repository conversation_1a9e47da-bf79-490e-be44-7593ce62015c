package main

import (
	"fmt"
	"log"
	"math"

	"github.com/dfang/go-prettyjson"
	"github.com/gofiber/fiber/v3"

	"tandian-server/internal/pay"
)

type UnifiedOrderRequest struct {
	ReferrerID    string  `json:"referrer_id"`
	UserID        string  `json:"user_id"`
	PromotionID   string  `json:"promotion_id"`
	ShopID        string  `json:"shop_id"`
	PID           string  `json:"p_id"`
	OrderType     string  `json:"order_type"`
	TotalAmount   float64 `json:"total_amount"`
	PaidAmount    float64 `json:"paid_amount"`
	RefundAmount  float64 `json:"refund_amount"`
	Commission    float64 `json:"commission"`
	OrderNo       string  `json:"order_no"`
	OpenID        string  `json:"openid"` // WeChat OpenID for payment
	CouponCode    string  `json:"coupon_code"`
	CouponStockId string  `json:"coupon_stock_id"`
}

func HandlePrepay(c fiber.Ctx) error {
	payload := new(pay.PrepayRequest)
	if err := c.Bind().JSON(payload); err != nil {
		return err
	}

	// fmt.Printf("%+v\n", payload)

	errsMap := pay.PrepayRequestSchema.Validate(payload)
	if errsMap != nil {
		// handle errors -> see Errors section
		if errsMap["openid"] != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"error":   errsMap["openid"][0],
			})
		}

		if errsMap["out_trade_no"] != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"error":   errsMap["out_trade_no"][0],
			})
		}

		if errsMap["description"] != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"error":   errsMap["description"][0],
			})
		}
	}

	client := pay.NewWechatPayClient()

	resp, err := client.Prepay(c.Context(), *payload)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"error":   err.Error(),
		})
	}

	if resp.PrepayId == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"error":   "prepay_id is nil",
		})
	}

	unifiedOrderResp, err := client.UnifiedOrder(c.Context(), *resp.PrepayId)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"error":   "unified order failed",
		})
	}

	return c.JSON(fiber.Map{
		"code": 200,
		"msg":  "prepay success",
		// "data": fiber.Map{
		// 	"prepay_id": "prepay_id_1234567890",
		// },
		"data": unifiedOrderResp,
	})
}

func HandleUnifiedOrder(c fiber.Ctx) error {
	payload := new(UnifiedOrderRequest)
	if err := c.Bind().JSON(payload); err != nil {
		fmt.Println(err)

		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"code":  -1,
			"error": "Invalid request body",
		})
	}

	pp, _ := prettyjson.Marshal(payload)
	fmt.Println(string(pp))

	errsMap := pay.UnifiedOrderRequestSchema.Validate(payload)
	fmt.Printf("%+v\n", errsMap)

	for field, errs := range errsMap {
		if len(errs) > 0 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"code":    -1,
				"error":   errs[0],
				"message": field,
			})
		}
	}

	// step1: validate and create order
	client := pay.NewWechatPayClient()
	orderID, err := client.CreateOrder(c.Context(), pay.CreateOrderRequest{
		ReferrerID:    payload.ReferrerID,
		UserID:        payload.UserID,
		PromotionID:   payload.PromotionID,
		ShopID:        payload.ShopID,
		OrderType:     payload.OrderType,
		TotalAmount:   math.Round(payload.TotalAmount*100) / 100.0,
		PaidAmount:    math.Round(payload.PaidAmount*100) / 100.0,
		RefundAmount:  math.Round(payload.RefundAmount*100) / 100.0,
		Commission:    math.Round(payload.Commission*100) / 100.0,
		OrderNo:       payload.OrderNo,
		OpenID:        payload.OpenID,
		CouponCode:    payload.CouponCode,
		CouponStockId: payload.CouponStockId,
	})
	if err != nil {
		fmt.Println(err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"code":  -1,
			"error": err.Error(),
		})
	}

	// step2: prepay
	// Create description for the payment
	description := fmt.Sprintf("订单号: %s", payload.OrderNo)
	switch payload.OrderType {
	case "tandian1":
		description = fmt.Sprintf("探店订单: %s", payload.OrderNo)
	case "ykj", "tjt":
		description = fmt.Sprintf("特价团订单: %s", payload.OrderNo)
	}

	prepayReq := pay.PrepayRequest{
		OpenId:      payload.OpenID,
		OutTradeNo:  payload.OrderNo,
		Description: description,
		Attach:      fmt.Sprintf("order_id:%s", orderID),         // Attach the order ID for reference
		Total:       int64(math.Round(payload.PaidAmount * 100)), // Use paid amount for the payment (with rounding)
	}

	resp, err := client.Prepay(c.Context(), prepayReq)
	if err != nil {
		log.Printf("Prepay failed for order %s: %v", orderID, err) // Log details internally

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "支付预处理失败",
		})
	}

	if resp.PrepayId == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"code":  -1,
			"error": "prepay_id is nil",
		})
	}

	// Get payment parameters for wx.requestPayment
	paymentParams, err := client.UnifiedOrder(c.Context(), *resp.PrepayId)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"code":  -1,
			"error": "failed to get payment parameters",
		})
	}

	return c.JSON(fiber.Map{
		"code": 200,
		"msg":  "order created successfully",
		"data": fiber.Map{
			"orderId":        orderID,
			"orderNo":        payload.OrderNo,
			"payment_params": paymentParams, // Return payment parameters for wx.requestPayment
		},
	})
}

func HandleCloseOrder(c fiber.Ctx) error {
	payload := new(pay.CloseOrderRequest)
	if err := c.Bind().JSON(payload); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"code":  -1,
			"error": "Invalid request body",
		})
	}

	// Validate order number: required
	if payload.OutTradeNo == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"error":   "out_trade_no is required",
		})
	}

	if payload.PromotionID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"error":   "promotion_id is required",
		})
	}

	if payload.PromotionType == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"error":   "promotion_type is required",
		})
	}

	// Create WeChat Pay client
	client := pay.NewWechatPayClient()

	fmt.Println("call close order")
	// Call CloseOrder API
	err := client.CloseOrder(c.Context(), *payload)
	if err != nil {
		log.Printf("Failed to close order: %v", err) // Log details internally

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "关闭订单失败",
		})
	}

	return c.JSON(fiber.Map{
		"code": 200,
		"msg":  "order closed successfully",
		"data": fiber.Map{
			"out_trade_no": payload.OutTradeNo,
		},
	})
}

func HandleRefundOrder(c fiber.Ctx) error {
	payload := new(pay.RefundRequest)
	if err := c.Bind().JSON(payload); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"code":  -1,
			"error": "Invalid request body",
		})
	}

	// Validate required fields
	if payload.OutTradeNo == "" && payload.TransactionId == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"code":  -1,
			"error": "Either out_trade_no or transaction_id is required",
		})
	}

	if payload.From == "miniapp" {
		payload.Reason = "用户申请退款"
	}

	if payload.From == "admin" || payload.From == "cli" {
		if payload.Reason == "" {
			payload.Reason = "商品下架"
		}
	}

	// if payload.Amount <= 0 {
	// 	return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
	// 		"success": false,
	// 		"error":   "refund amount must be greater than 0",
	// 	})
	// }

	// if payload.TotalAmount <= 0 {
	// 	return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
	// 		"success": false,
	// 		"error":   "total amount must be greater than 0",
	// 	})
	// }

	// if payload.Amount > payload.TotalAmount {
	// 	return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
	// 		"success": false,
	// 		"error":   "refund amount cannot exceed total amount",
	// 	})
	// }

	// Process refund using the standalone function
	resp, err := pay.RefundOrder(c.Context(), *payload)
	if err != nil {
		log.Printf("Refund failed: %v", err) // Log details internally

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "退款失败",
		})
	}

	// Return successful response
	return c.JSON(fiber.Map{
		"code": 200,
		"msg":  "refund initiated successfully",
		"data": fiber.Map{
			"refund_id":     resp.RefundId,
			"out_refund_no": resp.OutRefundNo,
			"status":        resp.Status,
			"amount":        resp.Amount.Refund,
			"create_time":   resp.CreateTime,
		},
	})
}

func HandleQueryOrder(c fiber.Ctx) error {
	var payload struct {
		OrderNo string `json:"orderNo"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"error":   "orderNo is required",
		})
	}

	// Validate orderNo
	if payload.OrderNo == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"error":   "orderNo is required",
		})
	}

	// Query order status using the standalone function
	transaction, err := pay.QueryOrder(c.Context(), payload.OrderNo, "")
	if err != nil {
		log.Printf("Failed to query order: %v", err) // Log details internally

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "查询订单失败",
		})
	}

	// Build response data with nil checks
	responseData := fiber.Map{
		"orderNo": payload.OrderNo,
	}

	// Add transaction ID if available
	if transaction.TransactionId != nil {
		responseData["transactionId"] = *transaction.TransactionId
	}

	// Add trade state if available
	if transaction.TradeState != nil {
		responseData["tradeState"] = *transaction.TradeState
	}

	// Add trade state description if available
	if transaction.TradeStateDesc != nil {
		responseData["tradeStateDesc"] = *transaction.TradeStateDesc
	}

	// Add amount info if available
	if transaction.Amount != nil {
		amountData := fiber.Map{}
		if transaction.Amount.Total != nil {
			amountData["total"] = *transaction.Amount.Total
		}

		if transaction.Amount.Currency != nil {
			amountData["currency"] = *transaction.Amount.Currency
		}

		responseData["amount"] = amountData
	}

	// Add payer info if available
	if transaction.Payer != nil && transaction.Payer.Openid != nil {
		responseData["payerOpenid"] = *transaction.Payer.Openid
	}

	// Add success time if available
	if transaction.SuccessTime != nil {
		responseData["successTime"] = *transaction.SuccessTime
	}

	// Return order status
	return c.JSON(fiber.Map{
		"code": 200,
		"msg":  "order queried successfully",
		"data": responseData,
	})
}

func HandleVerifyOrder(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		OrderNo      string `json:"order_no"`
		VerifyMethod int    `json:"verify_method"` // 1: 用户端点立即核销, 2: 商家端扫码核销
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"error":   "Invalid request body",
			"message": "请求参数解析失败",
		})
	}

	// Validate order number
	if payload.OrderNo == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"error":   "order_no is required",
			"message": "订单号不能为空",
		})
	}

	if payload.VerifyMethod == 0 {
		payload.VerifyMethod = 1
	}

	// Use the standalone function to verify the order
	err := pay.VerifyOrder(c.Context(), payload.OrderNo, fmt.Sprintf("%d", payload.VerifyMethod))
	if err != nil {
		log.Printf("Failed to verify order: %v", err)

		// Check if it's a not found error
		if err.Error() == fmt.Sprintf("order %s not found or already verified", payload.OrderNo) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"code":    -1,
				"error":   "Order not found or already verified",
				"message": "订单不存在或已核销",
			})
		}

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "更新订单状态失败",
		})
	}

	count := 1 // Since the verify succeeded, assume 1 record was updated

	// Log the verification
	fmt.Printf("Order %s verified successfully\n", payload.OrderNo)

	// TODO: Call cloud function to notify (similar to wx.cloud.callFunction)
	// This would typically be done through an async task or notification service
	// For now, we'll just log it
	go func() {
		// Simulate async notification
		fmt.Printf("Notification for order %s verification sent\n", payload.OrderNo)
		// In production, this would call your notification service
		// e.g., notificationService.NotifyOrderVerification(payload.OrderNo)
	}()

	// Return success response
	return c.JSON(fiber.Map{
		"code": 200,
		"msg":  "order verified successfully",
		"data": fiber.Map{
			"count":    count,
			"order_no": payload.OrderNo,
			"status":   "20",
		},
	})
}
