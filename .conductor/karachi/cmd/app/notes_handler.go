package main

import (
	"log"
	"net/http"
	"time"

	"github.com/Oudwins/zog"
	"github.com/gofiber/fiber/v3"

	"tandian-server/internal/util/cloudbase"
)

// Note status constants.
const (
	NOTE_STATUS_PENDING      = 0 // 待审核
	NOTE_STATUS_APPROVED     = 1 // 已通过
	NOTE_STATUS_REJECTED     = 2 // 已驳回
	NOTE_STATUS_LINK_SENT    = 3 // 笔记链接已发送到商家群
	NOTE_STATUS_PRE_APPROVED = 4 // 笔记已通过预审
)

// Order status constants for notes.
const (
	ORDER_STATUS_PENDING_REVIEW = "30" // 笔记审核中
	ORDER_STATUS_APPROVED       = "31" // 笔记已通过
	ORDER_STATUS_REJECTED       = "32" // 笔记已驳回
)

// feedback
// rating
// remark
// status
// url
// user_id
// order_id
// card_id
// shop_id
// sync_url
// auditedAt

// SubmitNoteRequest represents the request payload for submitting a note.
type SubmitNoteRequest struct {
	ID        string `json:"id,omitempty"`        // Note ID for updates
	Feedback  string `json:"feedback,omitempty"`  // 反馈内容
	Rating    int    `json:"rating,omitempty"`    // 评分
	Remark    string `json:"remark,omitempty"`    // 备注
	URL       string `json:"url"`                 // 笔记链接
	UserID    string `json:"user_id"`             // User info with _id
	OrderID   string `json:"order_id"`            // Order info with _id
	CardID    string `json:"card_id,omitempty"`   // 卡片ID
	ShopID    string `json:"shop_id,omitempty"`   // Shop info with _id
	SyncURL   string `json:"sync_url,omitempty"`  // 同步链接
	Status    int    `json:"status,omitempty"`    // 状态
	AuditedAt int64  `json:"auditedAt,omitempty"` // 审核时间（毫秒时间戳）
	CreatedAt int64  `json:"created_at,omitempty"`
	UpdatedAt int64  `json:"updated_at,omitempty"`
}

// SubmitNoteResponse represents the response after submitting a note.
type SubmitNoteResponse struct {
	NoteID  string `json:"note_id"`
	OrderID string `json:"order_id"`
}

// SubmitNoteRequestSchema defines the validation schema for submitting a note.
var SubmitNoteRequestSchema = zog.Struct(zog.Shape{
	"ID":        zog.String().Optional(),
	"Remark":    zog.String().Optional(),
	"URL":       zog.String().Required(zog.Message("笔记链接必须")),
	"UserID":    zog.String().Required(zog.Message("用户ID必须")),
	"OrderID":   zog.String().Required(zog.Message("订单ID必须")),
	"CardID":    zog.String().Required(zog.Message("卡片ID必须")),
	"ShopID":    zog.String().Required(zog.Message("店铺ID必须")),
	"SyncURL":   zog.String().Optional(),
	"Feedback":  zog.String().Optional(),
	"Rating":    zog.Int().Optional().GTE(1, zog.Message("评分必须大于等于1")).LTE(5, zog.Message("评分必须小于等于5")),
	"Status":    zog.Int().Optional(),
	"AuditedAt": zog.Int64().Optional(),
})

// Helper functions

// errorResponse creates a standardized error response.
func errorResponse(code int, message string, err error) fiber.Map {
	response := fiber.Map{
		"code":    code,
		"message": message,
	}
	if err != nil {
		response["error"] = err.Error()
	}

	return response
}

// successResponse creates a standardized success response.
func successResponse(message string, data any) fiber.Map {
	return fiber.Map{
		"code":    200,
		"message": message,
		"data":    data,
	}
}

// getCountFromResult extracts count from CloudBase result.
func getCountFromResult(result map[string]any) int {
	if c, ok := result["count"].(int); ok {
		return c
	}

	if c, ok := result["count"].(float64); ok {
		return int(c)
	}

	return 0
}

// buildWhereClause builds a CloudBase where clause with equality condition.
func buildWhereClause(field, value string) map[string]any {
	return map[string]any{
		"filter": map[string]any{
			"where": map[string]any{
				field: map[string]any{
					"$eq": value,
				},
			},
		},
	}
}

// HandleSubmitNote handles the submission of notes (create or update).
func HandleSubmitNote(c fiber.Ctx) error {
	// Parse request body
	var payload SubmitNoteRequest
	if err := c.Bind().JSON(&payload); err != nil {
		log.Printf("Failed to parse submit note request: %v", err)
		return c.Status(http.StatusBadRequest).JSON(errorResponse(-1, "请求参数解析失败", err))
	}

	// Validate request with Zog schema
	errsMap := SubmitNoteRequestSchema.Validate(&payload)
	if errsMap != nil {
		sanitized := zog.Issues.SanitizeMap(errsMap)
		response := errorResponse(-1, "参数验证失败", nil)
		response["errors"] = sanitized

		return c.Status(http.StatusBadRequest).JSON(response)
	}

	ctx := c.Context()

	var noteID string

	// Prepare note data
	noteData := map[string]any{
		"user_id":  map[string]any{"_id": payload.UserID},
		"order_id": map[string]any{"_id": payload.OrderID},
		"card_id":  map[string]any{"_id": payload.CardID},
		"shop_id":  map[string]any{"_id": payload.ShopID},
		"url":      payload.URL,
	}
	// Add optional fields only if they have values
	if payload.SyncURL != "" {
		noteData["sync_url"] = payload.SyncURL
	}

	if payload.Feedback != "" {
		noteData["feedback"] = payload.Feedback
	}

	if payload.Rating > 0 {
		noteData["rating"] = payload.Rating
	}

	if payload.Remark != "" {
		noteData["remark"] = payload.Remark
	}

	// If note ID exists, update the note; otherwise create new note
	if payload.ID != "" {
		// Update existing note
		noteData["status"] = NOTE_STATUS_PENDING
		noteData["updatedAt"] = time.Now().Unix() * 1000 // CloudBase uses milliseconds

		updatePayload := map[string]any{
			"filter": map[string]any{
				"where": map[string]any{
					"_id": map[string]any{
						"$eq": payload.ID,
					},
				},
			},
			"data": noteData,
		}

		result, err := cloudbase.UpdateItem(ctx, "notes", updatePayload)
		if err != nil {
			log.Printf("Failed to update note: %v", err)
			return c.Status(http.StatusInternalServerError).JSON(errorResponse(-1, "更新笔记失败", err))
		}

		if getCountFromResult(result) == 0 {
			return c.Status(http.StatusNotFound).JSON(errorResponse(-1, "笔记不存在", nil))
		}

		noteID = payload.ID
		log.Printf("Updated note: %s", noteID)
	} else {
		// Create new note
		noteData["status"] = NOTE_STATUS_PENDING
		noteData["createdAt"] = time.Now().Unix() * 1000 // CloudBase uses milliseconds
		noteData["updatedAt"] = time.Now().Unix() * 1000 // CloudBase uses milliseconds
		createPayload := map[string]any{
			"data": noteData,
		}

		result, err := cloudbase.CreateItem(ctx, "notes", createPayload)
		if err != nil {
			log.Printf("Failed to create note: %v", err)
			return c.Status(http.StatusInternalServerError).JSON(errorResponse(-1, "创建笔记失败", err))
		}

		// Extract note ID from result
		if id, ok := result["id"].(string); ok {
			noteID = id
		} else {
			return c.Status(http.StatusInternalServerError).JSON(errorResponse(-1, "创建笔记成功但未能获取ID", nil))
		}

		log.Printf("Created new note: %s", noteID)
	}

	// Update order status to pending review
	orderUpdatePayload := map[string]any{
		"filter": map[string]any{
			"where": map[string]any{
				"_id": map[string]any{
					"$eq": payload.OrderID,
				},
			},
		},
		"data": map[string]any{
			"status":     ORDER_STATUS_PENDING_REVIEW,
			"updated_at": time.Now().Unix() * 1000, // CloudBase uses milliseconds
		},
	}

	orderResult, err := cloudbase.UpdateItem(ctx, "orders", orderUpdatePayload)
	if err != nil {
		log.Printf("Failed to update order status: %v", err)
		return c.Status(http.StatusInternalServerError).JSON(errorResponse(-1, "更新订单状态失败", err))
	}

	if getCountFromResult(orderResult) == 0 {
		log.Printf("Warning: Order %s was not updated (may already be in pending review status)", payload.OrderID)
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "提交笔记成功",
		"data": SubmitNoteResponse{
			NoteID:  noteID,
			OrderID: payload.OrderID,
		},
	})
}

// HandleGetNote retrieves a note by ID.
func HandleGetNote(c fiber.Ctx) error {
	noteID := c.Params("id")
	if noteID == "" {
		return c.Status(http.StatusBadRequest).JSON(errorResponse(-1, "缺少笔记ID", nil))
	}

	ctx := c.Context()

	// Query note from CloudBase
	query := map[string]any{
		"filter": map[string]any{
			"where": map[string]any{
				"_id": map[string]any{
					"$eq": noteID,
				},
			},
		},
	}

	result, err := cloudbase.GetItem(ctx, "notes", query)
	if err != nil {
		log.Printf("Failed to get note: %v", err)
		return c.Status(http.StatusInternalServerError).JSON(errorResponse(-1, "获取笔记失败", err))
	}

	if len(result) == 0 {
		return c.Status(http.StatusNotFound).JSON(errorResponse(-1, "笔记不存在", nil))
	}

	return c.Status(http.StatusOK).JSON(successResponse("获取笔记成功", result))
}

// HandleListNotes lists notes with optional filters.
func HandleListNotes(c fiber.Ctx) error {
	// Parse query parameters
	userID := c.Query("user_id")
	orderID := c.Query("order_id")
	status := c.Query("status")
	limit := 10
	offset := 0

	ctx := c.Context()

	// Build query
	where := map[string]any{}

	if userID != "" {
		where["user_id._id"] = map[string]any{
			"$eq": userID,
		}
	}

	if orderID != "" {
		where["order_id._id"] = map[string]any{
			"$eq": orderID,
		}
	}

	if status != "" {
		where["status"] = map[string]any{
			"$eq": status,
		}
	}

	query := map[string]any{
		"filter": map[string]any{
			"where": where,
		},
		"limit":  limit,
		"offset": offset,
		"orderBy": []any{
			map[string]any{
				"field":     "created_at",
				"direction": "desc",
			},
		},
	}

	result, err := cloudbase.GetItems(ctx, "notes", query)
	if err != nil {
		log.Printf("Failed to list notes: %v", err)
		return c.Status(http.StatusInternalServerError).JSON(errorResponse(-1, "获取笔记列表失败", err))
	}

	return c.Status(http.StatusOK).JSON(successResponse("获取笔记列表成功", result))
}

// HandleDeleteNote deletes a note by ID.
func HandleDeleteNote(c fiber.Ctx) error {
	noteID := c.Params("id")
	if noteID == "" {
		return c.Status(http.StatusBadRequest).JSON(errorResponse(-1, "缺少笔记ID", nil))
	}

	// Get user ID from context
	userID := c.Get("X-User-ID", "")
	if userID == "" {
		return c.Status(http.StatusUnauthorized).JSON(errorResponse(-1, "用户未登录", nil))
	}

	ctx := c.Context()

	// Delete note (with user verification)
	deletePayload := map[string]any{
		"filter": map[string]any{
			"where": map[string]any{
				"$and": []any{
					map[string]any{
						"_id": map[string]any{
							"$eq": noteID,
						},
					},
					map[string]any{
						"user_id._id": map[string]any{
							"$eq": userID,
						},
					},
				},
			},
		},
	}

	result, err := cloudbase.DeleteItem(ctx, "notes", deletePayload)
	if err != nil {
		log.Printf("Failed to delete note: %v", err)
		return c.Status(http.StatusInternalServerError).JSON(errorResponse(-1, "删除笔记失败", err))
	}

	if getCountFromResult(result) == 0 {
		return c.Status(http.StatusNotFound).JSON(errorResponse(-1, "笔记不存在或无权删除", nil))
	}

	return c.Status(http.StatusOK).JSON(successResponse("删除笔记成功", nil))
}
