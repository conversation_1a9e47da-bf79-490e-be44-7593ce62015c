package main

import (
	"bytes"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gofiber/fiber/v3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func generateWuhanCoordinates() (float64, float64) {
	minLat, maxLat := 30.35, 30.85
	minLng, maxLng := 113.70, 114.65
	
	// Use crypto/rand for better randomness
	latRange := int64((maxLat - minLat) * 1000000)
	lngRange := int64((maxLng - minLng) * 1000000)
	
	latRand, _ := rand.Int(rand.Reader, big.NewInt(latRange))
	lngRand, _ := rand.Int(rand.Reader, big.NewInt(lngRange))
	
	lat := minLat + float64(latRand.Int64())/1000000
	lng := minLng + float64(lngRand.Int64())/1000000
	
	return lat, lng
}

func TestHandleReverseGeocode_QueryParams(t *testing.T) {
	app := fiber.New()
	app.Get("/api/v1/geo/reverse", HandleReverseGeocode)
	
	lat, lng := generateWuhanCoordinates()
	
	req := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/geo/reverse?latitude=%f&longitude=%f", lat, lng), nil)
	resp, err := app.Test(req, fiber.TestConfig{Timeout: 10 * time.Second})
	require.NoError(t, err)
	
	assert.Equal(t, fiber.StatusOK, resp.StatusCode)
	
	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err)
	
	var result ReverseGeocodeResponse
	err = json.Unmarshal(body, &result)
	require.NoError(t, err)
	
	assert.NotEmpty(t, result.Address)
	assert.Empty(t, result.Error)
	assert.InDelta(t, lat, result.Latitude, 0.000001)
	assert.InDelta(t, lng, result.Longitude, 0.000001)
	
	// Verify address components
	if result.AddressComponent != nil {
		assert.Contains(t, result.AddressComponent.Province, "湖北")
		assert.Contains(t, result.AddressComponent.City, "武汉")
		assert.NotEmpty(t, result.AddressComponent.Nation)
	}
	
	t.Logf("Generated coordinates: lat=%f, lng=%f", lat, lng)
	t.Logf("Retrieved address: %s", result.Address)
	if result.AddressComponent != nil {
		t.Logf("Province: %s, City: %s, District: %s", 
			result.AddressComponent.Province, 
			result.AddressComponent.City, 
			result.AddressComponent.District)
	}
}

func TestHandleReverseGeocode_JSONBody(t *testing.T) {
	app := fiber.New()
	app.Post("/api/v1/geo/reverse", HandleReverseGeocode)
	
	lat, lng := generateWuhanCoordinates()
	
	reqBody := ReverseGeocodeRequest{
		Latitude:  lat,
		Longitude: lng,
	}
	
	jsonBody, err := json.Marshal(reqBody)
	require.NoError(t, err)
	
	req := httptest.NewRequest("POST", "/api/v1/geo/reverse", bytes.NewReader(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := app.Test(req, fiber.TestConfig{Timeout: 10 * time.Second})
	require.NoError(t, err)
	
	assert.Equal(t, fiber.StatusOK, resp.StatusCode)
	
	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err)
	
	var result ReverseGeocodeResponse
	err = json.Unmarshal(body, &result)
	require.NoError(t, err)
	
	assert.NotEmpty(t, result.Address)
	assert.Empty(t, result.Error)
	assert.InDelta(t, lat, result.Latitude, 0.000001)
	assert.InDelta(t, lng, result.Longitude, 0.000001)
	
	t.Logf("Generated coordinates: lat=%f, lng=%f", lat, lng)
	t.Logf("Retrieved address: %s", result.Address)
	t.Logf("Full response: %+v", result)
}

func TestHandleReverseGeocode_InvalidLatitude(t *testing.T) {
	app := fiber.New()
	app.Get("/api/v1/geo/reverse", HandleReverseGeocode)
	
	tests := []struct {
		name string
		lat  float64
		lng  float64
	}{
		{"Latitude too low", -91.0, 114.0},
		{"Latitude too high", 91.0, 114.0},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/geo/reverse?latitude=%f&longitude=%f", tt.lat, tt.lng), nil)
			resp, err := app.Test(req, fiber.TestConfig{Timeout: 10 * time.Second})
			require.NoError(t, err)
			
			assert.Equal(t, fiber.StatusBadRequest, resp.StatusCode)
			
			body, err := io.ReadAll(resp.Body)
			require.NoError(t, err)
			
			var result ReverseGeocodeResponse
			err = json.Unmarshal(body, &result)
			require.NoError(t, err)
			
			assert.Empty(t, result.Address)
			assert.NotEmpty(t, result.Error)
			assert.Contains(t, result.Error, "Latitude must be between -90 and 90")
		})
	}
}

func TestHandleReverseGeocode_InvalidLongitude(t *testing.T) {
	app := fiber.New()
	app.Get("/api/v1/geo/reverse", HandleReverseGeocode)
	
	tests := []struct {
		name string
		lat  float64
		lng  float64
	}{
		{"Longitude too low", 30.5, -181.0},
		{"Longitude too high", 30.5, 181.0},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/geo/reverse?latitude=%f&longitude=%f", tt.lat, tt.lng), nil)
			resp, err := app.Test(req, fiber.TestConfig{Timeout: 10 * time.Second})
			require.NoError(t, err)
			
			assert.Equal(t, fiber.StatusBadRequest, resp.StatusCode)
			
			body, err := io.ReadAll(resp.Body)
			require.NoError(t, err)
			
			var result ReverseGeocodeResponse
			err = json.Unmarshal(body, &result)
			require.NoError(t, err)
			
			assert.Empty(t, result.Address)
			assert.NotEmpty(t, result.Error)
			assert.Contains(t, result.Error, "Longitude must be between -180 and 180")
		})
	}
}

func TestHandleReverseGeocode_InvalidQueryFormat(t *testing.T) {
	app := fiber.New()
	app.Get("/api/v1/geo/reverse", HandleReverseGeocode)
	
	tests := []struct {
		name     string
		query    string
		expected string
	}{
		{"Invalid latitude format", "?latitude=abc&longitude=114.0", "Invalid latitude format"},
		{"Invalid longitude format", "?latitude=30.5&longitude=xyz", "Invalid longitude format"},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/v1/geo/reverse"+tt.query, nil)
			resp, err := app.Test(req, fiber.TestConfig{Timeout: 10 * time.Second})
			require.NoError(t, err)
			
			assert.Equal(t, fiber.StatusBadRequest, resp.StatusCode)
			
			body, err := io.ReadAll(resp.Body)
			require.NoError(t, err)
			
			var result ReverseGeocodeResponse
			err = json.Unmarshal(body, &result)
			require.NoError(t, err)
			
			assert.Empty(t, result.Address)
			assert.NotEmpty(t, result.Error)
			assert.Equal(t, tt.expected, result.Error)
		})
	}
}

func TestHandleReverseGeocode_MissingParameters(t *testing.T) {
	app := fiber.New()
	app.Get("/api/v1/geo/reverse", HandleReverseGeocode)
	
	req := httptest.NewRequest("GET", "/api/v1/geo/reverse", nil)
	
	resp, err := app.Test(req, fiber.TestConfig{Timeout: 10 * time.Second})
	require.NoError(t, err)
	
	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err)
	
	var result ReverseGeocodeResponse
	err = json.Unmarshal(body, &result)
	require.NoError(t, err)
	
	assert.Equal(t, fiber.StatusBadRequest, resp.StatusCode)
	assert.Empty(t, result.Address)
	assert.NotEmpty(t, result.Error)
	assert.Contains(t, result.Error, "Invalid request format")
}

func TestHandleReverseGeocode_MultipleWuhanLocations(t *testing.T) {
	app := fiber.New()
	app.Get("/api/v1/geo/reverse", HandleReverseGeocode)
	
	locations := []struct {
		name string
		lat  float64
		lng  float64
	}{
		{"Wuhan City Center", 30.5928, 114.3055},
		{"Wuchang District", 30.5417, 114.3162},
		{"Hankou", 30.5819, 114.2719},
		{"Hanyang", 30.5494, 114.2181},
		{"Random Wuhan Location 1", 0, 0},
		{"Random Wuhan Location 2", 0, 0},
		{"Random Wuhan Location 3", 0, 0},
	}
	
	for i := 4; i < len(locations); i++ {
		locations[i].lat, locations[i].lng = generateWuhanCoordinates()
	}
	
	for _, loc := range locations {
		t.Run(loc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/geo/reverse?latitude=%f&longitude=%f", loc.lat, loc.lng), nil)
			resp, err := app.Test(req, fiber.TestConfig{Timeout: 10 * time.Second})
			require.NoError(t, err)
			
			assert.Equal(t, fiber.StatusOK, resp.StatusCode)
			
			body, err := io.ReadAll(resp.Body)
			require.NoError(t, err)
			
			var result ReverseGeocodeResponse
			err = json.Unmarshal(body, &result)
			require.NoError(t, err)
			
			assert.NotEmpty(t, result.Address)
			assert.Empty(t, result.Error)
			assert.InDelta(t, loc.lat, result.Latitude, 0.000001)
			assert.InDelta(t, loc.lng, result.Longitude, 0.000001)
			
			t.Logf("%s - Coordinates: lat=%f, lng=%f", loc.name, loc.lat, loc.lng)
			t.Logf("Retrieved address: %s", result.Address)
			if result.AddressComponent != nil {
				t.Logf("Address components: %+v", result.AddressComponent)
			}
			if result.Name != "" {
				t.Logf("POI Name: %s", result.Name)
			}
		})
	}
}

func BenchmarkHandleReverseGeocode(b *testing.B) {
	app := fiber.New()
	app.Get("/api/v1/geo/reverse", HandleReverseGeocode)
	
	lat, lng := generateWuhanCoordinates()
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/geo/reverse?latitude=%f&longitude=%f", lat, lng), nil)
		resp, err := app.Test(req, fiber.TestConfig{Timeout: 10 * time.Second})
		if err != nil {
			b.Fatal(err)
		}
		resp.Body.Close()
	}
}