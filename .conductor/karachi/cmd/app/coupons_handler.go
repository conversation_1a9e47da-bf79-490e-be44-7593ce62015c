package main

import (
	"context"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/dfang/go-prettyjson"
	"github.com/gofiber/fiber/v3"
	"github.com/gofiber/fiber/v3/middleware/adaptor"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/services/merchantexclusivecoupon"

	"tandian-server/internal/config"
	"tandian-server/internal/pay"
	"tandian-server/internal/util/cloudbase"
)

// HandleCreateCoupons creates a new merchant exclusive coupon stock
// The request body should follow the WeChat Pay API structure

func HandleCreateCoupons(c fiber.Ctx) error {
	// 1. Parse request body using SDK's native type
	payload := new(merchantexclusivecoupon.CreateBusifavorStockRequest)
	if err := c.Bind().JSON(payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	prettyjson.Print(payload)

	// 2. Call the service function to create merchant coupon
	resp, err := pay.CreateMerchantCoupon(c.Context(), *payload)
	if err != nil {
		// Check if it's a validation error
		if strings.Contains(err.Error(), "validation failed") {
			return c.Status(http.StatusBadRequest).JSON(fiber.Map{
				"code":    -1,
				"message": "参数验证失败",
				"error":   err.Error(),
			})
		}

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "创建商家券失败",
			"error":   err.Error(),
		})
	}

	// 3. Build response data
	responseData := fiber.Map{
		"stock_id": resp.StockId,
	}

	// Add optional fields if available
	if resp.StockName != "" {
		responseData["name"] = resp.StockName
	}

	if resp.OutRequestNo != "" {
		responseData["out_request_no"] = resp.OutRequestNo
	}

	if resp.CouponCodeMode != "" {
		responseData["coupon_code_mode"] = resp.CouponCodeMode
	}

	if resp.AvailableBeginTime != nil {
		responseData["available_begin_time"] = *resp.AvailableBeginTime
	}

	if resp.AvailableEndTime != nil {
		responseData["available_end_time"] = *resp.AvailableEndTime
	}

	if resp.WaitDaysAfterReceive != nil {
		responseData["wait_days_after_receive"] = *resp.WaitDaysAfterReceive
	}

	if resp.AvailableDayAfterReceive != nil {
		responseData["available_day_after_receive"] = *resp.AvailableDayAfterReceive
	}

	if resp.CreateTime != nil {
		responseData["create_time"] = *resp.CreateTime
	}

	// 4. Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "创建商家券成功",
		"data":    responseData,
	})
}

func HandleSetCouponNotifyCallbacks(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		NotifyUrl string `json:"notify_url"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate notify URL
	if payload.NotifyUrl == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "notify_url is required",
		})
	}

	client := pay.NewWechatPayClient()
	ctx := c.Context()

	svc := merchantexclusivecoupon.CallBackApiService{Client: client.Client}
	resp, result, err := svc.SetCouponNotify(ctx,
		merchantexclusivecoupon.SetCouponNotifyRequest{
			Mchid:     core.String(client.MchID),
			NotifyUrl: core.String(payload.NotifyUrl),
		},
	)
	if err != nil {
		// 处理错误
		log.Printf("call SetCouponNotify err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "设置优惠券事件通知失败",
			"error":   err.Error(),
		})
	}

	// Check HTTP status code
	if result.Response.StatusCode != http.StatusOK && result.Response.StatusCode != http.StatusNoContent {
		log.Printf("SetCouponNotify unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "设置优惠券事件通知失败",
			"status":  result.Response.StatusCode,
		})
	}

	log.Printf("SetCouponNotify success: status=%d resp=%s", result.Response.StatusCode, resp)

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "设置优惠券事件通知成功",
		"data": fiber.Map{
			"notify_url":  payload.NotifyUrl,
			"update_time": resp.UpdateTime,
		},
	})
}

func HandleGetCouponNotifyCallbacks(c fiber.Ctx) error {
	// Call API using the pay package function
	resp, result, err := pay.GetCouponNotifyCallbacks(c.Context())
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询优惠券事件通知失败",
			"error":   err.Error(),
		})
	}

	// Check HTTP status code
	if result.Response.StatusCode != http.StatusOK {
		log.Printf("GetCouponNotify unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询优惠券事件通知失败",
			"status":  result.Response.StatusCode,
		})
	}

	log.Printf("GetCouponNotify success: status=%d resp=%s", result.Response.StatusCode, resp)

	// Build response data
	responseData := fiber.Map{}

	// Add notify URL if available
	if resp.NotifyUrl != nil {
		responseData["notify_url"] = *resp.NotifyUrl
	}

	// Add merchant ID if available
	if resp.Mchid != nil {
		responseData["mchid"] = *resp.Mchid
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "查询优惠券事件通知成功",
		"data":    responseData,
	})
}

// HandleModifyCouponBudgets 修改批次预算.
func HandleModifyCouponBudgets(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		StockId               string `json:"stock_id"`
		TargetMaxCoupons      int64  `json:"target_max_coupons"`
		CurrentMaxCoupons     int64  `json:"current_max_coupons"`
		ModifyBudgetRequestNo string `json:"modify_budget_request_no"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.StockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	if payload.TargetMaxCoupons <= 0 {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "target_max_coupons must be greater than 0",
		})
	}

	// Create WeChat Pay client
	client := pay.NewWechatPayClient()
	svc := merchantexclusivecoupon.BusiFavorApiService{Client: client.Client}

	// Build request
	req := merchantexclusivecoupon.ModifyBudgetRequest{
		StockId:               core.String(payload.StockId),
		TargetMaxCoupons:      core.Int64(payload.TargetMaxCoupons),
		CurrentMaxCoupons:     core.Int64(payload.CurrentMaxCoupons),
		ModifyBudgetRequestNo: core.String(payload.ModifyBudgetRequestNo),
	}

	// Call API
	resp, result, err := svc.ModifyBudget(c.Context(), req)
	if err != nil {
		log.Printf("call ModifyBudget err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "修改批次预算失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("ModifyBudget unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "修改批次预算失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "修改批次预算成功",
		"data": fiber.Map{
			"stock_id":    payload.StockId,
			"max_coupons": resp.MaxCoupons,
		},
	})
}

// HandleModifyStockInfo 修改商家券基本信息.
func HandleModifyStockInfo(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		StockId        string `json:"stock_id"`
		CustomEntrance *struct {
			MiniProgramsInfo *struct {
				MiniProgramsAppid string `json:"mini_programs_appid"`
				MiniProgramsPath  string `json:"mini_programs_path"`
				EntranceWords     string `json:"entrance_words"`
				GuidingWords      string `json:"guiding_words"`
			} `json:"mini_programs_info,omitempty"`
		} `json:"custom_entrance,omitempty"`
		StockName    string `json:"stock_name,omitempty"`
		Comment      string `json:"comment,omitempty"`
		GoodsName    string `json:"goods_name,omitempty"`
		OutRequestNo string `json:"out_request_no"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.StockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	if payload.OutRequestNo == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "out_request_no is required",
		})
	}

	// Create WeChat Pay client
	client := pay.NewWechatPayClient()
	svc := merchantexclusivecoupon.BusiFavorApiService{Client: client.Client}

	// Build request
	req := merchantexclusivecoupon.ModifyStockInfoRequest{
		StockId:      core.String(payload.StockId),
		OutRequestNo: core.String(payload.OutRequestNo),
	}

	// Add optional fields
	if payload.StockName != "" {
		req.StockName = core.String(payload.StockName)
	}

	if payload.Comment != "" {
		req.Comment = core.String(payload.Comment)
	}

	if payload.GoodsName != "" {
		req.GoodsName = core.String(payload.GoodsName)
	}

	// Add custom entrance if provided
	if payload.CustomEntrance != nil && payload.CustomEntrance.MiniProgramsInfo != nil {
		req.CustomEntrance = &merchantexclusivecoupon.ModifyCustomEntrance{
			MiniProgramsInfo: &merchantexclusivecoupon.ModifyMiniAppInfo{
				MiniProgramsAppid: core.String(payload.CustomEntrance.MiniProgramsInfo.MiniProgramsAppid),
				MiniProgramsPath:  core.String(payload.CustomEntrance.MiniProgramsInfo.MiniProgramsPath),
				EntranceWords:     core.String(payload.CustomEntrance.MiniProgramsInfo.EntranceWords),
				GuidingWords:      core.String(payload.CustomEntrance.MiniProgramsInfo.GuidingWords),
			},
		}
	}

	// Call API
	result, err := svc.ModifyStockInfo(c.Context(), req)
	if err != nil {
		log.Printf("call ModifyStockInfo err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "修改批次信息失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK && result.Response.StatusCode != http.StatusNoContent {
		log.Printf("ModifyStockInfo unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "修改批次信息失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "修改批次信息成功",
		"data": fiber.Map{
			"stock_id": payload.StockId,
		},
	})
}

// HandleQueryStock 查询批次详情.
func HandleQueryStock(c fiber.Ctx) error {
	// Get stock_id from query params
	stockId := c.Query("stock_id")
	if stockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	// Build request
	req := merchantexclusivecoupon.QueryStockRequest{
		StockId: core.String(stockId),
	}

	// Call API using the pay package function
	resp, result, err := pay.QueryStock(c.Context(), req)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询批次详情失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("QueryStock unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询批次详情失败",
			"status":  result.Response.StatusCode,
		})
	}

	// // Build response data
	// responseData := fiber.Map{
	// 	"stock_id":   resp.StockId,
	// 	"stock_name": resp.StockName,
	// }

	// // Add optional fields if available
	// if resp.Comment != nil {
	// 	responseData["comment"] = *resp.Comment
	// }

	// if resp.GoodsName != nil {
	// 	responseData["goods_name"] = *resp.GoodsName
	// }

	// // Add coupon use rule if available
	// if resp.CouponUseRule != nil {
	// 	useRule := fiber.Map{}
	// 	if resp.CouponUseRule.CouponAvailableTime != nil {
	// 		useRule["available_begin_time"] = resp.CouponUseRule.CouponAvailableTime.AvailableBeginTime
	// 		useRule["available_end_time"] = resp.CouponUseRule.CouponAvailableTime.AvailableEndTime
	// 	}

	// 	if resp.CouponUseRule.FixedNormalCoupon != nil {
	// 		useRule["discount_amount"] = resp.CouponUseRule.FixedNormalCoupon.DiscountAmount
	// 		useRule["transaction_minimum"] = resp.CouponUseRule.FixedNormalCoupon.TransactionMinimum
	// 	}

	// 	responseData["coupon_use_rule"] = useRule
	// }

	// // Add stock send rule if available
	// if resp.StockSendRule != nil {
	// 	sendRule := fiber.Map{
	// 		"max_coupons": resp.StockSendRule.MaxCoupons,
	// 	}
	// 	if resp.StockSendRule.MaxCouponsPerUser != nil {
	// 		sendRule["max_coupons_per_user"] = resp.StockSendRule.MaxCouponsPerUser
	// 	}

	// 	responseData["stock_send_rule"] = sendRule
	// }

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "查询批次详情成功",
		"data":    resp,
	})
}

// HandleListCouponsByFilter 条件查询批次下的券.
func HandleListCouponsByFilter(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		Openid          string `json:"openid"`
		Appid           string `json:"appid"`
		StockId         string `json:"stock_id,omitempty"`
		CreatorMerchant string `json:"creator_merchant,omitempty"`
		BelongMerchant  string `json:"belong_merchant,omitempty"`
		SenderMerchant  string `json:"sender_merchant,omitempty"`
		Offset          int64  `json:"offset,omitempty"`
		Limit           int64  `json:"limit,omitempty"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.Openid == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "openid is required",
		})
	}

	if payload.Appid == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "appid is required",
		})
	}

	// Build request
	req := merchantexclusivecoupon.ListCouponsByFilterRequest{
		Openid: core.String(payload.Openid),
		Appid:  core.String(payload.Appid),
	}

	// Add optional fields
	if payload.StockId != "" {
		req.StockId = core.String(payload.StockId)
	}

	if payload.CreatorMerchant != "" {
		req.CreatorMerchant = core.String(payload.CreatorMerchant)
	}

	if payload.BelongMerchant != "" {
		req.BelongMerchant = core.String(payload.BelongMerchant)
	}

	if payload.SenderMerchant != "" {
		req.SenderMerchant = core.String(payload.SenderMerchant)
	}

	if payload.Offset > 0 {
		req.Offset = core.Int64(payload.Offset)
	}

	if payload.Limit > 0 {
		req.Limit = core.Int64(payload.Limit)
	}

	// Call API using the pay package function
	resp, result, err := pay.ListCouponsByFilter(c.Context(), req)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询券列表失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("ListCouponsByFilter unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询券列表失败",
			"status":  result.Response.StatusCode,
		})
	}

	// // Build response data
	// responseData := fiber.Map{
	// 	"total_count": resp.TotalCount,
	// 	"offset":      resp.Offset,
	// 	"limit":       resp.Limit,
	// }

	// // Add coupon data if available
	// if resp.Data != nil {
	// 	coupons := []fiber.Map{}

	// 	for _, coupon := range resp.Data {
	// 		couponData := fiber.Map{
	// 			"coupon_code": coupon.CouponCode,
	// 		}

	// 		// Add optional fields
	// 		if coupon.StockId != nil {
	// 			couponData["stock_id"] = *coupon.StockId
	// 		}

	// 		if coupon.CouponState != nil {
	// 			couponData["status"] = *coupon.CouponState
	// 		}

	// 		if coupon.ReceiveTime != nil {
	// 			couponData["receive_time"] = *coupon.ReceiveTime
	// 		}

	// 		if coupon.AvailableStartTime != nil {
	// 			couponData["available_start_time"] = *coupon.AvailableStartTime
	// 		}

	// 		if coupon.ExpireTime != nil {
	// 			couponData["expire_time"] = *coupon.ExpireTime
	// 		}

	// 		coupons = append(coupons, couponData)
	// 	}

	// 	responseData["data"] = coupons
	// }

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "查询券列表成功",
		"data":    resp,
	})
}

// HandleSendCoupon 发放商家券.
func HandleSendCoupon(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		StockId      string `json:"stock_id"`
		Openid       string `json:"openid"`
		OutRequestNo string `json:"out_request_no"`
		Appid        string `json:"appid"`
		CouponCode   string `json:"coupon_code,omitempty"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.StockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	if payload.Openid == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "openid is required",
		})
	}

	if payload.OutRequestNo == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "out_request_no is required",
		})
	}

	if payload.Appid == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "appid is required",
		})
	}

	// Create WeChat Pay client
	client := pay.NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}

	// Build request
	req := merchantexclusivecoupon.SendCouponRequest{
		StockId:      core.String(payload.StockId),
		Openid:       core.String(payload.Openid),
		OutRequestNo: core.String(payload.OutRequestNo),
		Appid:        core.String(payload.Appid),
	}

	// Add optional fields
	if payload.CouponCode != "" {
		req.CouponCode = core.String(payload.CouponCode)
	}

	// Call API
	resp, result, err := svc.SendCoupon(c.Context(), req)
	if err != nil {
		log.Printf("call SendCoupon err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "发放商家券失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("SendCoupon unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "发放商家券失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{}

	if resp.SendCouponResult != nil {
		if resp.SendCouponResult.CouponCode != nil {
			responseData["coupon_code"] = *resp.SendCouponResult.CouponCode
		}

		if resp.SendCouponResult.StockId != nil {
			responseData["stock_id"] = *resp.SendCouponResult.StockId
		}

		if resp.SendCouponResult.OutRequestNo != nil {
			responseData["out_request_no"] = *resp.SendCouponResult.OutRequestNo
		}
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "发放商家券成功",
		"data":    responseData,
	})
}

// HandleUseCoupon 核销用户券.
func HandleUseCoupon(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		CouponCode string `json:"coupon_code"`
		StockId    string `json:"stock_id,omitempty"`
		Openid     string `json:"openid,omitempty"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.CouponCode == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "coupon_code is required",
		})
	}

	if payload.StockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	// Build request
	req := merchantexclusivecoupon.UseCouponRequest{
		CouponCode: core.String(payload.CouponCode),
		Openid:     core.String(payload.Openid),
		StockId:    core.String(payload.StockId),
	}

	// Call API
	resp, result, err := pay.UseCoupon(c.Context(), req)
	if err != nil {
		log.Printf("call UseCoupon err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "核销用户券失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("UseCoupon unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "核销用户券失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{}
	if resp.StockId != nil {
		responseData["stock_id"] = *resp.StockId
	}

	if resp.Openid != nil {
		responseData["openid"] = *resp.Openid
	}

	if resp.WechatpayUseTime != nil {
		responseData["wechatpay_use_time"] = *resp.WechatpayUseTime
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "核销用户券成功",
		"data":    responseData,
	})
}

// HandleQueryCoupon 查询用户券详情.
func HandleQueryCoupon(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		CouponCode string `json:"coupon_code"`
		Openid     string `json:"openid"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.CouponCode == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "coupon_code is required",
		})
	}

	if payload.Openid == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "openid is required",
		})
	}

	// Build request
	req := merchantexclusivecoupon.QueryCouponRequest{
		CouponCode: core.String(payload.CouponCode),
		Openid:     core.String(payload.Openid),
	}

	// Call API using the pay package function
	resp, result, err := pay.QueryCoupon(c.Context(), req)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询用户券详情失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("QueryCoupon unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询用户券详情失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{
		"coupon_code": resp.CouponCode,
		"stock_id":    resp.StockId,
	}

	// Add optional fields
	if resp.CouponState != nil {
		responseData["status"] = *resp.CouponState
	}

	if resp.ReceiveTime != nil {
		responseData["receive_time"] = *resp.ReceiveTime
	}

	if resp.AvailableStartTime != nil {
		responseData["available_start_time"] = *resp.AvailableStartTime
	}

	if resp.ExpireTime != nil {
		responseData["expire_time"] = *resp.ExpireTime
	}

	if resp.CouponUseRule != nil {
		useRule := fiber.Map{}
		if resp.CouponUseRule.CouponAvailableTime != nil {
			useRule["available_begin_time"] = resp.CouponUseRule.CouponAvailableTime.AvailableBeginTime
			useRule["available_end_time"] = resp.CouponUseRule.CouponAvailableTime.AvailableEndTime
		}

		if resp.CouponUseRule.FixedNormalCoupon != nil {
			useRule["discount_amount"] = resp.CouponUseRule.FixedNormalCoupon.DiscountAmount
			useRule["transaction_minimum"] = resp.CouponUseRule.FixedNormalCoupon.TransactionMinimum
		}

		responseData["coupon_use_rule"] = useRule
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "查询用户券详情成功",
		"data":    responseData,
	})
}

// HandleReturnCoupon 返还用户券.
func HandleReturnCoupon(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		CouponCode      string `json:"coupon_code"`
		StockId         string `json:"stock_id"`
		ReturnRequestNo string `json:"return_request_no"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.CouponCode == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "coupon_code is required",
		})
	}

	if payload.StockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	// Build request (ReturnRequestNo will be generated automatically if not provided)
	req := merchantexclusivecoupon.ReturnCouponRequest{
		CouponCode: core.String(payload.CouponCode),
		StockId:    core.String(payload.StockId),
	}

	// Only set ReturnRequestNo if provided
	if payload.ReturnRequestNo != "" {
		req.ReturnRequestNo = core.String(payload.ReturnRequestNo)
	}

	// Call API using the pay package function
	resp, result, err := pay.ReturnCoupon(c.Context(), req)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "返还用户券失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("ReturnCoupon unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "返还用户券失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{}
	if resp.WechatpayReturnTime != nil {
		responseData["wechatpay_return_time"] = *resp.WechatpayReturnTime
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "返还用户券成功",
		"data":    responseData,
	})
}

// HandleDeactivateCoupon 使券失效.
func HandleDeactivateCoupon(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		CouponCode       string `json:"coupon_code"`
		StockId          string `json:"stock_id"`
		DeactivateReason string `json:"deactivate_reason,omitempty"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.CouponCode == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "coupon_code is required",
		})
	}

	if payload.StockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	// Build request
	req := merchantexclusivecoupon.DeactivateCouponRequest{
		CouponCode: core.String(payload.CouponCode),
		StockId:    core.String(payload.StockId),
	}

	// Add optional fields
	if payload.DeactivateReason != "" {
		req.DeactivateReason = core.String(payload.DeactivateReason)
	}

	// Call API
	resp, result, err := pay.DeactivateCoupon(c.Context(), req)
	if err != nil {
		log.Printf("call DeactivateCoupon err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "使券失效失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("DeactivateCoupon unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "使券失效失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{}
	if resp.WechatpayDeactivateTime != nil {
		responseData["wechatpay_deactivate_time"] = *resp.WechatpayDeactivateTime
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "使券失效成功",
		"data":    responseData,
	})
}

// HandleAssociateTradeInfo 关联订单信息.
func HandleAssociateTradeInfo(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		StockId      string `json:"stock_id"`
		CouponCode   string `json:"coupon_code"`
		OutTradeNo   string `json:"out_trade_no"`
		OutRequestNo string `json:"out_request_no"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.StockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	if payload.CouponCode == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "coupon_code is required",
		})
	}

	if payload.OutTradeNo == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "out_trade_no is required",
		})
	}

	if payload.OutRequestNo == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "out_request_no is required",
		})
	}

	// Create WeChat Pay client
	client := pay.NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}

	// Build request
	req := merchantexclusivecoupon.AssociateTradeInfoRequest{
		StockId:      core.String(payload.StockId),
		CouponCode:   core.String(payload.CouponCode),
		OutTradeNo:   core.String(payload.OutTradeNo),
		OutRequestNo: core.String(payload.OutRequestNo),
	}

	// Call API
	resp, result, err := svc.AssociateTradeInfo(c.Context(), req)
	if err != nil {
		log.Printf("call AssociateTradeInfo err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "关联订单信息失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("AssociateTradeInfo unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "关联订单信息失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{}
	if resp.WechatpayAssociateTime != nil {
		responseData["wechatpay_associate_time"] = *resp.WechatpayAssociateTime
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "关联订单信息成功",
		"data":    responseData,
	})
}

// HandleDisassociateTradeInfo 取消关联订单信息.
func HandleDisassociateTradeInfo(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		StockId      string `json:"stock_id"`
		CouponCode   string `json:"coupon_code"`
		OutTradeNo   string `json:"out_trade_no"`
		OutRequestNo string `json:"out_request_no"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.StockId == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "stock_id is required",
		})
	}

	if payload.CouponCode == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "coupon_code is required",
		})
	}

	if payload.OutTradeNo == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "out_trade_no is required",
		})
	}

	if payload.OutRequestNo == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "out_request_no is required",
		})
	}

	// Create WeChat Pay client
	client := pay.NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}

	// Build request
	req := merchantexclusivecoupon.DisassociateTradeInfoRequest{
		StockId:      core.String(payload.StockId),
		CouponCode:   core.String(payload.CouponCode),
		OutTradeNo:   core.String(payload.OutTradeNo),
		OutRequestNo: core.String(payload.OutRequestNo),
	}

	// Call API
	resp, result, err := svc.DisassociateTradeInfo(c.Context(), req)
	if err != nil {
		log.Printf("call DisassociateTradeInfo err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "取消关联订单信息失败",
			"error":   err.Error(),
		})
	}

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("DisassociateTradeInfo unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "取消关联订单信息失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{}
	if resp.WechatpayDisassociateTime != nil {
		responseData["wechatpay_disassociate_time"] = *resp.WechatpayDisassociateTime
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    0,
		"message": "取消关联订单信息成功",
		"data":    responseData,
	})
}

// HandleCouponsNotifyCallback 处理商家券事件通知回调.
func HandleCouponsNotifyCallback(c fiber.Ctx) error {
	// Log the raw request for debugging
	body := c.Body()
	log.Printf("Received coupon notify callback raw body: %s\n", string(body))

	// Parse the notification request
	payload := new(notify.Request)
	if err := c.Bind().JSON(payload); err != nil {
		log.Printf("Failed to parse coupon notification request: %v", err)

		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    "FAIL",
			"message": "请求参数解析失败",
		})
	}

	// Get context
	ctx := c.Context()

	// Convert fiber request to http request for notification verification
	req, err := adaptor.ConvertRequest(c, true)
	if err != nil {
		log.Printf("Failed to convert fiber request to http request: %v", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    "FAIL",
			"message": "内部服务器错误",
		})
	}

	// Get WechatPay config
	wechatPayConfig := config.AppConfig.WechatPay

	// Initialize the notification handler
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(wechatPayConfig.MchID)
	verifier := verifiers.NewSHA256WithRSAVerifier(certificateVisitor)

	handler, err := notify.NewRSANotifyHandler(wechatPayConfig.MchAPIv3Key, verifier)
	if err != nil {
		log.Printf("Failed to create notify handler: %v", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    "FAIL",
			"message": "内部服务器错误",
		})
	}

	// Parse and decrypt the notification based on event type
	// Merchant exclusive coupon events include:
	// - COUPON.SEND: 发放通知
	// - COUPON.USE: 核销通知
	// - COUPON.REFUND: 退款通知
	// Using generic map structure as SDK doesn't define specific types for coupon notifications
	var content map[string]interface{}

	// Parse the notification
	notifyReq, err := handler.ParseNotifyRequest(ctx, req, &content)
	if err != nil {
		log.Printf("Failed to parse coupon notification: %v", err)

		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    "FAIL",
			"message": "通知解析失败",
		})
	}

	log.Printf("Coupon notification - EventType: %s, RequestID: %s, Content: %+v",
		payload.EventType, notifyReq.ID, content)

	// Handle notification based on event type
	switch payload.EventType {
	case "COUPON.SEND":
		// Handle coupon send notification
		if err := handleCouponSendNotification(ctx, content); err != nil {
			log.Printf("Failed to handle coupon send notification: %v", err)

			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"code":    "FAIL",
				"message": "处理失败",
			})
		}

	case "COUPON.USE":
		// Handle coupon use notification
		if err := handleCouponUseNotification(ctx, content); err != nil {
			log.Printf("Failed to handle coupon use notification: %v", err)

			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"code":    "FAIL",
				"message": "处理失败",
			})
		}

	case "COUPON.REFUND":
		// Handle coupon refund notification
		if err := handleCouponRefundNotification(ctx, content); err != nil {
			log.Printf("Failed to handle coupon refund notification: %v", err)

			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"code":    "FAIL",
				"message": "处理失败",
			})
		}

	default:
		log.Printf("Unknown coupon event type: %s", payload.EventType)

		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    "FAIL",
			"message": "未知的事件类型",
		})
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code": "SUCCESS",
	})
}

// handleCouponSendNotification processes coupon send notifications.
func handleCouponSendNotification(ctx context.Context, data map[string]interface{}) error {
	// Extract common fields from notification data
	stockId, _ := data["stock_id"].(string)
	couponCode, _ := data["coupon_code"].(string)
	openid, _ := data["openid"].(string)
	appid, _ := data["appid"].(string)
	sendTime, _ := data["send_time"].(string)

	// Log the coupon send event
	log.Printf("Processing coupon send - StockId: %s, CouponCode: %s, OpenId: %s, AppId: %s, SendTime: %s",
		stockId, couponCode, openid, appid, sendTime)

	// TODO: Implement business logic for coupon send notification
	// Examples:
	// - Save coupon send record to database
	// - Update user's coupon list
	// - Send notification to user
	// - Update statistics

	// For now, just log the event
	log.Printf("Coupon sent successfully - StockId: %s, CouponCode: %s",
		stockId, couponCode)

	return nil
}

// handleCouponUseNotification processes coupon use notifications.
func handleCouponUseNotification(ctx context.Context, data map[string]interface{}) error {
	// Extract common fields from notification data
	stockId, _ := data["stock_id"].(string)
	couponCode, _ := data["coupon_code"].(string)
	openid, _ := data["openid"].(string)
	useTime, _ := data["use_time"].(string)
	transactionId, _ := data["transaction_id"].(string)

	// Log the coupon use event
	log.Printf("Processing coupon use - StockId: %s, CouponCode: %s, OpenId: %s, TransactionId: %s, UseTime: %s",
		stockId, couponCode, openid, transactionId, useTime)

	// TODO: Implement business logic for coupon use notification
	// Examples:
	// - Update coupon status in database
	// - Record usage history
	// - Update statistics
	// - Process any related business logic

	// For now, just log the event
	log.Printf("Coupon used successfully - StockId: %s, CouponCode: %s",
		stockId, couponCode)

	return nil
}

// handleCouponRefundNotification processes coupon refund notifications.
func handleCouponRefundNotification(ctx context.Context, data map[string]interface{}) error {
	// Extract common fields from notification data
	stockId, _ := data["stock_id"].(string)
	couponCode, _ := data["coupon_code"].(string)
	openid, _ := data["openid"].(string)
	refundId, _ := data["refund_id"].(string)
	refundTime, _ := data["refund_time"].(string)

	// Log the coupon refund event
	log.Printf("Processing coupon refund - StockId: %s, CouponCode: %s, OpenId: %s, RefundId: %s, RefundTime: %s",
		stockId, couponCode, openid, refundId, refundTime)

	// TODO: Implement business logic for coupon refund notification
	// Examples:
	// - Update coupon status to refunded
	// - Process refund in database
	// - Update user's coupon list
	// - Update statistics

	// For now, just log the event
	log.Printf("Coupon refunded successfully - StockId: %s, CouponCode: %s",
		stockId, couponCode)

	return nil
}

// 根据KEY获取可用的活动（领券）.
func HandleFetchAvailableCompaigns(c fiber.Ctx) error {
	ctx := c.Context()

	// Parse request body
	var payload struct {
		UserID string `json:"user_id"`
		OpenID string `json:"openid"`
		AppID  string `json:"appid"`
		KEY    string `json:"key"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	if payload.KEY == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "key is required",
		})
	}

	// Validate required fields
	if payload.UserID == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "user_id or openid is required",
		})
	}
	payload.AppID = config.AppConfig.WechatPay.AppID

	// 临时修复（因为前端没有传openid，已经加了，需要发版)
	if payload.OpenID == "" {
		userData, _ := getUserInfo(ctx, payload.UserID)
		if len(userData) > 0 {
			payload.OpenID = userData["openid"].(string)
		}
	}

	prettyjson.Printf("/api/v1/coupon/fetch_available_compaigns payload: %s\n", payload)

	// 1. Check if user has placed a tandian order
	hasTandianOrder, err := CheckUserHasTandianOrder(ctx, payload.OpenID, payload.UserID)
	if err != nil {
		log.Printf("Failed to check user tandian order: %v", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询用户订单失败",
			"error":   err.Error(),
		})
	}

	if hasTandianOrder {
		return c.Status(http.StatusOK).JSON(fiber.Map{
			"code":    -1,
			"message": "不是新用户，没有迎新券",
		})
	}

	stocksForWelcomeNewUser, err := QueryCompaignsByKey(payload.KEY)
	prettyjson.Printf("coupons queried from database: %s\n", stocksForWelcomeNewUser)

	if err != nil {
		return c.Status(http.StatusOK).JSON(fiber.Map{
			"code":    -1,
			"message": "查询可用探店券失败",
			"error":   err.Error(),
		})
	}

	// Check which coupons the user has already received (only include available stocks)
	availableCoupons := filterOutAlreayReceivedStocks(ctx, stocksForWelcomeNewUser, payload.OpenID, payload.AppID)
	if len(availableCoupons) == 0 {
		log.Printf("No available coupons for user %s (all stocks already received)", payload.OpenID)
		return c.Status(http.StatusOK).JSON(fiber.Map{
			"code":    -1,
			"message": "无可领优惠券，也有可能已经领过了",
		})
	}

	// // Fallback to hardcoded values if no campaigns found
	// if len(couponsForUser) == 0 {
	// 	log.Printf("No campaigns found in database, using fallback stock IDs")
	// 	couponsForUser = []string{
	// 		"1219460000000014",
	// 		"1219460000000015",
	// 	}
	// }

	// Build coupon list with generated out_request_no
	coupons := []fiber.Map{}
	for _, stockID := range availableCoupons {
		coupons = append(coupons, fiber.Map{
			"stock_id":       stockID,
			"out_request_no": _generate_out_request_no(stockID),
		})
	}

	// Prepare send coupon parameters for signature
	// According to the doc, when sending multiple coupons, parameters use index notation
	send_coupon_params := map[string]interface{}{
		"send_coupon_merchant": config.AppConfig.WechatPay.MchID,
	}

	// Add stock_id and out_request_no for each available stock with index
	if len(coupons) > 0 {
		for i := range coupons {
			send_coupon_params["stock_id"+strconv.Itoa(i)] = coupons[i]["stock_id"]
			send_coupon_params["out_request_no"+strconv.Itoa(i)] = coupons[i]["out_request_no"]
		}
	}

	// Log parameters for debugging
	log.Printf("Send coupon params for signature: %+v", send_coupon_params)

	// 计算签名
	sign, err := calcSign(send_coupon_params)
	if err != nil {
		return c.Status(http.StatusOK).JSON(fiber.Map{
			"code":    -1,
			"message": "获取可用券时签名失败",
		})
	}

	// 4. Return available coupons with plugin initialization data
	pluginData := fiber.Map{
		"has_tandian_order": hasTandianOrder,
		"coupons":           coupons,
		"sign":              sign, // HMAC-SHA256 signature
	}

	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "获取可用券成功",
		"data":    pluginData,
	})
}

func _generate_out_request_no(stock_id string) string {
	// Generate 6 random alphanumeric characters using crypto/rand for security
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	randomBytes := make([]byte, 6)

	// Use crypto/rand for secure random generation
	if _, err := rand.Read(randomBytes); err != nil {
		// Fallback to timestamp-based generation if crypto/rand fails
		log.Printf("Warning: crypto/rand failed, using fallback: %v", err)

		for i := range randomBytes {
			randomBytes[i] = charset[i%len(charset)]
		}
	} else {
		// Map random bytes to charset
		for i := range randomBytes {
			randomBytes[i] = charset[randomBytes[i]%byte(len(charset))]
		}
	}

	return fmt.Sprintf(
		"%s_%s_%d_%s",
		config.AppConfig.WechatPay.MchID,
		stock_id,
		time.Now().Unix(),
		string(randomBytes),
	)
}

// calcSign calculates signature for WeChat Pay merchant exclusive coupon plugin
// The plugin uses HMAC-SHA256 according to the documentation
// Parameters should be sorted alphabetically and joined with &
// The key is the APIv2 signkey from WeChat merchant platform.
func calcSign(params map[string]interface{}) (string, error) {
	// Step 1: Sort parameter names alphabetically
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}

	sort.Strings(keys)

	// Step 2: Build the string to sign
	var parts []string

	for _, k := range keys {
		// Skip empty values
		v := fmt.Sprintf("%v", params[k])
		if v != "" {
			parts = append(parts, fmt.Sprintf("%s=%s", k, v))
		}
	}

	// Step 3: Get the API key
	// The merchant exclusive coupon plugin uses V2 API key
	apiKey := config.AppConfig.WechatPay.MchAPIv2Key
	if apiKey == "" {
		return "", errors.New("api v2 key is empty")
	}

	parts = append(parts, fmt.Sprintf("key=%s", apiKey))

	// Step 4: Join all parts with &
	stringToSign := strings.Join(parts, "&")

	// Removed sensitive logging - was logging API key in stringToSign
	// Only log in development if needed:
	// if config.AppConfig.AppEnv == "development" {
	//     log.Printf("Generating signature for params count: %d", len(params))
	// }

	// Step 5: Calculate HMAC-SHA256 (merchant exclusive coupon plugin uses HMAC-SHA256)
	h := hmac.New(sha256.New, []byte(apiKey))
	h.Write([]byte(stringToSign))
	signature := hex.EncodeToString(h.Sum(nil))

	// Step 6: Convert to uppercase
	result := strings.ToUpper(signature)
	log.Printf("Generated signature: %s", result)

	return result, nil
}

func QueryCompaignsByKey(key string) ([]string, error) {
	// Query database for available campaigns
	campaignsQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"available": map[string]interface{}{
					"$eq": true, // Only get active campaigns
				},
				"key": map[string]interface{}{
					"$eq": key, // Filter by
				},
			},
		},
		"limit":    4, // Get up to 100 active campaigns
		"getCount": true,
	}
	prettyjson.Print(campaignsQuery)

	// Fetch campaigns from database
	campaignsResult, err := cloudbase.GetItems(context.Background(), "campaigns", campaignsQuery)
	if err != nil {
		log.Printf("Failed to fetch campaigns from database: %v", err)
		// Continue with fallback hardcoded values if database query fails
	}

	// Build coupon list from campaigns or use fallback
	var couponsForUser []string

	prettyjson.Print(campaignsResult)

	if len(campaignsResult["records"].([]interface{})) > 0 {
		// Extract stock_ids from campaigns based on user eligibility
		for _, campaign := range campaignsResult["records"].([]interface{}) {
			if campaignMap, ok := campaign.(map[string]interface{}); ok {
				// // Check if campaign is for new users only
				// isNewUserOnly, _ := campaignMap["new_users_only"].(bool)
				// if isNewUserOnly {
				// 	continue // Skip this campaign if it's for new users only and user has orders
				// }
				// Get stock_id from campaign
				if stockId, ok := campaignMap["stock_id"].(string); ok && stockId != "" {
					couponsForUser = append(couponsForUser, stockId)
				}
			}
		}
	}

	return couponsForUser, nil
}

func filterOutAlreayReceivedStocks(ctx context.Context, welcomNewUserCoupons []string, openid, appid string) []string {
	availableStocks := []string{}

	// Create WeChat Pay client once for all queries
	client := pay.NewWechatPayClient()
	svc := merchantexclusivecoupon.CouponApiService{Client: client.Client}

	for _, stockID := range welcomNewUserCoupons {
		// Query if user already has any coupons from this stock
		req := merchantexclusivecoupon.ListCouponsByFilterRequest{
			Openid:         core.String(openid),
			Appid:          core.String(appid),
			StockId:        core.String(stockID),
			BelongMerchant: core.String(config.AppConfig.WechatPay.MchID),
			Limit:          core.Int64(1), // We only need to know if at least one exists
		}
		prettyjson.Printf("req: %s\n", req)

		// Check if user has any coupons from this stock
		resp, result, err := svc.ListCouponsByFilter(ctx, req)
		prettyjson.Printf("resp: %s\n", resp)
		prettyjson.Printf("result: %s\n", result)

		if err != nil || result.Response.StatusCode != http.StatusOK {
			// Error querying, log and skip this stock
			log.Printf("Error querying coupons for stock %s: %v", stockID, err)
			continue
		}
		prettyjson.Printf("resp: %s\n", resp)

		// If no coupons found from this stock, it's available to receive
		if resp.TotalCount == nil || *resp.TotalCount == 0 {
			availableStocks = append(availableStocks, stockID)
			log.Printf("Stock %s is available for user %s (no existing coupons)", stockID, openid)
		} else {
			// User already has coupon(s) from this stock
			log.Printf("User %s already has %d coupon(s) from stock %s", openid, *resp.TotalCount, stockID)
		}
	}

	return availableStocks
}

// 探店订单确认页面 获取当前可用的全返券.
func HandleFetchAvailableCouponsWhenCheckout(c fiber.Ctx) error {
	// Parse request body
	var payload struct {
		Openid          string  `json:"openid"`
		ThresholdAmount float64 `json:"threshold_amount"`
		CouponState     string  `json:"coupon_state"`
		// Appid           string `json:"appid"`
		// StockId         string `json:"stock_id,omitempty"`
		// CreatorMerchant string `json:"creator_merchant,omitempty"`
		// BelongMerchant  string `json:"belong_merchant,omitempty"`
		// SenderMerchant  string `json:"sender_merchant,omitempty"`
		// Offset          int64  `json:"offset,omitempty"`
		// Limit           int64  `json:"limit,omitempty"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}
	// Validate required fields
	if payload.Openid == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "openid is required",
		})
	}

	req := new(merchantexclusivecoupon.ListCouponsByFilterRequest)
	req.Appid = core.String(config.AppConfig.WechatPay.AppID)
	req.BelongMerchant = core.String(config.AppConfig.WechatPay.MchID)
	req.Openid = core.String(payload.Openid)
	req.BelongMerchant = core.String(config.AppConfig.WechatPay.MchID)

	if payload.CouponState == "" {
		req.CouponState = (*merchantexclusivecoupon.CouponStatus)(core.String("SENDED"))
	}

	req.Limit = core.Int64(30) // 最大只允许30

	// Call API using the pay package function
	resp, result, err := pay.ListCouponsByFilter(c.Context(), *req)
	if err != nil {
		log.Printf("call ListCouponsByFilter err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询券列表失败",
			"error":   err.Error(),
		})
	}

	prettyjson.Print(resp)

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("ListCouponsByFilter unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询券列表失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{
		"total_count": resp.TotalCount,
		"offset":      resp.Offset,
		"limit":       resp.Limit,
	}

	// Add coupon data if available
	if resp.Data != nil {
		coupons := []fiber.Map{}

		for _, coupon := range resp.Data {
			// 大于原价就可以用券
			if *coupon.CouponUseRule.DiscountCoupon.TransactionMinimum >= int64(payload.ThresholdAmount*100) {
				couponData := fiber.Map{
					"coupon_code":          coupon.CouponCode,
					"stock_type":           coupon.StockType,
					"stock_name":           coupon.StockName,
					"stock_id":             coupon.StockId,
					"goods_name":           coupon.GoodsName,
					"coupon_state":         coupon.CouponState,
					"receive_time":         coupon.ReceiveTime,
					"available_start_time": coupon.CouponUseRule.CouponAvailableTime.AvailableBeginTime,
					"available_end_time":   coupon.CouponUseRule.CouponAvailableTime.AvailableEndTime,
					"expire_time":          coupon.ExpireTime,
					"discount_percent":     coupon.CouponUseRule.DiscountCoupon.DiscountPercent,
					"discount_amount":      coupon.CouponUseRule.DiscountCoupon.TransactionMinimum,
				}
				coupons = append(coupons, couponData)
			}
		}

		responseData["data"] = coupons
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "查询券列表成功",
		"data":    responseData,
	})
}

// 领券中心展示 可领优惠券.
func HandleFetchAvailableCouponsInCouponsCenter(c fiber.Ctx) error {
	payload := new(merchantexclusivecoupon.ListCouponsByFilterRequest)
	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	// Validate required fields
	if payload.Openid == nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "openid is required",
		})
	}

	if payload.Appid == nil {
		payload.Appid = core.String(config.AppConfig.WechatPay.AppID)
	}

	if payload.BelongMerchant == nil {
		payload.BelongMerchant = core.String(config.AppConfig.WechatPay.MchID)
	}

	if payload.CouponState == nil {
		payload.CouponState = (*merchantexclusivecoupon.CouponStatus)(core.String("SENDED"))
	}

	// Call API using the pay package function
	resp, result, err := pay.ListCouponsByFilter(c.Context(), *payload)
	if err != nil {
		log.Printf("call ListCouponsByFilter err:%s", err)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询券列表失败",
			"error":   err.Error(),
		})
	}

	prettyjson.Print(resp)

	if result.Response.StatusCode != http.StatusOK {
		log.Printf("ListCouponsByFilter unexpected status=%d", result.Response.StatusCode)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": "查询券列表失败",
			"status":  result.Response.StatusCode,
		})
	}

	// Build response data
	responseData := fiber.Map{
		"total_count": resp.TotalCount,
		"offset":      resp.Offset,
		"limit":       resp.Limit,
	}

	// Add coupon data if available
	if resp.Data != nil {
		coupons := []fiber.Map{}

		for _, coupon := range resp.Data {
			couponData := fiber.Map{
				"coupon_code":          coupon.CouponCode,
				"stock_type":           coupon.StockType,
				"stock_name":           coupon.StockName,
				"stock_id":             coupon.StockId,
				"goods_name":           coupon.GoodsName,
				"coupon_state":         coupon.CouponState,
				"receive_time":         coupon.ReceiveTime,
				"available_start_time": coupon.CouponUseRule.CouponAvailableTime.AvailableBeginTime,
				"available_end_time":   coupon.CouponUseRule.CouponAvailableTime.AvailableEndTime,
				"expire_time":          coupon.ExpireTime,
				"discount_percent":     coupon.CouponUseRule.DiscountCoupon.DiscountPercent,
				"discount_amount":      coupon.CouponUseRule.DiscountCoupon.TransactionMinimum,
			}
			coupons = append(coupons, couponData)
		}

		responseData["data"] = coupons
	}

	// Return success response
	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "查询券列表成功",
		"data":    responseData,
	})
}

func getUserInfo(ctx context.Context, useID string) (map[string]interface{}, error) {
	userQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"_id": map[string]interface{}{
					"$eq": useID,
				},
			},
		},
	}
	userData, err := cloudbase.GetItem(ctx, "users", userQuery)
	if err == nil && len(userData) > 0 {
		return userData, nil
	}

	return nil, errors.New("user not found")
}
