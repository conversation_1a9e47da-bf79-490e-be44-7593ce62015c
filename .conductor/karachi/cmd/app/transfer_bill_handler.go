package main

import (
	"context"
	"fmt"
	"log"
	"net/http"

	"github.com/Oudwins/zog"
	"github.com/dfang/go-prettyjson"
	"github.com/gofiber/fiber/v3"
	"github.com/gofiber/fiber/v3/middleware/adaptor"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"

	"tandian-server/internal/config"
	notifyService "tandian-server/internal/notify"
	"tandian-server/internal/pay"
)

func PreTransferWithAuthorizationNotifyUrl(c fiber.Ctx) error {
	// TODO: print request body
	fmt.Println(string(c.Body()))

	return nil
}

func AuthorizationNotifyUrl(c fiber.Ctx) error {
	// Get WechatPay config
	wechatPayConfig := config.AppConfig.WechatPay

	// 1. Initialize the notification handler
	ctx := context.Background()
	// Get the certificate visitor
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(wechatPayConfig.MchID)
	// Create the verifier
	verifier := verifiers.NewSHA256WithRSAVerifier(certificateVisitor)

	handler, err := notify.NewRSANotifyHandler(wechatPayConfig.MchAPIv3Key, verifier)
	if err != nil {
		log.Printf("Failed to create notify handler: %v", err)
		return c.Status(http.StatusInternalServerError).
			JSON(fiber.Map{"code": "FAIL", "message": "Internal server error"})
	}

	// 2. Parse the notification
	notification := make(map[string]interface{})
	req, err := adaptor.ConvertRequest(c, true)
	if err != nil {
		log.Printf("Failed to convert fiber request to http request: %v", err)
		return c.Status(http.StatusInternalServerError).
			JSON(fiber.Map{"code": "FAIL", "message": "Internal server error"})
	}

	_, err = handler.ParseNotifyRequest(ctx, req, &notification)
	if err != nil {
		log.Printf("Failed to parse notification: %v", err)
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{"code": "FAIL", "message": "Bad request"})
	}

	// 3. Process the notification
	// TODO: Implement your business logic here, e.g., update database
	log.Printf("Received authorization notification: %+v", notification)

	// 4. Return a success response
	return c.Status(http.StatusOK).JSON(fiber.Map{"code": "SUCCESS", "message": "Success"})
}

func init() {
	// Random seeding is automatic in Go 1.20+
	// No longer needed to manually seed
}

type TransferSceneReportInfo struct {
	InfoType    string `json:"info_type"`
	InfoContent string `json:"info_content"`
}

type CreateTransferBillRequest struct {
	UserID         string  `json:"user_id"         zog:"user_id"`
	OpenID         string  `json:"openid"          zog:"openid"`
	TransferAmount float64 `json:"transfer_amount" zog:"transfer_amount"`
	OutBillNo      string  `json:"out_bill_no"     zog:"out_bill_no"`
}

var CreateTransferBillRequestSchema = zog.Struct(
	zog.Shape{
		// its very important that schema keys like "name" match the struct field name NOT the input data
		"UserID": zog.String().Required(zog.Message("发起提现用户ID is required")),
		"OpenID": zog.String().Required(zog.Message("发起提现OpenID is required")),
		"TransferAmount": zog.Float64().
			Required().
			GTE(0.3, zog.Message("单次提现金额不能小于0.3元")).
			LTE(200, zog.Message("单次提现金额不能超过200元")),
		"OutBillNo": zog.String().Optional(), // generate in the backend now
	})

// HandleCreateTransferBill 创建转帐单.
func HandleCreateTransferBill(c fiber.Ctx) error {
	payload := new(CreateTransferBillRequest)
	if err := c.Bind().JSON(payload); err != nil {
		return err
	}

	fmt.Printf("%+v\n", payload)

	s, _ := prettyjson.Marshal(payload)
	fmt.Println("prettyjson:", string(s))

	// todo: Validate req coming from frontend first
	errsMap := CreateTransferBillRequestSchema.Validate(payload)
	if errsMap != nil {
		sanitized := zog.Issues.SanitizeMap(errsMap)

		return c.JSON(fiber.Map{
			"code":    -1,
			"message": sanitized,
		})
	}

	// TODO: 验证用户是否有被禁止提现
	// TODO: 验证提现次数
	// if b := pay.CheckIfUserAuditedForWithdrawal(payload.OpenID, payload.UserID); !b {
	// 	return c.Status(http.StatusInternalServerError).
	// 		JSON(fiber.Map{
	// 			"code":    -1,
	// 			"message": "用户账目不对，暂时禁止提现功能",
	// 		})
	// }

	// Create Transfer Bill
	payload.OutBillNo = pay.GenerateOutBillNo()
	req := pay.CreateTransferBillRequest{
		AppID:              "wxf77ccd1007594f39",
		OutBillNo:          payload.OutBillNo,
		OpenID:             payload.OpenID,
		Username:           "",
		TransferSceneID:    "1005",
		TransferAmount:     int64(payload.TransferAmount * 100),
		TransferRemark:     "余额提现",
		NotifyUrl:          "https://wx.dagexian.com/wx/notify_callback",
		UserRecvPerception: "劳务报酬",
		TransferSceneReportInfos: []pay.TransferSceneReportInfo{
			{
				InfoType:    "岗位类型",
				InfoContent: "探店达人",
			},
			{
				InfoType:    "报酬说明",
				InfoContent: "笔记返现",
			},
		},
	}

	client := pay.NewWechatPayClient()

	resp, err := client.CreateTransferBill(c.Context(), req)
	if err != nil {
		// log.Println(err)
		payload := notifyService.Payload{
			Msgtype: "text",
			Text: &notifyService.Text{
				Content: err.Error(),
			},
		}
		notifyService.SendNotEnoughAlert(payload)

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"error":   err.Error(),
			"message": "创建转账单失败",
		})
	}

	fmt.Printf("created transfer bill resp %+v\n", resp)

	// Create Pending Withdrawals and Transactions Record
	req1 := pay.CreatePendingWithdrawalRequest{
		TransferAmount: float64(payload.TransferAmount),
		OutBillNo:      resp.OutBillNo,
		TransferBillNo: resp.TransferBillNo,
		OpenID:         payload.OpenID,
		UserID:         payload.UserID,
	}

	err = pay.CreatePendingRecordsWhenRequestWithdrawal(req1)
	if err != nil {
		fmt.Println("CreatePendingRecordsWhenRequestWithdrawal failed:", err)

		return c.Status(http.StatusInternalServerError).
			JSON(fiber.Map{
				"code":    -1,
				"message": err.Error(),
			})
	}

	return c.Status(http.StatusOK).
		JSON(fiber.Map{
			"success": true,
			"code":    200,
			"data":    resp,
		})
}

// 商家转账回调通知	https://pay.weixin.qq.com/doc/v3/merchant/4012712115#2.2%E3%80%81%E5%AF%B9%E5%9B%9E%E8%B0%83%E9%80%9A%E7%9F%A5%E8%BF%9B%E8%A1%8C%E5%BA%94%E7%AD%94
// 支付成功回调通知 https://pay.weixin.qq.com/doc/v3/merchant/4012791861#2.2%E3%80%81%E5%AF%B9%E5%9B%9E%E8%B0%83%E9%80%9A%E7%9F%A5%E8%BF%9B%E8%A1%8C%E5%BA%94%E7%AD%94
// 退款成功回调通知 https://pay.weixin.qq.com/doc/v3/merchant/4012791865#2.2%E3%80%81%E5%AF%B9%E5%9B%9E%E8%B0%83%E9%80%9A%E7%9F%A5%E8%BF%9B%E8%A1%8C%E5%BA%94%E7%AD%94
func HandleNotifyCallback(c fiber.Ctx) error {
	// Log the raw request for debugging
	body := c.Body()
	prettyjson.Printf("Received notify callback raw body: %s\n", body)

	payload := new(notify.Request)
	if err := c.Bind().JSON(payload); err != nil {
		return err
	}

	payClient := pay.NewWechatPayClient()
	ctx := c.Context()

	req, err := adaptor.ConvertRequest(c, true)
	if err != nil {
		fmt.Printf("Failed to convert fiber request to http request: %v", err)
		return c.Status(http.StatusInternalServerError).
			JSON(fiber.Map{"code": "FAIL", "message": "Internal server error"})
	}

	// 3. Process the notification
	err = payClient.ParseNotifyCallbackMessage(ctx, payload, req)
	if err != nil {
		// return 500 if failed
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"code":    "FAIL",
			"message": "失败",
		})
	}

	// 4. return 200 or 204 if success
	return c.Status(http.StatusNoContent).JSON(fiber.Map{
		"code": "SUCCESS",
	})
}

type CreateTransferBillTestPayload struct {
	Amount float64 `json:"amount"`
}

// 确认收款页面 点取消.
func HandleCancelTransferBill(c fiber.Ctx) error {
	// cancel transfer bill
	payload := new(pay.CancelTransferBillPayload)
	if err := c.Bind().JSON(payload); err != nil {
		return err
	}

	err := pay.CancelTransferBill(c.Context(), payload)
	if err != nil {
		fmt.Println("pay.CancelTransferBill failed:", err)

		return c.Status(http.StatusInternalServerError).
			JSON(fiber.Map{
				"code":    -1,
				"message": err.Error(),
			})
	}

	return nil
}
