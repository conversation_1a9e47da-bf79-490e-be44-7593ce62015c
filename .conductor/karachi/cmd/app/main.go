package main

import (
	"context"
	_ "embed"
	"fmt"
	"io"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/bytedance/sonic"
	"github.com/cloudflare/tableflip"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gofiber/contrib/monitor"
	"github.com/gofiber/fiber/v3"
	"github.com/gofiber/fiber/v3/middleware/healthcheck"
	"github.com/gofiber/fiber/v3/middleware/logger"
	recoverer "github.com/gofiber/fiber/v3/middleware/recover"
	"github.com/gofiber/fiber/v3/middleware/requestid"

	"tandian-server/internal/config"
	"tandian-server/internal/pay"
	"tandian-server/internal/redis"
)

var (
	// Version will show the process start time to verify graceful restart.
	version        = "v0.0.2"
	pid            = os.Getpid()
	processStarted = time.Now()
)

// Global redsync instance (should be initialized in init or via dependency injection).
var (
	redsyncPool *redsync.Redsync
)

// InitRedsync initializes the redsync instance.
func InitRedsync() {
	redisClient := redis.GetClient()
	pool := goredis.NewPool(redisClient)
	redsyncPool = redsync.New(pool)
}

// //go:generate sh -c "printf %s $(git rev-parse HEAD) > commit.txt"
// //go:embed commit.txt
// var Commit string

func main() {
	// Create logs and tmp directories if they don't exist
	if err := os.MkdirAll("logs", 0o755); err != nil {
		log.Fatalf("failed to create log directory: %v", err)
	}

	if err := os.MkdirAll("tmp", 0o755); err != nil {
		log.Fatalf("failed to create tmp directory: %v", err)
	}

	// Set up file logging
	logFile, err := os.OpenFile("logs/app.log", os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		log.Fatalf("error opening log file: %v", err)
	}
	defer logFile.Close() // Ensure log file is closed properly

	// Log to both console and file
	mw := io.MultiWriter(os.Stdout, logFile)
	log.SetOutput(mw)

	config.LoadConfig()
	redis.Init()

	// Init redsync pool
	InitRedsync()

	// Initialize redsync pool for distributed locks
	pay.InitRedsync()

	// Log process info for tracking graceful restarts
	log.Printf("Starting server - PID: %d, Time: %s", pid, processStarted.Format("2006-01-02 15:04:05"))

	upg, err := tableflip.New(tableflip.Options{
		PIDFile:        "tmp/app.pid",
		UpgradeTimeout: 30 * time.Second,
	})
	if err != nil {
		panic(err)
	}
	defer upg.Stop()

	// // Handle SIGUSR1 for log rotation instead of SIGHUP
	// go func() {
	// 	sigUSR1 := make(chan os.Signal, 1)
	// 	signal.Notify(sigUSR1, syscall.SIGUSR1)
	// 	for range sigUSR1 {
	// 		log.Println("Received SIGUSR1. Reopening logs...")
	// 		// Here you could add log rotation logic if needed
	// 		log.Println("Log rotation completed")
	// 	}
	// }()

	// Handle SIGHUP for graceful upgrade (optional, can be removed if not needed)
	go func() {
		sigHUP := make(chan os.Signal, 1)
		signal.Notify(sigHUP, syscall.SIGHUP)

		for range sigHUP {
			log.Println("Received SIGHUP. Initiating graceful upgrade...")
			upg.Upgrade() //nolint:errcheck
		}
	}()

	// Handle SIGINT and SIGTERM for graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-quit
		log.Printf("Received signal: %v. Signaling main goroutine for graceful shutdown...", sig)
		// Close upg.Exit() to trigger the main goroutine's shutdown logic
		upg.Stop()
	}()

	// Listen must be called before Ready
	ln, err := upg.Listen("tcp", fmt.Sprintf(":%d", config.AppConfig.AppServerPort))
	if err != nil {
		panic(err)
	}
	defer ln.Close()

	app := fiber.New(fiber.Config{
		JSONEncoder: sonic.Marshal,
		JSONDecoder: sonic.Unmarshal,
	})

	// Add a logger middleware
	// app.Use(logger.New(logger.Config{
	// 	Output: mw,
	// }))

	app.Use(requestid.New())
	// app.Use(logger.New())
	// app.Use(logger.New(logger.Config{
	// 	Format: "[${ip}]:${port} ${status} - ${method} ${path}\n",
	// }))

	app.Use(logger.New(logger.Config{
		CustomTags: map[string]logger.LogFunc{
			"requestid": func(output logger.Buffer, c fiber.Ctx, data *logger.Data, extraParam string) (int, error) {
				return output.WriteString(requestid.FromContext(c))
			},
		},
		TimeFormat: "2006-01-02",
		TimeZone:   "Asia/Shanghai",

		// For more options, see the Config section
		// Use the custom tag ${requestid} as defined above.
		// Format: "${pid} ${requestid} ${status} - ${method} ${path}\n",
		Format: "[${time}] | ${requestid} | ${status} | ${latency} | ${method} ${path}\n",
	}))

	// Add a recover middleware
	app.Use(recoverer.New())

	// Provide a minimal config for liveness check
	app.Get(healthcheck.DefaultLivenessEndpoint, healthcheck.NewHealthChecker())

	// Provide a minimal config for readiness check
	app.Get(healthcheck.DefaultReadinessEndpoint, healthcheck.NewHealthChecker())

	// Provide a minimal config for startup check
	app.Get(healthcheck.DefaultStartupEndpoint, healthcheck.NewHealthChecker())

	// Initialize default config (Assign the middleware to /metrics)
	app.Get("/metrics", monitor.New())

	// Set UTF-8 encoding for all responses
	app.Use(func(c fiber.Ctx) error {
		c.Set(fiber.HeaderContentType, fiber.MIMEApplicationJSONCharsetUTF8)
		return c.Next()
	})

	// Middleware to handle HEAD requests for GET routes
	app.Use(func(c fiber.Ctx) error {
		if c.Method() == fiber.MethodHead {
			// Change method to GET for route matching
			c.Request().Header.SetMethod(fiber.MethodGet)
			// Process the request
			err := c.Next()
			// Clear the response body for HEAD requests
			c.Response().ResetBody()

			return err
		}

		return c.Next()
	})

	// Use the debug middleware only in development
	if config.AppConfig.AppEnv == "development" || config.AppConfig.AppEnv == "dev" {
		log.Println("Debug middleware enabled (development mode)")
		app.Use(DebugMiddleware)
	}

	app.Get("/version", func(c fiber.Ctx) error {
		// Show version with process info to verify graceful restart
		uptime := time.Since(processStarted).Round(time.Second)
		info := fmt.Sprintf("Version: %s | PID: %d | Started: %s | Uptime: %s",
			version,
			pid,
			processStarted.Format("15:04:05"),
			uptime)

		return c.SendString(info)
	})

	app.Get("/", func(c fiber.Ctx) error {
		return c.SendString("Welcome!")
	})

	SetupRoutes(app)

	go app.Listener(ln, fiber.ListenConfig{
		EnablePrefork: false, // Disable prefork in dev
	})

	// tableflip ready
	if err := upg.Ready(); err != nil {
		panic(err)
	}

	// Wait for either upg.Exit() (for upgrade) or quit (for termination) to close.
	log.Println("Waiting for upg.Exit() or quit signal...")
	<-upg.Exit()
	log.Println("upg.Exit() closed. Main process exiting.")

	// Make sure to set a deadline on exiting the process
	// after upg.Exit() is closed. No new upgrades can be
	// performed if the parent doesn't exit.
	// If the app doesn't exit within a reasonable time after shutdown, force exit.
	time.AfterFunc(30*time.Second, func() {
		log.Println("Graceful shutdown timed out, forcing exit.")
		os.Exit(1)
	})

	// Wait for connections to drain.
	app.ShutdownWithContext(context.Background())
}

func DebugMiddleware(c fiber.Ctx) error {
	// Only log in development mode - double check
	if config.AppConfig.AppEnv != "development" && config.AppConfig.AppEnv != "dev" {
		return c.Next()
	}

	// Print request headers (excluding sensitive ones)
	log.Println("=== Request Headers ===")

	for k, v := range c.GetReqHeaders() {
		// Skip sensitive headers
		if k == "Authorization" || k == "X-Api-Key" || k == "Cookie" {
			log.Printf("%s: [REDACTED]\n", k)
		} else {
			log.Printf("%s: %v\n", k, v)
		}
	}

	// Print request body (limit size and mask sensitive fields)
	body := c.Body()
	if len(body) > 0 && len(body) < 10000 { // Limit to 10KB
		log.Println("=== Request Body ===")
		// Limit body logging to first 500 chars
		if len(body) > 500 {
			log.Printf("Body (truncated): %s...\n", string(body[:500]))
		} else {
			log.Printf("Body: %s\n", string(body))
		}
	}

	return c.Next()
}
