package main

import (
	"log"

	jwtware "github.com/gofiber/contrib/jwt"
	"github.com/gofiber/fiber/v3"
	"github.com/golang-jwt/jwt/v5"

	"tandian-server/internal/config"
)

func SetupRoutes(app *fiber.App) {
	// // Provide a minimal config for liveness check
	// app.Get(healthcheck.LivenessEndpoint, healthcheck.New())
	// // Provide a minimal config for readiness check
	// app.Get(healthcheck.ReadinessEndpoint, healthcheck.New())
	// // Provide a minimal config for startup check
	// app.Get(healthcheck.StartupEndpoint, healthcheck.New())
	// // Provide a minimal config for check with custom endpoint
	// app.Get("/live", healthcheck.New())

	// app.Post("/wx/pre_transfer_with_authorization_notify_url", PreTransferWithAuthorizationNotifyUrl)
	// app.Post("/wx/authorization_notify_url", AuthorizationNotifyUrl)
	app.Post("/wx/notify_callback", HandleNotifyCallback) // 微信支付回调

	app.Post("/api/v1/wx/create_transfer_bill", HandleCreateTransferBill)
	app.Post("/api/v1/wx/cancel_transfer_bill", HandleCancelTransferBill)

	app.Post("/api/v1/wx/unified_order", HandleUnifiedOrder)
	app.Post("/api/v1/wx/close_order", HandleCloseOrder)
	app.Post("/api/v1/wx/refund_order", HandleRefundOrder)
	app.Post("/api/v1/wx/query_order", HandleQueryOrder)
	app.Post("/api/v1/wx/verify_order", HandleVerifyOrder)

	// app.Post("/api/v1/pay/prepay", HandlePrepay)

	app.Get("/api/v1/promotions/:id", HandleTest)

	app.Post("/api/v1/test_create_transfer_bill", HandleCreateTransferBillTest)

	// 商家券
	app.Post("/api/v1/coupon/notify_callback", HandleCouponsNotifyCallback)
	app.Post("/api/v1/coupon/create_coupon_stock", HandleCreateCoupons)
	app.Post("/api/v1/coupon/set_coupon_notify_url", HandleSetCouponNotifyCallbacks)
	app.Post("/api/v1/coupon/get_coupon_notify_url", HandleGetCouponNotifyCallbacks)
	app.Post("/api/v1/coupon/modify_coupon_budgets", HandleModifyCouponBudgets)
	app.Post("/api/v1/coupon/modify_stock_info", HandleModifyStockInfo)
	app.Get("/api/v1/coupon/query_stock", HandleQueryStock)
	app.Post("/api/v1/coupon/list_coupons_by_filter", HandleListCouponsByFilter)
	app.Post("/api/v1/coupon/send_coupon", HandleSendCoupon)
	app.Post("/api/v1/coupon/use_coupon", HandleUseCoupon)
	app.Post("/api/v1/coupon/query_coupon", HandleQueryCoupon)
	app.Post("/api/v1/coupon/return_coupon", HandleReturnCoupon)
	app.Post("/api/v1/coupon/deactivate_coupon", HandleDeactivateCoupon)
	// app.Post("/api/v1/query_stock_info", HandleQueryStockInfo)
	app.Post("/api/v1/coupon/associate_trade_info", HandleAssociateTradeInfo)
	app.Post("/api/v1/coupon/disassociate_trade_info", HandleDisassociateTradeInfo)

	app.Post("/api/v1/coupon/fetch_available_compaigns", HandleFetchAvailableCompaigns)
	app.Post("/api/v1/coupon/fetch_available_coupons_when_checkout", HandleFetchAvailableCouponsWhenCheckout)
	app.Post("/api/v1/coupon/fetch_available_coupons_in_coupons_center", HandleFetchAvailableCouponsInCouponsCenter)

	// // JWT Middleware
	// app.Use(jwtware.New(jwtware.Config{
	// 	SigningKey: jwtware.SigningKey{
	// 		Key: []byte("secret"),
	// 	},
	// }))

	// 邀请助力
	app.Get("/api/v1/help-campaign/can-initiate", HandleCanInitiateHelpCampaign)
	app.Post("/api/v1/help-campaign/can-assist", HandleCanAssistHelpCampaign)
	app.Post("/api/v1/help-campaign/assist", HandleAssistHelpCampaign)

	// // Protected routes - using app.Use() for authentication middleware
	// protectedRoutes := []string{
	// 	"/api/v1/help-campaign/latest",
	// 	"/api/v1/note/submit_note",
	// }

	// for _, route := range protectedRoutes {
	// 	app.Use(route, RequireAuthForMethod(fiber.MethodPost))
	// }

	// app.Post("/api/v1/help-campaign/latest", HandleGetLatestHelpCampaignDetail, AuthorizationRequired())
	app.Post("/api/v1/help-campaign/latest", HandleGetLatestHelpCampaignDetail)
	app.Get("/api/v1/help-campaign/:campaign_id", HandleGetHelpCampaignDetail)
	// app.Get("/api/v1/help-campaign/my-campaigns", HandleGetMyHelpCampaigns)
	// app.Get("/api/v1/help-campaign/my-assists", HandleGetMyHelpAssists)
	// app.Post("/api/v1/help-campaign/initiate", HandleInitiateHelpCampaign)

	app.Post("/api/v1/help-campaign/fetch_available_compaigns_after_zhuli", HandleFetchAvailableCompaignsAfterZhuli)
	app.Post(
		"/api/v1/help-campaign/fetch_available_compaigns_after_fqzl_completed",
		HandleFetchAvailableCompaignsAfterFqzlCompleted,
	)
	app.Post("/api/v1/help-campaign/update_compaign_status", HandleUpdateCampaignCompletedStatus)
	app.Post("/api/v1/help-campaign/update_help_record_status", HandleUpdateHelpRecordStatus)

	// 笔记
	app.Post("/api/v1/note/submit_note", HandleSubmitNote)
	// app.Get("/api/v1/note/:id", HandleGetNote)
	// app.Get("/api/v1/note", HandleListNotes)
	// app.Delete("/api/v1/note/:id", HandleDeleteNote)

	app.Get("/api/v1/geo/reverse", HandleReverseGeocode)
	app.Post("/api/v1/geo/reverse", HandleReverseGeocode)

	app.Get("/debug", func(c fiber.Ctx) error {
		// Print all request headers
		log.Println("Headers:")

		headers := ""
		for k, v := range c.GetReqHeaders() {
			log.Printf("%s: %v\n", k, v)
			headers += k + ": " + v[0] + "\n"
		}

		return c.SendString(headers)
	})

	app.Post("/jwt", func(c fiber.Ctx) error {
		// Extract JWT claims from context
		// token := c.Locals("jwt")
		// if token != nil {
		// 	// Type assert to *jwt.Token
		// 	if jwtToken, ok := token.(*jwt.Token); ok {
		// 		// Extract claims as MapClaims
		// 		if claims, ok := jwtToken.Claims.(jwt.MapClaims); ok {
		// 			prettyjson.Printf("claims: %s\n", claims)
		// 			log.Println("JWT token validated successfully")

		// 			// Access specific claims
		// 			if userID, ok := claims["user_id"].(string); ok {
		// 				log.Printf("JWT user_id: %s", userID)
		// 			}
		// 			if openid, ok := claims["openid"].(string); ok {
		// 				log.Printf("JWT openid: %s", openid)
		// 			}
		// 			if sub, ok := claims["sub"].(string); ok {
		// 				log.Printf("JWT subject: %s", sub)
		// 			}
		// 			// Log all claims for debugging
		// 			log.Printf("All JWT claims: %+v", claims)
		// 		}
		// 	}
		// }

		log.Println(c.Locals("user_id"))
		log.Println(c.Locals("openid"))

		return c.JSON(fiber.Map{
			"message": "JWT token validated successfully",
		})
	}, AuthorizationRequired())
}

// RequireAuthForMethod returns a middleware that requires JWT authentication for specific HTTP methods.
func RequireAuthForMethod(methods ...string) fiber.Handler {
	// Check if JWT secret is configured on startup
	if config.AppConfig.JWTSecret == "" {
		log.Fatal("JWT secret is not configured. Please set JWTSecret in config.yaml")
	}

	// Create the JWT middleware once
	jwtMiddleware := jwtware.New(jwtware.Config{
		SigningKey: jwtware.SigningKey{Key: []byte(config.AppConfig.JWTSecret)},
		ContextKey: "jwt",
		ErrorHandler: func(c fiber.Ctx, err error) error {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   "Unauthorized",
				"message": "Invalid or expired token",
			})
		},
	})

	// Return middleware that checks method and authorization
	return func(c fiber.Ctx) error {
		// Check if this method requires auth
		requiresAuth := false
		for _, method := range methods {
			if c.Method() == method {
				requiresAuth = true
				break
			}
		}

		// If method doesn't require auth, continue
		if !requiresAuth {
			return c.Next()
		}

		// Check for Authorization header
		authHeader := c.Get("Authorization")
		if authHeader == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   "Unauthorized",
				"message": "Missing authorization header",
			})
		}

		// Validate JWT token
		return jwtMiddleware(c)
	}
}

// AuthorizationRequired returns a JWT middleware for backward compatibility
// Use RequireAuthForMethod for new routes.
func AuthorizationRequired() fiber.Handler {
	if config.AppConfig.JWTSecret == "" {
		log.Fatal("JWT secret is not configured. Please set JWTSecret in config.yaml")
	}

	return jwtware.New(jwtware.Config{
		SigningKey: jwtware.SigningKey{Key: []byte(config.AppConfig.JWTSecret)},
		ContextKey: "jwt",
		ErrorHandler: func(c fiber.Ctx, err error) error {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   "Unauthorized",
				"message": "Invalid or expired token",
			})
		},
		SuccessHandler: func(c fiber.Ctx) error {
			// Extract JWT claims from context
			token := c.Locals("jwt")
			if token != nil {
				// Type assert to *jwt.Token
				if jwtToken, ok := token.(*jwt.Token); ok {
					// Extract claims as MapClaims
					if claims, ok := jwtToken.Claims.(jwt.MapClaims); ok {
						// Access specific claims
						if userID, ok := claims["user_id"].(string); ok {
							c.Locals("user_id", userID)
						}
						if openid, ok := claims["openid"].(string); ok {
							c.Locals("openid", openid)
						}
						// if sub, ok := claims["sub"].(string); ok {
						// 	log.Printf("JWT subject: %s", sub)
						// }
						// Log all claims for debugging
						// log.Printf("All JWT claims: %+v", claims)
					}
				}
			}
			return c.Next()
		},
	})
}
