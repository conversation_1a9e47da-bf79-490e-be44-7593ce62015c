package main

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/dfang/go-prettyjson"
	"github.com/go-redsync/redsync/v4"
	"github.com/gofiber/fiber/v3"
	"github.com/golang-jwt/jwt/v5"

	"tandian-server/internal/config"
	"tandian-server/internal/model"
	"tandian-server/internal/util/cloudbase"
)

// CanInitiateResponse represents the response for can-initiate endpoint.
type CanInitiateResponse struct {
	CanInitiate     bool                `json:"can_initiate"`
	DailyCount      int                 `json:"daily_count"`
	MaxDaily        int                 `json:"max_daily"`
	CurrentCampaign *model.HelpCampaign `json:"current_campaign,omitempty"`
}

// CanAssistResponse represents the response for can-assist endpoint.
type CanAssistResponse struct {
	CanAssist  bool   `json:"can_assist"`
	DailyCount int    `json:"daily_count"`
	MaxDaily   int    `json:"max_daily"`
	Message    string `json:"message,omitempty"`
}

// CanAssistRequest represents the request for checking if a user can assist.
type CanAssistRequest struct {
	CampaignID   string `json:"campaign_id"    validate:"required"`
	HelperUserID string `json:"helper_user_id" validate:"required"`
}

// InitiateCampaignRequest represents the request for initiating a campaign.
type InitiateCampaignRequest struct {
	UserID      string `json:"user_id"     validate:"required"`
	Title       string `json:"title"       validate:"required,min=1,max=100"`
	Description string `json:"description" validate:"max=500"`
}

// InitiateCampaignResponse represents the response for initiating a campaign.
type InitiateCampaignResponse struct {
	CampaignID string `json:"campaign_id"`
	ShareURL   string `json:"share_url"`
	ExpiresAt  int64  `json:"expires_at"`
}

// AssistCampaignRequest represents the request for assisting a campaign.
type AssistCampaignRequest struct {
	CampaignID    string `json:"campaign_id"     validate:"required"`
	InviterUserID string `json:"inviter_user_id" validate:"required"` // 可选：发起人ID（用于验证）
	HelperUserID  string `json:"helper_user_id"  validate:"required"` // 助力者ID
}

// AssistCampaignResponse represents the response for assisting a campaign.
type AssistCampaignResponse struct {
	Success           bool   `json:"success"`
	RewardAmount      int    `json:"reward_amount"`
	CurrentCount      int    `json:"current_count"`
	TargetCount       int    `json:"target_count"`
	CampaignComplete  bool   `json:"campaign_complete"`
	InitiatorNickname string `json:"initiator_nickname"`
	InitiatorAvatar   string `json:"initiator_avatar"`
	Progress          string `json:"progress"`
	Message           string `json:"message"`
}

// GetTodayCampaignDetailRequest represents the request for getting today's campaign detail.
type GetTodayCampaignDetailRequest struct {
	UserID string `json:"user_id" validate:"required"` // 必须：用户ID
}

// MyAssistsResponse represents the response for my assists.
type MyAssistsResponse struct {
	Assists  []AssistWithCampaign `json:"assists"`
	Total    int                  `json:"total"`
	Page     int                  `json:"page"`
	PageSize int                  `json:"page_size"`
}

// AssistWithCampaign represents an assist record with campaign info.
type AssistWithCampaign struct {
	ID           string `json:"id"`
	CampaignID   string `json:"campaign_id"`
	AssistantID  string `json:"assistant_id"`
	RewardAmount int    `json:"reward_amount"`
	CreatedAt    int64  `json:"created_at"`
	// Campaign info
	CampaignTitle     string                   `json:"campaign_title"`
	CampaignInitiator string                   `json:"campaign_initiator"`
	CampaignStatus    model.HelpCampaignStatus `json:"campaign_status"`
}

// MyCampaignsResponse represents the response for my campaigns.
type MyCampaignsResponse struct {
	Campaigns []model.HelpCampaign `json:"campaigns"`
	Total     int                  `json:"total"`
	Page      int                  `json:"page"`
	PageSize  int                  `json:"page_size"`
}

func HandleCanInitiateHelpCampaign(c fiber.Ctx) error {
	ctx := c.Context()

	userID := c.Query("user_id")
	if userID == "" {
		return ValidationErrorResponse(c, ErrMsgBadRequest, "user_id is required")
	}

	// Get today's start time
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()

	// Check today's initiation count
	countFilter := map[string]interface{}{
		"initiator_id": userID,
		"created_at": map[string]interface{}{
			"$gte": todayStart,
		},
	}

	countResult, err := cloudbase.Count(ctx, "help_campaigns", countFilter)
	if err != nil {
		return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, err)
	}

	dailyCount := int(countResult)
	canInitiate := dailyCount < model.MAX_DAILY_INITIATIONS

	// Check for current active campaign
	var currentCampaign *model.HelpCampaign
	if dailyCount > 0 {
		activeFilter := map[string]interface{}{
			"initiator_id": userID,
			"status":       string(model.HelpCampaignStatusActive),
		}

		campaignData, err := cloudbase.FindOne(ctx, "help_campaigns", activeFilter)
		if err == nil && campaignData != nil {
			currentCampaign = &model.HelpCampaign{}

			if id, ok := campaignData["_id"].(string); ok {
				currentCampaign.ID = id
			}
			if initiatorID, ok := campaignData["initiator_id"].(string); ok {
				currentCampaign.InitiatorID = initiatorID
			}
			// if title, ok := campaignData["title"].(string); ok {
			// 	currentCampaign.Title = title
			// }
			if targetCount, ok := campaignData["target_count"].(float64); ok {
				currentCampaign.TargetCount = int(targetCount)
			}
			if currentCount, ok := campaignData["current_count"].(float64); ok {
				currentCampaign.CurrentCount = int(currentCount)
			}
			if status, ok := campaignData["status"].(string); ok {
				currentCampaign.Status = model.HelpCampaignStatus(status)
			}
			// if shareURL, ok := campaignData["share_url"].(string); ok {
			// 	currentCampaign.ShareURL = shareURL
			// }
			if expiresAt, ok := campaignData["expires_at"].(float64); ok {
				currentCampaign.ExpiresAt = int64(expiresAt)
			}
			if createdAt, ok := campaignData["created_at"].(float64); ok {
				currentCampaign.CreatedAt = int64(createdAt)
			}
		}
	}

	response := CanInitiateResponse{
		CanInitiate:     canInitiate,
		DailyCount:      dailyCount,
		MaxDaily:        model.MAX_DAILY_INITIATIONS,
		CurrentCampaign: currentCampaign,
	}

	return SuccessResponse(c, "查询成功", response)
}

func HandleCanAssistHelpCampaign(c fiber.Ctx) error {
	ctx := c.Context()

	var req CanAssistRequest
	if err := c.Bind().JSON(&req); err != nil {
		return ValidationErrorResponse(c, ErrMsgBadRequest, "Invalid request format")
	}

	// Validate required fields
	if req.CampaignID == "" || req.HelperUserID == "" {
		return ValidationErrorResponse(c, ErrMsgValidation, "campaign_id and helper_user_id are required")
	}

	// Check if this specific campaign can be assisted
	var canAssistThisCampaign bool
	message := "可以助力"

	campaign, err := cloudbase.FindById(ctx, "help_campaigns", req.CampaignID)
	if err != nil || campaign == nil {
		canAssistThisCampaign = false
		response := CanAssistResponse{
			CanAssist: canAssistThisCampaign,
			Message:   "助力活动不存在",
		}
		return SuccessResponse(c, "查询成功", response)
	}

	// Check if user is trying to help their own campaign
	initiatorID, _ := campaign["initiator_id"].(string)
	if req.HelperUserID == initiatorID {
		canAssistThisCampaign = false
		response := CanAssistResponse{
			CanAssist: canAssistThisCampaign,
			Message:   "不能为自己的活动助力",
		}
		return SuccessResponse(c, "查询成功", response)
	}

	if model.IsCampaignExpired(ctx, req.CampaignID) {
		canAssistThisCampaign = false
		response := CanAssistResponse{
			CanAssist: canAssistThisCampaign,
			Message:   "本次助力任务已结束",
		}
		return SuccessResponse(c, "查询成功", response)
	}

	helpRecordCount := model.GetHelpRecordCountOfCampaign(ctx, req.CampaignID)
	if helpRecordCount == model.TARGET_HELP_COUNT {
		canAssistThisCampaign = false
		response := CanAssistResponse{
			CanAssist: canAssistThisCampaign,
			Message:   "本次助力任务已完成",
		}
		return SuccessResponse(c, "查询成功", response)
	}

	if model.IsAssistedCampaignToday(ctx, req.HelperUserID, req.CampaignID) {
		canAssistThisCampaign = false
		response := CanAssistResponse{
			CanAssist: canAssistThisCampaign,
			Message:   "今日已助力过了",
		}
		return SuccessResponse(c, "查询成功", response)
	}

	if model.IsMaxDailyAssistsReached(ctx, req.HelperUserID) {
		canAssistThisCampaign = false
		response := CanAssistResponse{
			CanAssist: canAssistThisCampaign,
			Message:   "今日助力次数已用完",
		}
		return SuccessResponse(c, "查询成功", response)
	}

	response := CanAssistResponse{
		CanAssist:  true,
		DailyCount: 1,
		MaxDaily:   model.MAX_DAILY_ASSISTS,
		Message:    message,
	}

	return SuccessResponse(c, "查询成功", response)
}

func HandleInitiateHelpCampaign(c fiber.Ctx) error {
	ctx := c.Context()

	var req InitiateCampaignRequest
	if err := c.Bind().JSON(&req); err != nil {
		return ValidationErrorResponse(c, ErrMsgBadRequest, "Invalid request format")
	}

	// Validate required fields
	if req.UserID == "" || req.Title == "" {
		return ValidationErrorResponse(c, ErrMsgValidation, "user_id and title are required")
	}

	// Check if user can initiate (reuse the logic)
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()

	countFilter := map[string]interface{}{
		"initiator_id": req.UserID,
		"created_at": map[string]interface{}{
			"$gte": todayStart,
		},
	}

	dailyCount, err := cloudbase.Count(ctx, "help_campaigns", countFilter)
	if err != nil {
		return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, err)
	}

	if int(dailyCount) >= model.MAX_DAILY_INITIATIONS {
		return ErrorResponse(c, http.StatusTooManyRequests, -2, "今日发起次数已达上限", nil)
	}

	// Check if user has an active campaign
	activeFilter := map[string]interface{}{
		"initiator_id": req.UserID,
		"status":       string(model.HelpCampaignStatusActive),
	}

	existingCampaign, _ := cloudbase.FindOne(ctx, "help_campaigns", activeFilter)
	if existingCampaign != nil {
		return ErrorResponse(c, http.StatusConflict, -3, "您已有进行中的助力活动", nil)
	}

	// Create new campaign
	expiresAt := now.Add(time.Duration(model.CAMPAIGN_DURATION) * time.Second).Unix()

	campaignData := map[string]interface{}{
		"initiator_id":  req.UserID,
		"title":         req.Title,
		"description":   req.Description,
		"target_count":  model.TARGET_HELP_COUNT,
		"current_count": 0,
		"status":        string(model.HelpCampaignStatusActive),
		"reward_amount": model.INITIATOR_REWARD,
		"expires_at":    expiresAt,
		"created_at":    now.Unix(),
		"updated_at":    now.Unix(),
	}

	result, err := cloudbase.Create(ctx, "help_campaigns", campaignData)
	if err != nil {
		return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, err)
	}

	campaignID := result["_id"].(string)
	shareURL := fmt.Sprintf("https://your-domain.com/help-campaign/%s", campaignID)

	// Update the campaign with the share URL
	updateData := map[string]interface{}{
		"share_url": shareURL,
	}

	_, err = cloudbase.UpdateById(ctx, "help_campaigns", campaignID, updateData)
	if err != nil {
		return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, err)
	}

	response := InitiateCampaignResponse{
		CampaignID: campaignID,
		ShareURL:   shareURL,
		ExpiresAt:  expiresAt,
	}

	return SuccessResponse(c, "助力活动创建成功", response)
}

// HandleAssistHelpCampaign 助力按钮.
func HandleAssistHelpCampaign(c fiber.Ctx) error {
	ctx := c.Context()

	var req AssistCampaignRequest
	if err := c.Bind().JSON(&req); err != nil {
		return ValidationErrorResponse(c, ErrMsgBadRequest, "Invalid request format")
	}

	// Validate required fields
	if req.CampaignID == "" || req.HelperUserID == "" {
		return ValidationErrorResponse(c, ErrMsgValidation, "campaign_id and helper_user_id are required")
	}

	// Check if user is trying to help their own campaign (if inviter is provided)
	if req.InviterUserID != "" && req.InviterUserID == req.HelperUserID {
		return ValidationErrorResponse(c, ErrMsgValidation, "自己不能给自己助力")
	}

	// Use redsync for atomic operations
	lockKey := fmt.Sprintf("campaign_lock:%s", req.CampaignID)
	mutex := redsyncPool.NewMutex(lockKey,
		redsync.WithExpiry(5*time.Second), // Lock expires after 5 seconds
		redsync.WithTries(3),              // Try 3 times to acquire lock
	)

	// Acquire lock
	if err := mutex.Lock(); err != nil {
		return err
	}

	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			// Log error but don't return it to avoid masking original error
			fmt.Printf("failed to release lock: %v\n", err)
		}
	}()

	// Pre-check: Check if user has reached daily assist limit (this is user-specific, not campaign-specific)
	if model.IsMaxDailyAssistsReached(ctx, req.HelperUserID) {
		return ErrorResponse(c, http.StatusOK, -2, "你的今日助力次数已达上限", nil)
	}

	// Pre-check: Check if user has already assisted this campaign today
	if model.IsAssistedCampaignToday(ctx, req.HelperUserID, req.CampaignID) {
		return ErrorResponse(c, http.StatusOK, -3, "今日已助力过了", nil)
	}

	// Get campaign details
	campaign, err := cloudbase.FindById(ctx, "help_campaigns", req.CampaignID)
	if err != nil || campaign == nil {
		return ErrorResponse(c, http.StatusOK, -3, "助力活动不存在", err)
	}

	// // Check if campaign is active with safe type assertion
	// status, _ := campaign["status"].(string)
	// if status != string(model.HelpCampaignStatusActive) {
	// }

	if model.IsCampaignExpired(ctx, req.CampaignID) {
		return ErrorResponse(c, http.StatusOK, -4, "助力活动已结束", nil)
	}

	// Check if user is trying to help their own campaign
	initiatorID, _ := campaign["initiator_id"].(string)
	if req.HelperUserID == initiatorID {
		return ErrorResponse(c, http.StatusBadRequest, -6, "不能为自己的活动助力", nil)
	}

	if model.IsCampaignFinished(ctx, req.CampaignID) {
		return ErrorResponse(c, http.StatusOK, -4, "该活动今日助力次数已满", nil)
	}

	// Create assist record
	assistData := map[string]interface{}{
		"campaign_id":     req.CampaignID,
		"helper_user_id":  req.HelperUserID,
		"inviter_user_id": initiatorID,
		"help_time":       time.Now().Unix() * 1000,
		"createdAt":       time.Now().Unix() * 1000,
		"reward_status":   0,
	}

	_, err = cloudbase.Create(ctx, "help_records", assistData)
	if err != nil {
		return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, err)
	}

	// Update campaign current count
	currentCount, _ := model.UpdateCurrentCountForCampaign(ctx, req.CampaignID)

	// Get initiator information
	var initiatorNickname, initiatorAvatar string
	if initiatorID != "" {
		userQuery := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"_id": map[string]interface{}{
						"$eq": initiatorID,
					},
				},
			},
		}
		userData, err := cloudbase.GetItem(ctx, "users", userQuery)
		if err == nil && len(userData) > 0 {
			// Try to get nickname and avatar with safe type assertions
			if nickname, ok := userData["nickname"].(string); ok {
				initiatorNickname = nickname
			} else if name, ok := userData["name"].(string); ok {
				initiatorNickname = name
			} else if username, ok := userData["username"].(string); ok {
				initiatorNickname = username
			} else {
				initiatorNickname = "用户" + initiatorID[:6]
			}

			if avatar, ok := userData["avatar"].(string); ok {
				initiatorAvatar = avatar
			} else if avatarUrl, ok := userData["avatarUrl"].(string); ok {
				initiatorAvatar = avatarUrl
			} else if avatar_url, ok := userData["avatar_url"].(string); ok {
				initiatorAvatar = avatar_url
			}
		}
	}

	// Format progress message
	progress := fmt.Sprintf("%d/%d", currentCount, model.TARGET_HELP_COUNT)

	var message string
	var campaignComplete bool
	if currentCount == model.TARGET_HELP_COUNT {
		progress = "已完成"
		campaignComplete = true
	}
	if campaignComplete {
		message = "恭喜！助力任务已完成"
	} else {
		remaining := model.TARGET_HELP_COUNT - currentCount
		message = fmt.Sprintf("助力成功！还需%d人助力即可完成任务", remaining)
	}

	response := AssistCampaignResponse{
		Success:           true,
		RewardAmount:      model.ASSISTANT_REWARD,
		CurrentCount:      currentCount,
		TargetCount:       model.TARGET_HELP_COUNT,
		CampaignComplete:  campaignComplete,
		InitiatorNickname: initiatorNickname,
		InitiatorAvatar:   initiatorAvatar,
		Progress:          progress,
		Message:           message,
	}

	return SuccessResponse(c, "助力成功", response)
}

// HandleGetLatestHelpCampaignDetail
// 邀请助力页面: 获取今日助力活动详情，如果没有则自动创建 (POST请求).
func HandleGetLatestHelpCampaignDetail(c fiber.Ctx) error {
	ctx := c.Context()

	// Extract JWT claims from context
	token := c.Locals("jwt")
	if token != nil {
		// Type assert to *jwt.Token
		if jwtToken, ok := token.(*jwt.Token); ok {
			// Extract claims as MapClaims
			if claims, ok := jwtToken.Claims.(jwt.MapClaims); ok {
				// Access specific claims
				if userID, ok := claims["user_id"].(string); ok {
					log.Printf("JWT user_id: %s", userID)
				}
				if openid, ok := claims["openid"].(string); ok {
					log.Printf("JWT openid: %s", openid)
				}
				if sub, ok := claims["sub"].(string); ok {
					log.Printf("JWT subject: %s", sub)
				}
				// Log all claims for debugging
				log.Printf("All JWT claims: %+v", claims)
			}
		}
	}

	var req GetTodayCampaignDetailRequest
	if err := c.Bind().JSON(&req); err != nil {
		return ValidationErrorResponse(c, ErrMsgBadRequest, "Invalid request format")
	}

	prettyjson.Printf("payload: %s\n", req)

	// 验证必须参数
	if req.UserID == "" {
		return ValidationErrorResponse(c, ErrMsgValidation, "user_id is required")
	}

	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix() * 1000

	// 查找今日活动
	todayFilter := map[string]interface{}{
		"filter": map[string]interface{}{
			"where": map[string]interface{}{
				"initiator_id": map[string]interface{}{
					"$eq": req.UserID,
				},
				"createdAt": map[string]interface{}{
					"$gte": todayStart,
				},
			},
		},
	}
	campaignData, err := cloudbase.GetItem(ctx, "help_campaigns", todayFilter)
	if err != nil {
		return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, err)
	}
	prettyjson.Printf("campaignData: %s\n", campaignData)

	// 如果没有找到今日活动，自动创建一个
	if len(campaignData) == 0 {
		// // 检查是否已达到今日创建上限
		// dailyCount, countErr := cloudbase.Count(ctx, "help_campaigns", todayFilter)
		// if countErr != nil {
		// 	return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, countErr)
		// }

		// if int(dailyCount) >= model.MAX_DAILY_INITIATIONS {
		// 	return ErrorResponse(c, http.StatusTooManyRequests, -2, "今日发起次数已达上限", nil)
		// }

		// 创建新的助力活动
		// expiresAt := now.Add(time.Duration(model.CAMPAIGN_DURATION) * time.Second).Unix()

		newCampaignData := map[string]interface{}{
			// "title":         "邀请好友助力",
			// "description":   "邀请好友为我助力，完成任务即可获得奖励",
			"initiator_id":  req.UserID,
			"target_count":  model.TARGET_HELP_COUNT,
			"current_count": 0,
			"status":        model.HelpCampaignStatusActive,
			// "reward_amount": model.INITIATOR_REWARD,
			// "expires_at":    expiresAt,
		}
		prettyjson.Print(newCampaignData)

		result, createErr := cloudbase.Create(ctx, "help_campaigns", newCampaignData)
		if createErr != nil {
			return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, createErr)
		}

		prettyjson.Print(result)

		newCampaignID, ok := result["_id"].(string)
		if !ok || newCampaignID == "" {
			return ErrorResponse(
				c,
				http.StatusInternalServerError,
				-1,
				"Failed to get campaign ID from created record",
				nil,
			)
		}

		// 重新查询创建的活动详情
		campaignData, err = cloudbase.FindById(ctx, "help_campaigns", newCampaignID)
		if err != nil || campaignData == nil {
			return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, err)
		}
	}

	// Convert to model with safe type assertions
	campaign := model.HelpCampaign{}

	if id, ok := campaignData["_id"].(string); ok {
		campaign.ID = id
	}

	if initiatorID, ok := campaignData["initiator_id"].(string); ok {
		campaign.InitiatorID = initiatorID
	}

	// if title, ok := campaignData["title"].(string); ok {
	// 	campaign.Title = title
	// }

	// if targetCount, ok := campaignData["target_count"].(float64); ok {
	// 	campaign.TargetCount = int(targetCount)
	// }

	if currentCount, ok := campaignData["current_count"].(float64); ok {
		campaign.CurrentCount = int(currentCount)
	}

	if status, ok := campaignData["status"].(string); ok {
		campaign.Status = model.HelpCampaignStatus(status)
	}

	if createdAt, ok := campaignData["createdAt"].(float64); ok {
		campaign.CreatedAt = int64(createdAt)
	}

	if updatedAt, ok := campaignData["updatedAt"].(float64); ok {
		campaign.UpdatedAt = int64(updatedAt)
	}

	if shareURL, ok := campaignData["share_url"].(string); ok {
		campaign.ShareURL = shareURL
	}

	campaign.TargetCount = model.TARGET_HELP_COUNT
	campaign.CurrentCount = model.GetCurrentCountOfCampaign(ctx, campaign.ID)
	campaign.TodayHelpedOthers = model.HasHelpedToday(ctx, req.UserID)

	log.Println("campaign.CurrentCount: ", campaign.CurrentCount)

	log.Printf("getHelpRecordsOfCampaign called: %s\n", campaign.ID)
	assistantInfos := model.GetHelpRecordsOfCampaign(ctx, campaign.ID)

	response := model.CampaignDetailResponse{
		Campaign:   campaign,
		Assistants: assistantInfos,
	}

	return SuccessResponse(c, "查询成功", response)
}

// HandleGetHelpCampaignDetail
// 助力页面: 根据campaign_id获取详情.
func HandleGetHelpCampaignDetail(c fiber.Ctx) error {
	ctx := c.Context()

	campaignID := c.Params("campaign_id")
	if campaignID == "" {
		return ValidationErrorResponse(c, ErrMsgBadRequest, "campaign_id is required")
	}
	viewerID := c.Query("user_id")
	if viewerID == "" {
		return ValidationErrorResponse(c, ErrMsgBadRequest, "user_id is required")
	}

	// Get campaign details
	campaignData, err := cloudbase.FindById(ctx, "help_campaigns", campaignID)
	if err != nil || campaignData == nil {
		return ErrorResponse(c, http.StatusNotFound, -1, "助力活动不存在", err)
	}
	prettyjson.Printf("campaignData: %s\n", campaignData)

	// Convert to model with safe type assertions
	campaign := model.HelpCampaign{}

	if id, ok := campaignData["_id"].(string); ok {
		campaign.ID = id
	}

	if initiatorID, ok := campaignData["initiator_id"].(string); ok {
		campaign.InitiatorID = initiatorID
	}

	// if title, ok := campaignData["title"].(string); ok {
	// 	campaign.Title = title
	// }

	// if targetCount, ok := campaignData["target_count"].(float64); ok {
	// 	campaign.TargetCount = int(targetCount)
	// }

	// if currentCount, ok := campaignData["current_count"].(float64); ok {
	// 	campaign.CurrentCount = int(currentCount)
	// }

	// if status, ok := campaignData["status"].(string); ok {
	// 	campaign.Status = model.HelpCampaignStatus(status)
	// }

	// if rewardAmount, ok := campaignData["reward_amount"].(float64); ok {
	// 	campaign.RewardAmount = int(rewardAmount)
	// }

	// if expiresAt, ok := campaignData["expires_at"].(float64); ok {
	// 	campaign.ExpiresAt = int64(expiresAt)
	// }

	// if createdAt, ok := campaignData["created_at"].(float64); ok {
	// 	campaign.CreatedAt = int64(createdAt)
	// }

	// if updatedAt, ok := campaignData["updated_at"].(float64); ok {
	// 	campaign.UpdatedAt = int64(updatedAt)
	// }

	// if description, ok := campaignData["description"].(string); ok {
	// 	campaign.Description = description
	// }

	// if shareURL, ok := campaignData["share_url"].(string); ok {
	// 	campaign.ShareURL = shareURL
	// }

	// Check if campaign is expired and update status if needed
	now := time.Now()
	if campaign.Status == model.HelpCampaignStatusActive && now.Unix() > campaign.ExpiresAt {
		updateData := map[string]interface{}{
			"status":     string(model.HelpCampaignStatusExpired),
			"updated_at": now.Unix(),
		}
		cloudbase.UpdateById(ctx, "help_campaigns", campaignID, updateData)
		campaign.Status = model.HelpCampaignStatusExpired
		campaign.UpdatedAt = now.Unix()
	}

	initiatorData, err := cloudbase.FindById(ctx, "users", campaign.InitiatorID)
	if err != nil {
		return ErrorResponse(c, http.StatusInternalServerError, -1, ErrMsgInternalServer, err)
	}
	prettyjson.Printf("initiatorData: %s\n", initiatorData)

	// Safe type assertions for avatar and nickname
	if avatar, ok := initiatorData["avatar"].(string); ok {
		campaign.InitiatorAvatar = avatar
	}
	if nickname, ok := initiatorData["nickname"].(string); ok {
		campaign.InitiatorNickname = nickname
	}

	myLatestHelpRecord := model.GetMyLatestHelpRecord(ctx, viewerID)
	if len(myLatestHelpRecord) > 0 {
		campaign.ViewerHasHelped = true
		rewardStatusFloat, ok := myLatestHelpRecord["reward_status"].(float64)
		if ok && rewardStatusFloat == 1 {
			campaign.ViewerRewardStatus = 1
		} else {
			campaign.ViewerRewardStatus = 0
		}
	} else {
		campaign.ViewerHasHelped = false
		campaign.ViewerRewardStatus = 0
	}

	campaign.TodayHelpedOthers = model.HasHelpedToday(ctx, viewerID)
	campaign.TargetCount = model.TARGET_HELP_COUNT
	campaign.CurrentCount = model.GetCurrentCountOfCampaign(ctx, campaign.ID)
	assistantInfos := model.GetHelpRecordsOfCampaign(ctx, campaign.ID)

	response := model.CampaignDetailResponse{
		Campaign:   campaign,
		Assistants: assistantInfos,
	}

	return SuccessResponse(c, "查询成功", response)
}

// HandleFetchAvailableCompaignsAfterZhuli 助力成功后发券.
func HandleFetchAvailableCompaignsAfterZhuli(c fiber.Ctx) error {
	ctx := c.Context()

	// Parse request body
	var payload struct {
		UserID string `json:"user_id"`
		OpenID string `json:"openid"`
		AppID  string `json:"appid"`
		KEY    string `json:"key"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	prettyjson.Print(payload)

	if payload.KEY == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "key is required",
		})
	}

	// Validate required fields
	if payload.UserID == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "user_id or openid is required",
		})
	}
	payload.AppID = config.AppConfig.WechatPay.AppID

	// 临时修复（因为前端没有传openid，已经加了，需要发版)
	if payload.OpenID == "" {
		userData, _ := model.GetUserInfo(ctx, payload.UserID)
		if len(userData) > 0 {
			payload.OpenID = userData["openid"].(string)
		}
	}

	prettyjson.Printf("/api/v1/help-campaign/fetch_available_compaigns_after_zhuli payload: %s\n", payload)

	stocksForZhuli, err := QueryCompaignsByKey(payload.KEY)
	prettyjson.Printf("coupons queried from database: %s\n", stocksForZhuli)

	if err != nil {
		return c.Status(http.StatusOK).JSON(fiber.Map{
			"code":    -1,
			"message": "查询可用探店券失败",
			"error":   err.Error(),
		})
	}

	// Build coupon list with generated out_request_no
	coupons := []fiber.Map{}
	for _, stockID := range stocksForZhuli {
		coupons = append(coupons, fiber.Map{
			"stock_id":       stockID,
			"out_request_no": _generate_out_request_no(stockID),
		})
	}

	// Prepare send coupon parameters for signature
	// According to the doc, when sending multiple coupons, parameters use index notation
	send_coupon_params := map[string]interface{}{
		"send_coupon_merchant": config.AppConfig.WechatPay.MchID,
	}

	// Add stock_id and out_request_no for each available stock with index
	if len(coupons) > 0 {
		for i := range coupons {
			send_coupon_params["stock_id"+strconv.Itoa(i)] = coupons[i]["stock_id"]
			send_coupon_params["out_request_no"+strconv.Itoa(i)] = coupons[i]["out_request_no"]
		}
	}

	// Log parameters for debugging
	log.Printf("Send coupon params for signature: %+v", send_coupon_params)

	// 计算签名
	sign, err := calcSign(send_coupon_params)
	if err != nil {
		return c.Status(http.StatusOK).JSON(fiber.Map{
			"code":    -1,
			"message": "获取可用券时签名失败",
		})
	}

	// 4. Return available coupons with plugin initialization data
	pluginData := fiber.Map{
		"coupons": coupons,
		"sign":    sign, // HMAC-SHA256 signature
	}

	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "获取可用券成功",
		"data":    pluginData,
	})
}

// HandleFetchAvailableCompaignsAfterFqzlCompleted 发起助力成功后发券.
func HandleFetchAvailableCompaignsAfterFqzlCompleted(c fiber.Ctx) error {
	ctx := c.Context()

	// Parse request body
	var payload struct {
		UserID string `json:"user_id"`
		OpenID string `json:"openid"`
		AppID  string `json:"appid"`
		KEY    string `json:"key"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	prettyjson.Print(payload)

	if payload.KEY == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "key is required",
		})
	}

	// Validate required fields
	if payload.UserID == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "user_id or openid is required",
		})
	}
	payload.AppID = config.AppConfig.WechatPay.AppID

	// 临时修复（因为前端没有传openid，已经加了，需要发版)
	if payload.OpenID == "" {
		userData, _ := model.GetUserInfo(ctx, payload.UserID)
		if len(userData) > 0 {
			payload.OpenID = userData["openid"].(string)
		}
	}

	prettyjson.Printf("/api/v1/help-campaign/fetch_available_compaigns_after_zhuli payload: %s\n", payload)

	stocksForZhuli, err := QueryCompaignsByKey(payload.KEY)
	prettyjson.Printf("coupons queried from database: %s\n", stocksForZhuli)

	if err != nil {
		return c.Status(http.StatusOK).JSON(fiber.Map{
			"code":    -1,
			"message": "查询可用探店券失败",
			"error":   err.Error(),
		})
	}

	// // Check which coupons the user has already received (only include available stocks)
	// availableCoupons := filterOutAlreayReceivedStocks(ctx, stocksForWelcomeNewUser, payload.OpenID, payload.AppID)
	// if len(availableCoupons) == 0 {
	// 	log.Printf("No available coupons for user %s (all stocks already received)", payload.OpenID)
	// 	return c.Status(http.StatusOK).JSON(fiber.Map{
	// 		"code":    -1,
	// 		"message": "无可领优惠券，也有可能已经领过了",
	// 	})
	// }

	// // Fallback to hardcoded values if no campaigns found
	// if len(couponsForUser) == 0 {
	// 	log.Printf("No campaigns found in database, using fallback stock IDs")
	// 	couponsForUser = []string{
	// 		"1219460000000014",
	// 		"1219460000000015",
	// 	}
	// }

	// Build coupon list with generated out_request_no
	coupons := []fiber.Map{}
	for _, stockID := range stocksForZhuli {
		coupons = append(coupons, fiber.Map{
			"stock_id":       stockID,
			"out_request_no": _generate_out_request_no(stockID),
		})
	}

	// Prepare send coupon parameters for signature
	// According to the doc, when sending multiple coupons, parameters use index notation
	send_coupon_params := map[string]interface{}{
		"send_coupon_merchant": config.AppConfig.WechatPay.MchID,
	}

	// Add stock_id and out_request_no for each available stock with index
	if len(coupons) > 0 {
		for i := range coupons {
			send_coupon_params["stock_id"+strconv.Itoa(i)] = coupons[i]["stock_id"]
			send_coupon_params["out_request_no"+strconv.Itoa(i)] = coupons[i]["out_request_no"]
		}
	}

	// Log parameters for debugging
	log.Printf("Send coupon params for signature: %+v", send_coupon_params)

	// 计算签名
	sign, err := calcSign(send_coupon_params)
	if err != nil {
		return c.Status(http.StatusOK).JSON(fiber.Map{
			"code":    -1,
			"message": "获取可用券时签名失败",
		})
	}

	// 4. Return available coupons with plugin initialization data
	pluginData := fiber.Map{
		"coupons": coupons,
		"sign":    sign, // HMAC-SHA256 signature
	}

	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "获取可用券成功",
		"data":    pluginData,
	})
}

// HandleUpdateCampaignCompletedStatus 助力进度完成后领取了商家券后更新reward_status.
func HandleUpdateCampaignCompletedStatus(c fiber.Ctx) error {
	ctx := c.Context()

	// Parse request body
	var payload struct {
		// UserID     string `json:"user_id"`
		// OpenID     string `json:"openid"`
		// AppID      string `json:"appid"`
		// KEY        string `json:"key"`
		CampaignID string `json:"campaign_id"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	prettyjson.Print(payload)

	if payload.CampaignID == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "campaign_id is required",
		})
	}

	// update help_campaigns status to completed
	updateCampaignData := map[string]interface{}{
		"status":        "completed",
		"reward_status": 1,
		"completedAt":   time.Now().Unix() * 1000,
	}

	_, err := cloudbase.UpdateById(ctx, "help_campaigns", payload.CampaignID, updateCampaignData)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "更新成功",
	})
}

// HandleUpdateHelpRecordStatus 助力成功后领取了商家券后更新reward_status.
func HandleUpdateHelpRecordStatus(c fiber.Ctx) error {
	ctx := c.Context()

	// Parse request body
	var payload struct {
		UserID string `json:"user_id"`
		// CampaignID string `json:"campaign_id"`
	}

	if err := c.Bind().JSON(&payload); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "请求参数解析失败",
			"error":   err.Error(),
		})
	}

	prettyjson.Print(payload)

	if payload.UserID == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "user_id and campaign_id are required",
		})
	}

	filter := map[string]interface{}{
		"helper_user_id": map[string]interface{}{
			"$eq": payload.UserID,
		},
		"reward_status": map[string]interface{}{
			"$eq": 0,
		},
		"createdAt": map[string]interface{}{
			"$gte": time.Now().AddDate(0, 0, -1).Unix() * 1000,
		},
	}

	record, err := cloudbase.FindOne(ctx, "help_records", filter)
	if err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "bad request",
		})
	}

	recordId, ok := record["_id"].(string)
	if !ok {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"code":    -1,
			"message": "bad request",
		})
	}

	// update help_records reward_status to 1
	updateCampaignData := map[string]interface{}{
		"reward_status": 1,
		"updatedAt":     time.Now().Unix() * 1000,
	}

	prettyjson.Print(updateCampaignData)

	result, err := cloudbase.UpdateById(ctx, "help_records", recordId, updateCampaignData)
	if err != nil {
		return err
	}
	prettyjson.Print(result)

	return c.Status(http.StatusOK).JSON(fiber.Map{
		"code":    200,
		"message": "更新成功",
	})
}
