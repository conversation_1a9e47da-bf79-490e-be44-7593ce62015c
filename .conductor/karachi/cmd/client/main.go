package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/hibiken/asynq"
	"go.uber.org/zap"
	"gopkg.in/yaml.v2"

	"tandian-server/internal/config"
	"tandian-server/internal/logger"
	"tandian-server/internal/redis"
	"tandian-server/internal/tasks"
)

func main() {
	config.LoadConfig()
	redis.Init()

	if err := logger.Init(config.AppConfig.AppEnv, config.AppConfig.Log.Level, config.AppConfig.Log.Modules); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	appLog := logger.Get("client")
	defer appLog.Sync()

	if err := run(appLog); err != nil {
		appLog.Error("Application failed", zap.Error(err))
		return
	}
}

func run(appLog *zap.Logger) error {
	redisOpt := asynq.RedisClientOpt{
		Addr:     config.AppConfig.Asynq.RedisAddr,
		Password: config.AppConfig.Asynq.RedisPassword,
		DB:       config.AppConfig.Asynq.RedisDB,
	}

	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		panic(err)
	}

	// https://github.com/hibiken/asynq/wiki/Dynamic-Periodic-Task
	if config.AppConfig.Asynq.UseDynamicTaskManager {
		appLog.Info("Using dynamic task manager for periodic tasks")

		provider := &FileBasedConfigProvider{filename: "./periodic_task_config.yml"}

		mgr, err := asynq.NewPeriodicTaskManager(
			asynq.PeriodicTaskManagerOpts{
				RedisConnOpt:               redisOpt,         // this is the redis connection option
				PeriodicTaskConfigProvider: provider,         // this provider object is the interface to your config source
				SyncInterval:               10 * time.Second, // this field specifies how often sync should happen
				SchedulerOpts: &asynq.SchedulerOpts{
					Location: loc, // this field specifies the time zone for the cron spec
				},
			})
		if err != nil {
			return fmt.Errorf("failed to create periodic task manager: %w", err)
		}

		if err := mgr.Run(); err != nil {
			return fmt.Errorf("failed to run periodic task manager: %w", err)
		}

		appLog.Info("Periodic task manager is running")
	} else {
		client := asynq.NewClient(redisOpt)
		defer client.Close()

		scheduler := asynq.NewScheduler(redisOpt, &asynq.SchedulerOpts{
			Location: loc,
		})
		// 自动刷新cloudbase token
		refreshCloudbaseTokenTask, err := tasks.NewRefreshCloudbaseTokenTask()
		if err != nil {
			appLog.Fatal("could not create task", zap.Error(err))
		}

		entryID, err := scheduler.Register("@every 10s", refreshCloudbaseTokenTask)
		if err != nil {
			appLog.Fatal("could not register task", zap.Error(err))
		}

		appLog.Info("registered an entry for refresh_cloudbase_access_token", zap.String("entry_id", entryID))

		// 自动刷新微信token
		refreshWechatTokenTask, err := tasks.NewRefreshWechatAccessTokenTask()
		if err != nil {
			appLog.Fatal("could not create task", zap.Error(err))
		}

		entryID2, err := scheduler.Register("@every 10s", refreshWechatTokenTask)
		if err != nil {
			appLog.Fatal("could not register task", zap.Error(err))
		}

		appLog.Info("registered an entry for refresh_wechat_access_token", zap.String("entry_id", entryID2))

		// 自动刷新wecom access token
		refreshWecomTokenTask, err := tasks.NewRefreshWecomAccessTokenTask()
		if err != nil {
			appLog.Fatal("could not create task", zap.Error(err))
		}

		entryID11, err := scheduler.Register("@every 10s", refreshWecomTokenTask)
		if err != nil {
			appLog.Fatal("could not register task", zap.Error(err))
		}

		appLog.Info("registered an entry for refresh_wecom_access_token", zap.String("entry_id", entryID11))

		// 发送笔记链接到企业微信群组
		sendNoteLinkTask, err := tasks.AutoEnqueueSendNoteLinkTask()
		if err != nil {
			appLog.Fatal("could not create task", zap.Error(err))
		}

		entryID5, err := scheduler.Register("@every 1m", sendNoteLinkTask)
		if err != nil {
			appLog.Fatal("could not register task", zap.Error(err))
		}

		appLog.Info("registered an entry for send_note_link", zap.String("entry_id", entryID5))

		// 自动通过预审核的笔记
		passNoteTask, err := tasks.AutoEnqueuePassNoteTask()
		if err != nil {
			appLog.Fatal("could not create task", zap.Error(err))
		}

		entryID3, err := scheduler.Register("@every 1h", passNoteTask)
		if err != nil {
			appLog.Fatal("could not register task", zap.Error(err))
		}

		appLog.Info("registered an entry for auto_pass_note", zap.String("entry_id", entryID3))

		// 自动刷新pending的withdrawals
		queryPendingWithdrawalTasks, err := tasks.AutoEnqueueQueryPendingWithdrawalsTask()
		if err != nil {
			appLog.Fatal("could not create task", zap.Error(err))
		}

		entryQueryPendingWithawal, err := scheduler.Register("@every 30s", queryPendingWithdrawalTasks)
		if err != nil {
			appLog.Fatal("could not register task", zap.Error(err))
		}

		appLog.Info("registered an entry for queryPendingWithdrawals", zap.String("entry_id", entryQueryPendingWithawal))

		// 自动刷新用户余额
		// enqueueRefreshAllUserMoneyTask()
		task4, err := tasks.NewAutoEnqueueRefreshUserMoneyTask()
		if err != nil {
			appLog.Fatal("could not create task", zap.Error(err))
		}

		entryID4, err := scheduler.Register("5 4 * * *", task4) // At 04:05.
		// entryID4, err := scheduler.Register("8 22 * * *", task4) // At 22:05.
		if err != nil {
			appLog.Fatal("could not register task", zap.Error(err))
		}

		appLog.Info("registered an entry for refreshUserMoney", zap.String("entry_id", entryID4))

		// fetchWecomGroups, err := tasks.NewFetchWecomGroupsTask()
		// if err != nil {
		// 	appLog.Fatal("could not create task", zap.Error(err))
		// }
		// entryID5, err = scheduler.Register("@every 3m", fetchWecomGroups)
		// if err != nil {
		// 	appLog.Fatal("could not register task", zap.Error(err))
		// }
		// appLog.Info("registered an entry for fetchWecomGroups", zap.String("entry_id", entryID5))

		if err := scheduler.Run(); err != nil {
			return fmt.Errorf("could not run scheduler: %w", err)
		}
	}
	return nil
}

func init() {
	// 	zap.ReplaceGlobals(zap.Must(zap.NewProduction()))
	logger := zap.Must(zap.NewProduction())
	if os.Getenv("APP_ENV") == "development" {
		logger = zap.Must(zap.NewDevelopment())
	}

	zap.ReplaceGlobals(logger)
}

// FileBasedConfigProvider implements asynq.PeriodicTaskConfigProvider interface.
type FileBasedConfigProvider struct {
	filename string
}

// Parses the yaml file and return a list of PeriodicTaskConfigs.
func (p *FileBasedConfigProvider) GetConfigs() ([]*asynq.PeriodicTaskConfig, error) {
	data, err := os.ReadFile(p.filename)
	if err != nil {
		return nil, err
	}

	var c PeriodicTaskConfigContainer
	if err := yaml.Unmarshal(data, &c); err != nil {
		return nil, err
	}

	var configs []*asynq.PeriodicTaskConfig

	for _, cfg := range c.Configs {
		// Build task options
		var opts []asynq.Option

		if cfg.Opts != nil {
			if cfg.Opts.Queue != "" {
				opts = append(opts, asynq.Queue(cfg.Opts.Queue))
			}

			if cfg.Opts.MaxRetry > 0 {
				opts = append(opts, asynq.MaxRetry(cfg.Opts.MaxRetry))
			}

			if cfg.Opts.Retention != "" {
				duration, err := time.ParseDuration(cfg.Opts.Retention)
				if err != nil {
					return nil, err
				}

				opts = append(opts, asynq.Retention(duration))
			}
		}

		configs = append(configs, &asynq.PeriodicTaskConfig{
			Cronspec: cfg.Cronspec,
			Task:     asynq.NewTask(cfg.TaskType, nil, opts...),
		})
	}

	return configs, nil
}

type PeriodicTaskConfigContainer struct {
	Configs []*Config `yaml:"configs"`
}

type Config struct {
	Cronspec string    `yaml:"cronspec"`
	TaskType string    `yaml:"task_type"`
	Opts     *TaskOpts `yaml:"opts"`
}

type TaskOpts struct {
	Queue     string `yaml:"queue"`
	MaxRetry  int    `yaml:"max_retry"`
	Retention string `yaml:"retention"` // duration string like "1h", "1d"
}
