package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"

	"github.com/dfang/go-prettyjson"
	"github.com/urfave/cli/v3"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/merchantexclusivecoupon"

	"tandian-server/internal/pay"
	"tandian-server/internal/util/cloudbase"
)

func deactivateCoupon(ctx context.Context, cmd *cli.Command) error {
	stockID := cmd.String("stock_id")
	couponCode := cmd.String("coupon_code")

	if stockID == "" || couponCode == "" {
		return errors.New("both stock_id and coupon_code are required")
	}

	request := merchantexclusivecoupon.DeactivateCouponRequest{
		StockId:    core.String(stockID),
		CouponCode: core.String(couponCode),
	}

	resp, result, err := pay.DeactivateCoupon(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to deactivate coupon: %w", err)
	}

	if result != nil && result.Response != nil {
		log.Printf("API Response Status: %d", result.Response.StatusCode)
	}

	if resp != nil {
		fmt.Printf("Coupon deactivated successfully:\n")
		fmt.Printf("  Stock ID: %s\n", stockID)
		fmt.Printf("  Coupon Code: %s\n", couponCode)

		if resp.WechatpayDeactivateTime != nil {
			fmt.Printf("  Deactivation Time: %s\n", *resp.WechatpayDeactivateTime)
		}
	}

	return nil
}

func queryCoupon(ctx context.Context, cmd *cli.Command) error {
	couponCode := cmd.String("coupon_code")
	openid := cmd.String("openid")

	if couponCode == "" || openid == "" {
		return errors.New("both coupon_code and openid are required")
	}

	request := merchantexclusivecoupon.QueryCouponRequest{
		CouponCode: core.String(couponCode),
		Openid:     core.String(openid),
	}

	resp, result, err := pay.QueryCoupon(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to query coupon: %w", err)
	}

	if result != nil && result.Response != nil {
		log.Printf("API Response Status: %d", result.Response.StatusCode)
	}

	if resp != nil {
		fmt.Printf("Coupon Details:\n")
		fmt.Printf("  Coupon Code: %s\n", getStringValue(resp.CouponCode))
		fmt.Printf("  Stock ID: %s\n", getStringValue(resp.StockId))
		fmt.Printf("  Stock Name: %s\n", getStringValue(resp.StockName))

		if resp.CouponState != nil {
			fmt.Printf("  Status: %s\n", string(*resp.CouponState))
		}

		if resp.AvailableStartTime != nil {
			fmt.Printf("  Available Start Time: %s\n", *resp.AvailableStartTime)
		}

		if resp.ExpireTime != nil {
			fmt.Printf("  Expire Time: %s\n", *resp.ExpireTime)
		}

		if resp.ReceiveTime != nil {
			fmt.Printf("  Receive Time: %s\n", *resp.ReceiveTime)
		}

		if resp.UseTime != nil {
			fmt.Printf("  Use Time: %s\n", *resp.UseTime)
		}

		// Pretty print the full response as JSON
		if jsonData, err := json.MarshalIndent(resp, "", "  "); err == nil {
			fmt.Printf("\nFull Response (JSON):\n%s\n", string(jsonData))
		}
	}

	return nil
}

func queryStock(ctx context.Context, cmd *cli.Command) error {
	stockID := cmd.String("stock_id")

	if stockID == "" {
		return errors.New("stock_id is required")
	}

	request := merchantexclusivecoupon.QueryStockRequest{
		StockId: core.String(stockID),
	}

	resp, result, err := pay.QueryStock(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to query stock: %w", err)
	}

	if result != nil && result.Response != nil {
		log.Printf("API Response Status: %d", result.Response.StatusCode)
	}

	if resp != nil {
		fmt.Printf("Stock Details:\n")
		fmt.Printf("  Stock ID: %s\n", getStringValue(resp.StockId))
		fmt.Printf("  Stock Name: %s\n", getStringValue(resp.StockName))

		if resp.StockState != nil {
			fmt.Printf("  Status: %s\n", string(*resp.StockState))
		}

		fmt.Printf("  Comment: %s\n", getStringValue(resp.Comment))
		fmt.Printf("  Goods Name: %s\n", getStringValue(resp.GoodsName))

		if resp.StockSendRule != nil {
			fmt.Printf("\n  Stock Send Rule:\n")

			if resp.StockSendRule.MaxCoupons != nil {
				fmt.Printf("    Max Coupons: %d\n", *resp.StockSendRule.MaxCoupons)
			}

			if resp.StockSendRule.MaxCouponsPerUser != nil {
				fmt.Printf("    Max Coupons Per User: %d\n", *resp.StockSendRule.MaxCouponsPerUser)
			}

			if resp.StockSendRule.MaxAmount != nil {
				fmt.Printf("    Max Amount: %d\n", *resp.StockSendRule.MaxAmount)
			}
		}

		if resp.CouponUseRule != nil && resp.CouponUseRule.CouponAvailableTime != nil {
			if resp.CouponUseRule.CouponAvailableTime.AvailableBeginTime != nil {
				fmt.Printf("  Available Begin Time: %s\n", *resp.CouponUseRule.CouponAvailableTime.AvailableBeginTime)
			}

			if resp.CouponUseRule.CouponAvailableTime.AvailableEndTime != nil {
				fmt.Printf("  Available End Time: %s\n", *resp.CouponUseRule.CouponAvailableTime.AvailableEndTime)
			}
		}

		if resp.SendCountInformation != nil {
			if resp.SendCountInformation.TotalSendNum != nil {
				fmt.Printf("  Total Sent: %d\n", *resp.SendCountInformation.TotalSendNum)
			}

			if resp.SendCountInformation.TotalSendAmount != nil {
				fmt.Printf("  Total Send Amount: %d\n", *resp.SendCountInformation.TotalSendAmount)
			}
		}

		// Pretty print the full response as JSON
		prettyjson.Printf("\nFull Response (JSON):\n%s\n", resp)
	}

	return nil
}

func useCoupon(ctx context.Context, cmd *cli.Command) error {
	stockID := cmd.String("stock_id")
	couponCode := cmd.String("coupon_code")
	openid := cmd.String("openid")

	if stockID == "" || couponCode == "" || openid == "" {
		return errors.New("stock_id, coupon_code, and openid are all required")
	}

	request := merchantexclusivecoupon.UseCouponRequest{
		StockId:    core.String(stockID),
		CouponCode: core.String(couponCode),
		Openid:     core.String(openid),
	}

	resp, result, err := pay.UseCoupon(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to use coupon: %w", err)
	}

	if result != nil && result.Response != nil {
		log.Printf("API Response Status: %d", result.Response.StatusCode)
	}

	if resp != nil {
		fmt.Printf("Coupon used successfully:\n")
		fmt.Printf("  Stock ID: %s\n", getStringValue(resp.StockId))
		fmt.Printf("  Openid: %s\n", getStringValue(resp.Openid))

		if resp.WechatpayUseTime != nil {
			fmt.Printf("  Use Time: %s\n", *resp.WechatpayUseTime)
		}
	}

	return nil
}

func returnCoupon(ctx context.Context, cmd *cli.Command) error {
	stockID := cmd.String("stock_id")
	couponCode := cmd.String("coupon_code")

	if stockID == "" || couponCode == "" {
		return errors.New("both stock_id and coupon_code are required")
	}

	request := merchantexclusivecoupon.ReturnCouponRequest{
		StockId:    core.String(stockID),
		CouponCode: core.String(couponCode),
	}

	resp, result, err := pay.ReturnCoupon(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to return coupon: %w", err)
	}

	if result != nil && result.Response != nil {
		log.Printf("API Response Status: %d", result.Response.StatusCode)
	}

	if resp != nil {
		fmt.Printf("Coupon returned successfully:\n")
		fmt.Printf("  Stock ID: %s\n", stockID)
		fmt.Printf("  Coupon Code: %s\n", couponCode)

		if resp.WechatpayReturnTime != nil {
			fmt.Printf("  Return Time: %s\n", *resp.WechatpayReturnTime)
		}
	}

	return nil
}

func listCoupons(ctx context.Context, cmd *cli.Command) error {
	openid := cmd.String("openid")
	userID := cmd.String("user_id")
	stockID := cmd.String("stock_id")
	state := cmd.String("state")
	limit := cmd.Int64("limit")
	offset := cmd.Int64("offset")

	// If user_id is provided but openid is not, query the database for openid
	if userID != "" && openid == "" {
		fmt.Printf("Looking up openid for user_id: %s\n", userID)

		// Query users collection to get openid from user_id
		query := map[string]interface{}{
			"filter": map[string]interface{}{
				"where": map[string]interface{}{
					"_id": map[string]interface{}{
						"$eq": userID,
					},
				},
			},
			"select": map[string]interface{}{
				"_id":    true,
				"openid": true,
			},
		}

		userData, err := cloudbase.GetItem(ctx, "users", query)
		if err != nil {
			return fmt.Errorf("failed to query user by user_id: %w", err)
		}

		if len(userData) == 0 {
			return fmt.Errorf("user not found with user_id: %s", userID)
		}

		// Extract openid from the result
		if userOpenid, ok := userData["openid"].(string); ok && userOpenid != "" {
			openid = userOpenid
			fmt.Printf("Found openid: %s for user_id: %s\n", openid, userID)
		} else {
			return fmt.Errorf("openid not found for user_id: %s", userID)
		}
	}

	// Ensure we have an openid at this point
	if openid == "" {
		return errors.New("either openid or user_id is required")
	}

	// Build request
	request := merchantexclusivecoupon.ListCouponsByFilterRequest{
		Openid: core.String(openid),
		Appid:  core.String(pay.GetAppID()), // Use the app ID from config
	}

	// Add optional filters
	if stockID != "" {
		request.StockId = core.String(stockID)
	}

	if state != "" {
		couponState := merchantexclusivecoupon.CouponStatus(state)
		request.CouponState = &couponState
	}

	if limit > 0 {
		request.Limit = core.Int64(limit)
	} else {
		request.Limit = core.Int64(20) // Default limit
	}

	if offset > 0 {
		request.Offset = core.Int64(offset)
	}

	// Call API
	resp, result, err := pay.ListCouponsByFilter(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to list coupons: %w", err)
	}

	if result != nil && result.Response != nil {
		log.Printf("API Response Status: %d", result.Response.StatusCode)
	}

	if resp != nil {
		fmt.Printf("Coupon List for User %s:\n", openid)
		fmt.Printf("Total Count: %d\n", getInt64Value(resp.TotalCount))
		fmt.Printf("Offset: %d\n", getInt64Value(resp.Offset))
		fmt.Printf("Limit: %d\n", getInt64Value(resp.Limit))
		fmt.Printf("\n")

		if len(resp.Data) > 0 {
			fmt.Printf("Coupons:\n")
			fmt.Printf("========\n")
			for i, coupon := range resp.Data {
				fmt.Printf("\n[%d] Coupon:\n", i+1)
				fmt.Printf("  Code: %s\n", getStringValue(coupon.CouponCode))
				fmt.Printf("  Stock ID: %s\n", getStringValue(coupon.StockId))
				fmt.Printf("  Stock Name: %s\n", getStringValue(coupon.StockName))

				if coupon.CouponState != nil {
					fmt.Printf("  State: %s\n", string(*coupon.CouponState))
				}

				if coupon.ReceiveTime != nil {
					fmt.Printf("  Receive Time: %s\n", *coupon.ReceiveTime)
				}

				if coupon.AvailableStartTime != nil {
					fmt.Printf("  Available From: %s\n", *coupon.AvailableStartTime)
				}

				if coupon.ExpireTime != nil {
					fmt.Printf("  Expires: %s\n", *coupon.ExpireTime)
				}

				if coupon.UseTime != nil {
					fmt.Printf("  Used At: %s\n", *coupon.UseTime)
				}

				// Display coupon rules if available
				if coupon.CouponUseRule != nil {
					if coupon.CouponUseRule.FixedNormalCoupon != nil {
						if coupon.CouponUseRule.FixedNormalCoupon.DiscountAmount != nil {
							fmt.Printf(
								"  Discount Amount: ¥%.2f\n",
								float64(*coupon.CouponUseRule.FixedNormalCoupon.DiscountAmount)/100,
							)
						}
						if coupon.CouponUseRule.FixedNormalCoupon.TransactionMinimum != nil {
							fmt.Printf(
								"  Min Purchase: ¥%.2f\n",
								float64(*coupon.CouponUseRule.FixedNormalCoupon.TransactionMinimum)/100,
							)
						}
					}
					if coupon.CouponUseRule.DiscountCoupon != nil {
						if coupon.CouponUseRule.DiscountCoupon.DiscountPercent != nil {
							fmt.Printf("  Discount: %d%%\n", *coupon.CouponUseRule.DiscountCoupon.DiscountPercent)
						}
						if coupon.CouponUseRule.DiscountCoupon.TransactionMinimum != nil {
							fmt.Printf(
								"  Min Purchase: ¥%.2f\n",
								float64(*coupon.CouponUseRule.DiscountCoupon.TransactionMinimum)/100,
							)
						}
					}
				}
			}
		} else {
			fmt.Println("No coupons found for this user.")
		}

		// Pretty print the full response as JSON
		if cmd.Bool("json") {
			fmt.Printf("\nFull Response (JSON):\n")
			if jsonData, err := json.MarshalIndent(resp, "", "  "); err == nil {
				fmt.Printf("%s\n", string(jsonData))
			}
		}
	}

	return nil
}

// Helper function to safely get string value from pointer.
func getStringValue(ptr *string) string {
	if ptr != nil {
		return *ptr
	}

	return "N/A"
}

// Helper function to safely get int64 value from pointer.
func getInt64Value(ptr *int64) int64 {
	if ptr != nil {
		return *ptr
	}
	return 0
}
