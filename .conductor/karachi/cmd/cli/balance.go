package main

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/urfave/cli/v3"

	"tandian-server/internal/pay"
)

func balanceCmd(ctx context.Context, cmd *cli.Command) error {
	accountType := strings.ToUpper(cmd.String("account-type"))
	if accountType != "BASIC" && accountType != "OPERATION" {
		return cli.Exit("--account-type 只能为 BASIC 或 OPERATION", 1)
	}

	payClient := pay.NewWechatPayClient()

	resp, err := payClient.Balance(ctx, accountType)
	if err != nil {
		return cli.Exit(fmt.Sprintf("查询余额失败: %v", err), 1)
	}

	data, err := json.MarshalIndent(resp, "", "  ")
	if err != nil {
		return cli.Exit(fmt.Sprintf("格式化响应失败: %v", err), 1)
	}

	fmt.Println(string(data))

	return nil
}
