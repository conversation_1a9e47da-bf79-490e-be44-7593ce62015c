package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/dfang/go-prettyjson"
	"github.com/urfave/cli/v3"

	"tandian-server/internal/pay"
)

func cancelTransferBill(ctx context.Context, cmd *cli.Command) error {
	outBillNo := cmd.String("out_bill_no")
	transferBillNo := cmd.String("transfer_bill_no")

	if (outBillNo == "" && transferBillNo == "") || (outBillNo != "" && transferBillNo != "") {
		return cli.Exit("Please provide either --out_bill_no or --transfer_bill_no", 1)
	}

	payClient := pay.NewWechatPayClient()

	var (
		resp *pay.CancelTransferBillResponse
		err  error
	)

	if outBillNo != "" {
		resp, err = payClient.CancelTransferBill(ctx, outBillNo)
	} else {
		resp, err = payClient.CancelTransferBillByTransferBillNo(ctx, transferBillNo)
	}

	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to cancel transfer bill: %v", err), 1)
	}

	jsonData, err := json.MarshalIndent(resp, "", "  ")
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to marshal response to JSON: %v", err), 1)
	}

	log.Println(string(jsonData))

	return nil
}

func queryTransferBill(ctx context.Context, cmd *cli.Command) error {
	outBillNo := cmd.String("out_bill_no")
	transferBillNo := cmd.String("transfer_bill_no")

	if (outBillNo == "" && transferBillNo == "") || (outBillNo != "" && transferBillNo != "") {
		return cli.Exit("Please provide either --out_bill_no or --transfer_bill_no", 1)
	}

	payClient := pay.NewWechatPayClient()

	var (
		transferBill *pay.TransferBillResponse
		err          error
	)

	if outBillNo != "" {
		transferBill, err = payClient.QueryTransferBillByOutBillNo(ctx, outBillNo)
	} else {
		transferBill, err = payClient.QueryTransferBillByTransferBillNo(ctx, transferBillNo)
	}

	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to query transfer bill: %v", err), 1)
	}

	prettyjson.Print(transferBill)

	user, err := pay.GetUserByOpenId(transferBill.Openid)
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to get user record: %v", err), 1)
	}

	prettyjson.Print(user)

	return nil
}
