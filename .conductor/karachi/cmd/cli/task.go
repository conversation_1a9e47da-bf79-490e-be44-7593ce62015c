package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/hibiken/asynq"
	"github.com/urfave/cli/v3"

	"tandian-server/internal/config"
	"tandian-server/internal/tasks"
)

func listTasks(ctx context.Context, cmd *cli.Command) error {
	taskTypes := []struct {
		Name        string
		Description string
	}{
		{"refresh_token", "Refresh system token"},
		{"refresh_wechat_token", "Refresh WeChat access token"},
		{"refresh_wecom_token", "Refresh WeCom access token"},
		{"refresh_user_money", "Refresh user money (requires --param user_id)"},
		{"pass_note", "Process a note (requires --param note_id)"},
		{"send_note_link", "Send note link (requires --param note_id)"},
		{"update_user_note_traits", "Update user note traits in Flagsmith (requires --param user_id)"},
		{"auto_scan_notes", "Auto scan and approve pending notes based on user trust level"},
		{"auto_scan_cards", "Auto scan and approve pending cards based on user trust level"},
	}

	for _, t := range taskTypes {
		log.Printf("%s: %s", t.Name, t.Description)
	}

	return nil
}

func enqueueTask(ctx context.Context, cmd *cli.Command) error {
	taskType := cmd.String("type")
	param := cmd.String("param")
	enqueueNow := cmd.Bool("now")
	scheduleIn := cmd.Duration("in")

	// Default to now=true if no schedule is given
	if scheduleIn == 0 && !cmd.IsSet("now") {
		enqueueNow = true
	}

	fmt.Println("taskType:", taskType)
	fmt.Println("param:", param)
	fmt.Println("enqueueNow:", enqueueNow)
	fmt.Println("scheduleIn:", scheduleIn)

	client := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     config.AppConfig.Asynq.RedisAddr,
			Password: config.AppConfig.Asynq.RedisPassword,
			DB:       config.AppConfig.Asynq.RedisDB,
		},
	)
	defer client.Close()

	switch taskType {
	case "refresh_token":
		return enqueue(client, tasks.TypeRefreshToken, nil, enqueueNow, scheduleIn, "critical")
	case "refresh_wechat_token":
		return enqueue(client, tasks.TypeRefreshWechatAccessToken, nil, enqueueNow, scheduleIn, "critical")
	case "refresh_wecom_token":
		return enqueue(client, tasks.TypeRefreshWecomAccessToken, nil, enqueueNow, scheduleIn, "critical")
	case "refresh_all_user_money":
		return enqueue(client, tasks.TypeEnqueueRefreshUserMoney, nil, enqueueNow, scheduleIn, "daily")
	case "refresh_user_money":
		if param == "" {
			return cli.Exit("user_id parameter is required for refresh_user_money task", 1)
		}

		task, err := tasks.NewRefreshUserMoneyTask(param)
		if err != nil {
			return err
		}

		return enqueue(client, task.Type(), task.Payload(), enqueueNow, scheduleIn, "daily")
	case "pass_note":
		if param == "" {
			return cli.Exit("note_id parameter is required for pass_note task", 1)
		}

		task, err := tasks.NewAutoPassNoteTask(param)
		if err != nil {
			return err
		}

		return enqueue(client, task.Type(), task.Payload(), enqueueNow, scheduleIn, "note")
	case "send_note_link":
		if param == "" {
			return cli.Exit("note_id parameter is required for send_note_link task", 1)
		}

		task, err := tasks.NewAutoSendNoteLinkTask(param)
		if err != nil {
			return err
		}

		return enqueue(client, task.Type(), task.Payload(), enqueueNow, scheduleIn, "note")
	case "update_help_campaign_stats":
		task, err := tasks.NewUpdateHelpCampaignStatsTask()
		if err != nil {
			return err
		}

		return enqueue(client, task.Type(), task.Payload(), enqueueNow, scheduleIn, "daily")
	case "update_user_note_traits":
		if param == "" {
			return cli.Exit("user_id parameter is required for update_user_note_traits task", 1)
		}

		task, err := tasks.NewUpdateUserNoteTraitsTask(param)
		if err != nil {
			return err
		}

		return enqueue(client, task.Type(), task.Payload(), enqueueNow, scheduleIn, "default")
	case "auto_scan_notes":
		task, err := tasks.NewAutoScanNotesTask()
		if err != nil {
			return err
		}

		return enqueue(client, task.Type(), task.Payload(), enqueueNow, scheduleIn, "note")
	case "auto_scan_cards":
		task, err := tasks.NewAutoScanCardsTask()
		if err != nil {
			return err
		}

		return enqueue(client, task.Type(), task.Payload(), enqueueNow, scheduleIn, "card")
	default:
		return cli.Exit("unknown task type", 1)
	}
}

func enqueue(
	client *asynq.Client,
	taskType string,
	payload []byte,
	enqueueNow bool,
	scheduleIn time.Duration,
	queue string,
) error {
	var (
		task *asynq.Task
		info *asynq.TaskInfo
		err  error
	)

	if payload != nil {
		task = asynq.NewTask(taskType, payload)
	} else {
		task = asynq.NewTask(taskType, nil)
	}

	opts := []asynq.Option{
		asynq.Queue(queue),
		asynq.Retention(time.Hour * 24 * 30),
	}

	//nolint:gocritic // if-else chain is clearer here than switch
	if enqueueNow {
		opts = append(opts, asynq.ProcessIn(time.Second))
	} else if scheduleIn > 0 {
		opts = append(opts, asynq.ProcessIn(scheduleIn))
	} else {
		opts = append(opts, asynq.ProcessIn(time.Second*5))
	}

	info, err = client.Enqueue(task, opts...)
	if err != nil {
		return err
	}

	log.Printf("Enqueued task: type=%s queue=%s id=%s", task.Type(), queue, info.ID)

	return nil
}
