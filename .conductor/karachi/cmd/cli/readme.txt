# README


```
go run cmd/cli/main.go queryTransferBill --transfer_bill_no 1330001886819462507150082894083230

go run cmd/cli/main.go queryTransferBill --out_bill_no T20250703160000900983

go run cmd/cli/main.go cancelTransferBill --transfer_bill_no 1330001886819462507150082894083230

```

```
下载运营账户的账单
bin/cli downloadBill --type fundflow --account-type OPERATION --date ********

bin/cli downloadBill --type fundflow --account-type OPERATION --bill-type SUCCESS --date ********
```


```
bin/cli enqueue -t refresh_all_user_money --in 10m
bin/cli enqueue -t refresh_user_money 
```