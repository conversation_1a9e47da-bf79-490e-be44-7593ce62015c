package main

import (
	"context"
	"fmt"
	"log"

	"github.com/urfave/cli/v3"

	"tandian-server/internal/pay"
)

func downloadBill(ctx context.Context, cmd *cli.Command) error {
	payClient := pay.NewWechatPayClient()

	url := cmd.String("url")
	outputDir := cmd.String("output-dir")

	if url != "" {
		log.Printf("Step 1: Downloading bill directly from url: %s", url)

		fileName, err := payClient.DownloadBill(ctx, url, "", outputDir)
		if err != nil {
			return cli.Exit(fmt.Sprintf("Failed to download bill: %v", err), 1)
		}

		log.Println("Bill downloaded.")
		fmt.Println(fileName)

		return nil
	}

	date := cmd.String("date")
	billType := cmd.String("type")
	billTypeParam := cmd.String("bill-type")
	accountTypeParam := cmd.String("account-type")

	// 自动格式化日期参数
	if len(date) == 8 {
		// 检查是否全为数字
		allDigit := true

		for _, c := range date {
			if c < '0' || c > '9' {
				allDigit = false
				break
			}
		}

		if allDigit {
			date = date[:4] + "-" + date[4:6] + "-" + date[6:]
		}
	}

	if date == "" || billType == "" {
		return cli.Exit("Either --url or both --date and --type must be provided.", 1)
	}

	// 参数强制校验
	switch billType {
	case "trade":
		if billTypeParam == "" {
			return cli.Exit("--bill-type is required when --type is 'trade'", 1)
		}
	case "fundflow":
		if accountTypeParam == "" {
			return cli.Exit("--account-type is required when --type is 'fundflow'", 1)
		}
	default:
		return cli.Exit(fmt.Sprintf("Invalid bill type '%s'. Must be 'trade' or 'fundflow'.", billType), 1)
	}

	var (
		billResp *pay.BillResponse
		err      error
	)

	log.Printf("Step 1: Requesting %s bill for date %s...", billType, date)

	switch billType {
	case "trade":
		params := pay.BillRequest{
			BillDate: date,
			BillType: billTypeParam,
		}
		billResp, err = payClient.TradeBill(ctx, params)
	case "fundflow":
		params := pay.BillRequest{
			BillDate:    date,
			AccountType: accountTypeParam,
		}
		billResp, err = payClient.FundFlowBill(ctx, params)
	}

	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to request bill: %v", err), 1)
	}

	if billResp == nil || billResp.DownloadURL == "" {
		return cli.Exit("Failed to get a valid download URL from the bill request.", 1)
	}

	log.Printf("Step 1 successful. Got download URL: %s", billResp.DownloadURL)
	log.Println("Step 2: Downloading bill content...")

	fileName, err := payClient.DownloadBill(ctx, billResp.DownloadURL, date, outputDir)
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to download bill: %v", err), 1)
	}

	log.Println("Step 2 successful. Bill downloaded.")
	fmt.Println(fileName)

	return nil
}
