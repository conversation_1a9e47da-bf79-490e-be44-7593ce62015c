package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/urfave/cli/v3"

	"tandian-server/internal/config"
	"tandian-server/internal/pay"
	"tandian-server/internal/redis"
)

func main() {
	// Load config
	config.LoadConfig(config.ConfigOptions{Silence: true})

	// Initialize Redis
	redis.Init()

	// Initialize redsync pool for distributed locks
	pay.InitRedsync()

	// Debug: print that we got here
	if os.Getenv("DEBUG_CLI") == "1" {
		log.Println("CLI initialized, creating app...")
	}

	app := &cli.Command{
		Name:    "tandian",
		Usage:   "Tandian task queue manager",
		Version: "1.0.0",
		Commands: []*cli.Command{
			{
				Name:  "task",
				Usage: "Manage task queue",
				Commands: []*cli.Command{
					{
						Name:    "list",
						Aliases: []string{"ls"},
						Usage:   "List available task types",
						Action:  listTasks,
					},
					{
						Name:  "enqueue",
						Usage: "Enqueue a task for processing",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "type",
								Aliases:  []string{"t"},
								Usage:    "Type of task to enqueue",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "param",
								Aliases: []string{"p"},
								Usage:   "Parameter for the task (e.g., user_id, note_id)",
							},
							&cli.BoolFlag{
								Name:  "now",
								Usage: "Process the task immediately",
								Value: false,
							},
							&cli.DurationFlag{
								Name:  "in",
								Usage: "Schedule the task after a duration (e.g., 5s, 10m, 1h)",
								Value: 0,
							},
						},
						Action: enqueueTask,
					},
				},
			},
			{
				Name:  "order",
				Usage: "Manage orders",
				Commands: []*cli.Command{
					{
						Name:  "query",
						Usage: "Query order by order_no or transaction_id",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:  "order_no",
								Usage: "The order number to query",
							},
							&cli.StringFlag{
								Name:  "transaction_id",
								Usage: "The transaction ID to query",
							},
						},
						Action: queryOrder,
					},
					{
						Name:  "refund",
						Usage: "Refund an order using any order identifier",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "order",
								Usage:    "Order identifier: order_no, out_trade_no, or transaction_id (required)",
								Required: true,
							},
							&cli.Int64Flag{
								Name:  "amount",
								Usage: "Refund amount in cents (if not provided, refunds full amount)",
							},
							&cli.Int64Flag{
								Name:  "total",
								Usage: "Total amount of the original order in cents (if not provided, queries from order)",
							},
							&cli.StringFlag{
								Name:  "reason",
								Usage: "Reason for refund",
								Value: "商品已下架",
							},
							&cli.BoolFlag{
								Name:  "force",
								Usage: "Force (skip tandian order type validation)",
								Value: false,
							},
						},
						Action: refundOrder,
					},
					{
						Name:  "verify",
						Usage: "Verify an order (mark as verified/used)",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "order_no",
								Usage:    "Order number (required)",
								Required: true,
							},
							&cli.StringFlag{
								Name:  "verify_method",
								Usage: "Verification method: 1=user instant verify, 2=merchant scan verify (default: 1)",
								Value: "1",
							},
						},
						Action: verifyOrder,
					},
				},
			},
			{
				Name:  "transfer-bill",
				Usage: "Manage transfer bills",
				Commands: []*cli.Command{
					{
						Name:  "query",
						Usage: "Query transfer bill by out_bill_no or transfer_bill_no",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:  "out_bill_no",
								Usage: "The out bill number to query",
							},
							&cli.StringFlag{
								Name:  "transfer_bill_no",
								Usage: "The transfer bill number to query",
							},
						},
						Action: queryTransferBill,
					},
					{
						Name:  "cancel",
						Usage: "Cancel transfer bill by out_bill_no or transfer_bill_no",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:  "out_bill_no",
								Usage: "The out bill number to cancel",
							},
							&cli.StringFlag{
								Name:  "transfer_bill_no",
								Usage: "The transfer bill number to cancel",
							},
						},
						Action: cancelTransferBill,
					},
				},
			},
			{
				Name:  "downloadBill",
				Usage: "Request and download a bill in a single step, or download by URL directly",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:  "url",
						Usage: "Bill download URL (if provided, will download directly)",
					},
					&cli.StringFlag{
						Name:  "date",
						Usage: "Bill date in YYYY-MM-DD format",
					},
					&cli.StringFlag{
						Name:  "type",
						Usage: "Type of bill to download: 'trade' or 'fundflow'",
					},
					&cli.StringFlag{
						Name:  "bill-type",
						Usage: "For trade bills: ALL, SUCCESS, or REFUND",
						Value: "ALL",
					},
					&cli.StringFlag{
						Name:  "account-type",
						Usage: "For fund flow bills: BASIC, OPERATION, or FEES",
						Value: "BASIC",
					},
					&cli.StringFlag{
						Name:  "output-dir",
						Usage: "Directory to save the downloaded bill (default: current directory)",
					},
				},
				Action: downloadBill,
			},
			{
				Name:  "balance",
				Usage: "查询微信商户余额，参数为 --account-type (BASIC 或 OPERATION)",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:  "account-type",
						Usage: "账户类型: basic 或 operation",
						Value: "basic",
					},
				},
				Action: balanceCmd,
			},
			{
				Name:  "coupon",
				Usage: "Manage coupons",
				Commands: []*cli.Command{
					{
						Name:  "queryCoupon",
						Usage: "Query a coupon by code and openid",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "coupon_code",
								Usage:    "Coupon code to query (required)",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "openid",
								Usage:    "User's openid (required)",
								Required: true,
							},
						},
						Action: queryCoupon,
					},
					{
						Name:  "queryStock",
						Usage: "Query stock details by stock ID",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "stock_id",
								Usage:    "Stock ID to query (required)",
								Required: true,
							},
						},
						Action: queryStock,
					},
					{
						Name:  "use",
						Usage: "Use/redeem a coupon",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "stock_id",
								Usage:    "Stock ID of the coupon (required)",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "coupon_code",
								Usage:    "Coupon code to use (required)",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "openid",
								Usage:    "User's openid (required)",
								Required: true,
							},
						},
						Action: useCoupon,
					},
					{
						Name:  "return",
						Usage: "Return a used coupon",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "stock_id",
								Usage:    "Stock ID of the coupon (required)",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "coupon_code",
								Usage:    "Coupon code to return (required)",
								Required: true,
							},
						},
						Action: returnCoupon,
					},
					{
						Name:  "deactivate",
						Usage: "Deactivate a coupon",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "stock_id",
								Usage:    "Stock ID of the coupon (required)",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "coupon_code",
								Usage:    "Coupon code to deactivate (required)",
								Required: true,
							},
						},
						Action: deactivateCoupon,
					},
					{
						Name:  "list",
						Usage: "List user's coupons by openid or user_id",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:  "openid",
								Usage: "User's openid (required if user_id not provided)",
							},
							&cli.StringFlag{
								Name:  "user_id",
								Usage: "User's ID in database (will query openid if provided)",
							},
							&cli.StringFlag{
								Name:  "stock_id",
								Usage: "Filter by stock ID (optional)",
							},
							&cli.StringFlag{
								Name:  "state",
								Usage: "Filter by coupon state: SENDED, USED, EXPIRED (optional)",
							},
							&cli.Int64Flag{
								Name:  "limit",
								Usage: "Number of results per page (default: 20)",
								Value: 20,
							},
							&cli.Int64Flag{
								Name:  "offset",
								Usage: "Offset for pagination (default: 0)",
								Value: 0,
							},
							&cli.BoolFlag{
								Name:  "json",
								Usage: "Output full response in JSON format",
							},
						},
						Action: listCoupons,
					},
				},
			},
		},
	}

	if os.Getenv("DEBUG_CLI") == "1" {
		log.Printf("Running app with args: %v\n", os.Args)
	}

	if err := app.Run(context.Background(), os.Args); err != nil {
		// Always print errors
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
