package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/urfave/cli/v3"

	"tandian-server/internal/pay"
)

const (
	// WeChat transaction ID typically starts with '4' and is 28-32 chars long.
	wechatTransactionIDMinLength = 28
	wechatTransactionIDPrefix    = '4'
)

func queryOrder(ctx context.Context, cmd *cli.Command) error {
	orderNo := cmd.String("order_no")
	transactionID := cmd.String("transaction_id")

	if (orderNo == "" && transactionID == "") || (orderNo != "" && transactionID != "") {
		return cli.Exit("Please provide either --order_no or --transaction_id", 1)
	}

	// Use the standalone function
	order, err := pay.QueryOrder(ctx, orderNo, transactionID)
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to query order: %v", err), 1)
	}

	jsonData, err := json.MarshalIndent(order, "", "  ")
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to marshal order to JSON: %v", err), 1)
	}

	log.Println(string(jsonData))

	return nil
}

func refundOrder(ctx context.Context, cmd *cli.Command) error {
	orderIdentifier := cmd.String("order")
	amount := cmd.Int64("amount")
	total := cmd.Int64("total")
	reason := cmd.String("reason")
	force := cmd.Bool("force")

	// Determine if the identifier is a transaction ID or an order number
	var orderNo, transactionID string
	if len(orderIdentifier) >= wechatTransactionIDMinLength && orderIdentifier[0] == wechatTransactionIDPrefix {
		// Likely a WeChat transaction ID
		transactionID = orderIdentifier
	} else {
		// Treat as order number (out_trade_no)
		orderNo = orderIdentifier
	}

	// If amount or total not provided, query the order first
	if amount == 0 || total == 0 {
		log.Printf("Querying order details to determine refund amounts...")

		// Use the standalone function
		order, err := pay.QueryOrder(ctx, orderNo, transactionID)
		if err != nil {
			return cli.Exit(fmt.Sprintf("Failed to query order: %v", err), 1)
		}

		// Set transaction ID if we only had order number
		if transactionID == "" && order.TransactionId != nil {
			transactionID = *order.TransactionId
		}

		// Set order number if we only had transaction ID
		if orderNo == "" && order.OutTradeNo != nil {
			orderNo = *order.OutTradeNo
		}

		// Use the order's total amount
		if total == 0 && order.Amount != nil && order.Amount.Total != nil {
			total = *order.Amount.Total
		}

		// If amount not specified, refund the full paid amount
		if amount == 0 && order.Amount != nil && order.Amount.PayerTotal != nil {
			amount = *order.Amount.PayerTotal
		}

		log.Printf("Order details retrieved. Transaction ID: %s, Order No: %s", transactionID, orderNo)
		log.Printf("Total: %.2f CNY, Refund amount: %.2f CNY", float64(total)/100, float64(amount)/100)
	}

	// Create refund request
	refundReq := pay.RefundRequest{
		OutTradeNo:    orderNo,
		TransactionId: transactionID,
		Amount:        amount,
		TotalAmount:   total,
		Reason:        reason,
		Force:         force,
	}

	log.Printf("Initiating refund...")
	log.Printf("Order identifier: %s", orderIdentifier)
	log.Printf("Refund Amount: %.2f CNY", float64(amount)/100)
	log.Printf("Total Amount: %.2f CNY", float64(total)/100)
	log.Printf("Reason: %s", reason)

	// Process refund using the standalone function
	resp, err := pay.RefundOrder(ctx, refundReq)
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to process refund: %v", err), 1)
	}

	// Display response
	log.Println("Refund initiated successfully!")

	// Format response data
	result := map[string]interface{}{
		"refund_id":      resp.RefundId,
		"out_refund_no":  resp.OutRefundNo,
		"transaction_id": resp.TransactionId,
		"out_trade_no":   resp.OutTradeNo,
		"status":         resp.Status,
		"create_time":    resp.CreateTime,
	}

	// Safely add amount details if they exist
	if resp.Amount != nil {
		amountData := map[string]interface{}{}
		if resp.Amount.Refund != nil {
			amountData["refund"] = *resp.Amount.Refund
		}

		if resp.Amount.Total != nil {
			amountData["total"] = *resp.Amount.Total
		}

		if resp.Amount.Currency != nil {
			amountData["currency"] = *resp.Amount.Currency
		}

		result["amount"] = amountData
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to marshal response to JSON: %v", err), 1)
	}

	fmt.Println(string(jsonData))

	return nil
}

func verifyOrder(ctx context.Context, cmd *cli.Command) error {
	orderNo := cmd.String("order_no")
	verifyMethod := cmd.String("verify_method")

	log.Printf("Verifying order: %s", orderNo)
	log.Printf("Verify method: %s (1=user instant, 2=merchant scan)", verifyMethod)

	// Use the standalone function to verify the order
	err := pay.VerifyOrder(ctx, orderNo, verifyMethod)
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to verify order: %v", err), 1)
	}

	log.Println("Order verified successfully!")

	// Display result
	verifyResult := map[string]interface{}{
		"order_no":      orderNo,
		"status":        "verified",
		"verify_method": verifyMethod,
		"verified_at":   time.Now().Format(time.RFC3339),
	}

	jsonData, err := json.MarshalIndent(verifyResult, "", "  ")
	if err != nil {
		return cli.Exit(fmt.Sprintf("Failed to marshal result to JSON: %v", err), 1)
	}

	fmt.Println(string(jsonData))

	return nil
}
