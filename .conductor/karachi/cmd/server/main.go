package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/hibiken/asynq"
	"github.com/hibiken/asynqmon"
	"go.uber.org/zap"

	"tandian-server/internal/config"
	"tandian-server/internal/flagsmith"
	"tandian-server/internal/logger"
	"tandian-server/internal/pay"
	"tandian-server/internal/redis"
	"tandian-server/internal/tasks"
)

func main() {
	config.LoadConfig()
	redis.Init()

	// Initialize redsync pool for distributed locks
	pay.InitRedsync()

	// Initialize logger
	if err := logger.Init(config.AppConfig.AppEnv, config.AppConfig.Log.Level, config.AppConfig.Log.Modules); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	appLog := logger.Get("server")
	defer appLog.Sync()

	// Replace global logger with the configured default logger
	zap.ReplaceGlobals(logger.Get("default"))

	// Initialize Flagsmith client if enabled
	if config.AppConfig.Flagsmith.Enabled {
		appLog.Info("Initializing Flagsmith client")
		err := flagsmith.InitializeFlagsmith(appLog)
		if err != nil {
			appLog.Error("Failed to initialize Flagsmith client", zap.Error(err))
			// Don't fail the server startup, just log the error
			// Tasks that require Flagsmith will handle the missing client gracefully
		} else {
			appLog.Info("Flagsmith client initialized successfully")
		}
	} else {
		appLog.Info("Flagsmith is disabled in configuration")
	}

	redisOpt := asynq.RedisClientOpt{
		Addr:     config.AppConfig.Asynq.RedisAddr,
		Password: config.AppConfig.Asynq.RedisPassword,
		DB:       config.AppConfig.Asynq.RedisDB,
	}
	srv := asynq.NewServer(
		redisOpt,
		asynq.Config{
			// Specify how many concurrent workers to use
			Concurrency: 5,
			// Optionally specify multiple queues with different priority.
			Queues: map[string]int{
				"critical": 6,
				"user":     3,
				"default":  1,
				"low":      1,
				"daily":    1,
				"note":     1,
				"card":     1,
				"order":    1,
				"coupon":   1,
			},
			StrictPriority:  true,
			ShutdownTimeout: 5 * time.Minute,
			// See the godoc for other configuration options
		},
	)

	// mux maps a type to a handler
	mux := asynq.NewServeMux()
	mux.HandleFunc(tasks.TypeRefreshToken, tasks.HandleRefreshCloudbaseTokenTask)
	mux.HandleFunc(tasks.TypeRefreshWechatAccessToken, tasks.HandleRefreshWechatAccessTokenTask)
	mux.HandleFunc(tasks.TypeRefreshWecomAccessToken, tasks.HandleRefreshWecomAccessTokenTask)

	// 定时关闭超时5分钟未支付的订单 (可在开发环境测试)
	mux.HandleFunc(tasks.TypeSchedulerQueryUnpaidOrders, tasks.HandleQueryUnpaidOrdersTask)
	mux.HandleFunc(tasks.TypeSchedulerCloseUnpaidOrder, tasks.HandleCloseUnpaidOrderTask)

	mux.HandleFunc(tasks.TypeRedeemCouponCode, tasks.HandleRedeemCouponCodeTask)
	mux.HandleFunc(tasks.TypeReturnCouponCode, tasks.HandleReturnCouponCodeTask)

	// Flagsmith user traits update task
	mux.HandleFunc(tasks.TypeUpdateUserNoteTraits, tasks.HandleUpdateUserNoteTraitsTask)

	// Auto scan and approve notes based on user trust level
	mux.HandleFunc(tasks.TypeAutoScanNotes, tasks.HandleAutoScanNotesTask)

	// Auto scan and approve cards based on user trust level
	mux.HandleFunc(tasks.TypeAutoScanCards, tasks.HandleAutoScanCardsTask)

	if config.AppConfig.AppEnv == "production" {
		// 自动取消超时未支付的订单
		mux.HandleFunc(
			tasks.TypeAutoEnqueueCancelOverduePaymentOrders,
			tasks.HandleAutoEnqueueCancelOverduePaymentOrdersTask,
		)
		mux.HandleFunc(tasks.TypeAutoCancelOverduePaymentOrder, tasks.HandleAutoCancelOverduePaymentOrderTask)

		// 定时查询转账单
		mux.HandleFunc(tasks.TypeEnqueueQueryTransferBill, tasks.HandleAutoEnqueueQueryPendingWithdrawalsTask)
		mux.HandleFunc(tasks.TypeQueryTransferBill, tasks.HandleQueryTransferBillTask)

		// 自动对账
		mux.HandleFunc(tasks.TypeEnqueueRefreshUserMoney, tasks.HandleAutoEnqueueRefreshUserMoneyTask)
		mux.HandleFunc(tasks.TypeRefreshUserMoney, tasks.HandleRefreshUserMoneyTask)

		// 自动发送通过预审的笔记链接
		mux.HandleFunc(tasks.TypeAutoEnqueueSendNoteLinkTask, tasks.HandleAutoEnqueueSendNoteLinkTask)
		mux.HandleFunc(tasks.TypeAutoEnqueuePassNoteTask, tasks.HandleAutoEnqueuePassNoteTask)
		mux.HandleFunc(tasks.TypeAutoSendNoteLink, tasks.HandleAutoSendNoteLinkTask)
		mux.HandleFunc(tasks.TypeAutoPassNote, tasks.HandleAutoPassNoteTask)

		mux.HandleFunc(tasks.TypeFetchWecomGroups, tasks.HandleFetchWecomGroupsTask)

		// 更新助力活动统计数据
		mux.HandleFunc(tasks.TypeUpdateHelpCampaignStats, tasks.HandleUpdateHelpCampaignStatsTask)
		mux.HandleFunc(tasks.TypeFetchWecomGroupDetail, tasks.HandleFetchWecomGroupDetailTask)
	}

	go func() {
		// Create a new ServeMux for the monitoring server
		httpMux := http.NewServeMux()

		h := asynqmon.New(asynqmon.Options{
			RootPath: "/monitoring", // RootPath specifies the root for asynqmon app
			RedisConnOpt: asynq.RedisClientOpt{
				Addr:     config.AppConfig.Asynq.RedisAddr,
				Password: config.AppConfig.Asynq.RedisPassword,
				DB:       config.AppConfig.Asynq.RedisDB,
			},
		})

		httpMux.Handle(h.RootPath()+"/", h)
		httpMux.HandleFunc("/api/v1/log/level", logger.LogLevelHandler())

		appLog.Info(
			"Monitoring server listening",
			zap.String("addr", fmt.Sprintf(":%d", config.AppConfig.MonitoringPort)),
		)

		if err := http.ListenAndServe(fmt.Sprintf(":%d", config.AppConfig.MonitoringPort), httpMux); err != nil {
			appLog.Fatal("could not run monitoring server", zap.Error(err))
		}
	}()

	appLog.Info("Starting asynq server")

	if err := srv.Run(mux); err != nil {
		appLog.Fatal("could not run server", zap.Error(err))
	}
}

func init() {
	// // 	zap.ReplaceGlobals(zap.Must(zap.NewProduction()))
	// logger := zap.Must(zap.NewProduction())
	// if os.Getenv("APP_ENV") == "development" {
	// 	logger = zap.Must(zap.NewDevelopment())
	// }

	// zap.ReplaceGlobals(logger)

	// Don't set up global logger here - it will be configured in main() with proper config
}
